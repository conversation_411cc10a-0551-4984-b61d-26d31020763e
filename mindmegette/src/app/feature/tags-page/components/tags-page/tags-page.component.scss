@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 32px;

  &::ng-deep {
    mindmegette-pager,
    mindmegette-pager .pager {
      margin-bottom: 0;
    }
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 32px;

    .tag-title {
      font-size: 40px;
      font-weight: 600;
      line-height: 48px;
      letter-spacing: -0.4px;
      color: var(--kui-gray-950);
    }

    .results {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        align-items: flex-start;
      }

      .sorter {
        @include media-breakpoint-down(md) {
          width: 100%;
        }

        app-search-result-sorter ::ng-deep {
          @include media-breakpoint-down(md) {
            flex-direction: column;
            width: 100%;
          }

          ng-select {
            width: 200px;

            @include media-breakpoint-down(md) {
              width: 100%;
            }
          }
        }
      }

      &-label {
        font-family: var(--kui-font-primary);
        font-size: 24px;
        font-weight: 600;
        line-height: 28px;
        letter-spacing: 0.12px;
        color: var(--kui-gray-950);

        @include media-breakpoint-down(sm) {
          font-size: 18px;
          line-height: 22px;
        }

        sup {
          font-weight: 400;
          font-size: 14px;

          @include media-breakpoint-down(sm) {
            font-size: 12px;
          }
        }
      }
    }
  }

  @include media-breakpoint-down(sm) {
    aside {
      margin-top: 32px;
    }
  }

  .article-card-wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 32px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: 1fr;
      grid-gap: 24px;
    }
  }

  ::ng-deep .external-recommendations {
    grid-gap: 32px !important;

    @include media-breakpoint-down(md) {
      grid-gap: 16px !important;
    }
  }
}
