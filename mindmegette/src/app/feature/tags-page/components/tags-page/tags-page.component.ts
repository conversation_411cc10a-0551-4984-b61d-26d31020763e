import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LayoutElementContentType,
  LimitableMeta,
  PAGE_TYPES,
  Tag,
} from '@trendency/kesma-ui';
import { Advertisement, AdvertisementsByMedium } from '@trendency/kesma-ui/lib/definitions';
import { Observable, Subject } from 'rxjs';
import { first, map, takeUntil, tap } from 'rxjs/operators';
import {
  AdvertisementMetaService,
  ArticleCardComponent,
  capitalize,
  ExternalRecommendationsComponent,
  getStructuredDataForRecipeList,
  MindmegetteArticleCardType,
  MindmegettePagerComponent,
  OverwritePriorityContentFlagsPipe,
  RecipeCardComponent,
  RecipeCardType,
  SearchResultSorterComponent,
  tagsPageMetaInfo,
  TitleWithPagePipe,
} from '../../../../shared';
import { environment } from '../../../../../environments/environment';
import { BackendSearchResult } from '../../api/tags-page.definitions';
import { backendSearchResultToArticleCard, backendSearchResultToRecipeCard } from '../../tags-page.utils';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { isDesktopAdShared } from '@shared/utils/advert.utils';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-tags-page',
  templateUrl: './tags-page.component.html',
  styleUrls: ['./tags-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    SearchResultSorterComponent,
    NgFor,
    AdvertisementAdoceanComponent,
    ExternalRecommendationsComponent,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    ArticleCardComponent,
    RecipeCardComponent,
    MindmegettePagerComponent,
    OverwritePriorityContentFlagsPipe,
    BreadcrumbComponent,
    TitleWithPagePipe,
  ],
})
export class TagsPageComponent implements OnInit, OnDestroy {
  readonly backendSearchResultToArticleCard = backendSearchResultToArticleCard;
  readonly backendSearchResultToRecipeCard = backendSearchResultToRecipeCard;
  readonly ArticleCardType = MindmegetteArticleCardType;
  readonly RecipeCardType = RecipeCardType;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  tag$: Observable<Tag> = this.route.data.pipe(
    map((res) => res?.['data']['tag']),
    tap((tag) => this.setPageMeta(tag))
  );
  searchResults$: Observable<BackendSearchResult[]> = this.route.data.pipe(
    map((res) => {
      return res['data']['data'];
    }),
    tap((data) => {
      const articles = data.map((searchResult: BackendSearchResult) => {
        return backendSearchResultToArticleCard(searchResult);
      });
      this.populateSidebarExcludedIds(articles);

      const recipes = data?.filter((article) => article.contentType === LayoutElementContentType.RECIPE);
      this.schemaService.removeStructuredData();

      if (recipes?.length) {
        this.schemaService.insertSchema(getStructuredDataForRecipeList(recipes as any, environment?.siteUrl ?? ''));
      }
    })
  );
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data'].limitable));

  sidebarExcludedIds: Array<string> = [];

  adverts?: AdvertisementsByMedium;
  box_1_tablet?: Advertisement;
  box_2_tablet?: Advertisement;
  showDesktopAd: boolean;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly schemaService: SchemaOrgService,
    private readonly adoMetaService: AdvertisementMetaService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.initAds();
    const tagSlug = this.route.snapshot?.params?.['tag'];
    this.adoMetaService.set([tagSlug]);
  }

  setPageMeta(tag: Tag): void {
    this.limitables$.pipe(first()).subscribe((res) => {
      const page = (res?.pageCurrent || 0) + 1;
      const canonical = createCanonicalUrlForPageablePage(`cimke/${tag.slug}`, this.route.snapshot, {
        skipAutoSlugSearch: true,
      });
      canonical && this.seo.updateCanonicalUrl(canonical);
      const metaData: IMetaData = tagsPageMetaInfo(capitalize(tag?.title ?? ''), page);
      this.seo.setMetaData(metaData);
    });
  }

  protected initAds(): void {
    this.resetAds();
    this.showDesktopAd = isDesktopAdShared(this.utilsService.isBrowser(), 1030);

    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, PAGE_TYPES.other_pages);

      this.box_1_tablet = { ...this.adverts?.mobile?.['mobilrectangle_1'], medium: 'desktop' };
      this.box_2_tablet = { ...this.adverts?.mobile?.['mobilrectangle_2'], medium: 'desktop' };

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  populateSidebarExcludedIds(articles: ArticleCard[]): void {
    this.sidebarExcludedIds = articles?.map((item) => item.id!) ?? [];
  }

  onChangeSort(sort: Record<string, string>): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...sort, page: null },
      queryParamsHandling: 'merge',
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
