import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Params, Router } from '@angular/router';
import { mergeMap, Observable, throwError } from 'rxjs';
import { IngredientPageService } from './ingredient-page.service';
import { ApiListResult, ApiResult, RecipeCard, RedirectService as KesmaRedirectService } from '@trendency/kesma-ui';
import { catchError, map, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { Ingredient, INGREDIENTS_PAGE_SIZE } from '../../shared';
import { getPageNum } from '../profile/profile-followed-users/profile-followed-users.resolver';

export interface IngredientRouteParams extends Params {
  ingredientSlug: string;
  previewHash?: string;
}

export type IngredientPageResolverData = Readonly<{
  ingredient: ApiResult<Ingredient>;
  recipes: ApiListResult<RecipeCard>;
}>;

@Injectable()
export class IngredientPageResolver {
  constructor(
    private readonly ingredientPageService: IngredientPageService,
    private readonly router: Router
  ) {}
  private readonly kesmaRedirectService = inject(KesmaRedirectService);

  public resolve(route: ActivatedRouteSnapshot): Observable<IngredientPageResolverData> {
    const params: IngredientRouteParams = route.params as IngredientRouteParams;

    const previewHash = params.previewHash;
    let ingredientSlug = params.ingredientSlug;
    const pageIndex = getPageNum(route.queryParams);

    const request$: Observable<ApiResult<Ingredient>> = previewHash
      ? this.ingredientPageService.getIngredientPreview(previewHash)
      : this.ingredientPageService.getIngredient(ingredientSlug);

    return request$.pipe(
      mergeMap((ingredient) => {
        ingredientSlug = ingredient.data?.slug ?? ingredientSlug;
        return this.ingredientPageService
          .getRecipesByIngredient(ingredientSlug, {
            ...route.queryParams,
            rowCount_limit: INGREDIENTS_PAGE_SIZE,
            page_limit: pageIndex,
          })
          .pipe(
            map((recipes) => {
              return {
                recipes,
                ingredient,
              };
            }),
            tap(({ recipes: { data } }) => {
              if (this.kesmaRedirectService.shouldBeRedirect(Number(pageIndex), data)) {
                this.kesmaRedirectService.redirectOldUrl(`hozzavalo/${ingredientSlug}`, false, 302);
              }
            })
          );
      }),
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(() => error);
      })
    ) as any;
  }
}
