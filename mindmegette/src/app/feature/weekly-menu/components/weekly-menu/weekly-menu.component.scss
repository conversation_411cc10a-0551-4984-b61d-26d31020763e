@use 'shared' as *;

:host {
  ::ng-deep {
    mindmegette-recipe-card {
      margin-bottom: 0 !important;
    }
  }

  .menu-container {
    margin-top: 40px;
    @include media-breakpoint-down(sm) {
      margin-top: 32px;
    }
  }

  h1 {
    color: var(--kui-gray-950);
    font-family: var(--kui-font-primary);
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 48px;
    letter-spacing: -0.4px;
    margin-bottom: 8px;
    @include media-breakpoint-down(sm) {
      font-size: 30px;
      line-height: 36px;
      letter-spacing: -0.3px;
    }
  }

  .date-interval {
    color: var(--kui-gray-600);
    font-family: var(--kui-font-secondary);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0.18px;
    margin-bottom: 24px;
    @include media-breakpoint-down(sm) {
      font-size: 14px;
      line-height: 20px;
    }
  }

  .days {
    margin-bottom: 40px;
    @include media-breakpoint-down(sm) {
      margin-bottom: 32px;
    }
  }

  .day-title {
    color: var(--kui-gray-950);
    font-family: var(--kui-font-primary);
    font-size: 30px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: -0.3px;
    margin-bottom: 16px;
  }

  .recipe-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
  }

  .recipe-card {
    width: calc(50% - 12px);
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  ::ng-deep .external-recommendations {
    margin-top: 32px;
  }

  aside {
    @include media-breakpoint-down(md) {
      margin-top: 32px;
    }
  }
}
