<section>
  <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { view: 'mobile' }"></ng-container>
  <div class="wrapper with-aside">
    <div class="left-column">
      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.['background'] as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
        }"
      ></kesma-advertisement-adocean>

      <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { view: 'desktop' }"></ng-container>
      <div *ngIf="selectionData?.data?.coverImage?.photographer || selectionData?.data?.coverImage?.source" class="photo">
        <ng-container *ngIf="selectionData?.data?.coverImage?.photographer"> FOTÓ: {{ selectionData?.data?.coverImage?.photographer }} </ng-container>
        <ng-container *ngIf="selectionData?.data?.coverImage?.photographer && selectionData?.data?.coverImage?.source"> / </ng-container>
        <ng-container *ngIf="selectionData?.data?.coverImage?.source">
          {{ selectionData?.data?.coverImage?.source }}
        </ng-container>
      </div>
      <h1 *ngIf="selectionData?.data?.title" class="title">
        {{ selectionData?.data?.title | titleWithPage: limitables?.pageCurrent }}
      </h1>
      <div *ngIf="selectionData?.data?.description" class="description">
        {{ selectionData?.data?.description }}
      </div>
      <div class="recipes first">
        @if (articles?.length > 0) {
          <ng-container *ngFor="let article of articles | slice: 0 : 6">
            <mindmegette-recipe-card
              *ngIf="!article?.column"
              [data]="selectionToRecipeCard(article)"
              [hasBackground]="true"
              [styleID]="RecipeCardType.TopImageLeftAlignedCard"
            >
            </mindmegette-recipe-card>
            <mindmegette-article-card
              *ngIf="article?.column"
              [data]="selectionToArticleCard(article)"
              [hasBackground]="true"
              [styleID]="ArticleCardType.TopImageLeftAlignedCard"
            >
            </mindmegette-article-card>
          </ng-container>
        }
      </div>
      <!-- AD -->
      <div *ngIf="articles?.[7]" class="ad">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </div>
      <!-- END AD -->

      <div *ngIf="articles && articles.length > 6" class="recipes">
        <ng-container *ngFor="let article of articles | slice: 6 : 12">
          <mindmegette-recipe-card
            *ngIf="!article?.column"
            [data]="selectionToRecipeCard(article)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-recipe-card>
          <mindmegette-article-card
            *ngIf="article?.column"
            [data]="selectionToArticleCard(article)"
            [hasBackground]="true"
            [styleID]="ArticleCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-article-card>
        </ng-container>
      </div>

      <!-- AD -->
      <div *ngIf="articles?.[13]" class="ad">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </div>
      <!-- END AD -->

      <div *ngIf="articles && articles.length > 12" class="recipes">
        <ng-container *ngFor="let article of articles | slice: 12 : 18">
          <mindmegette-recipe-card
            *ngIf="!article?.column"
            [data]="selectionToRecipeCard(article)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-recipe-card>
          <mindmegette-article-card
            *ngIf="article?.column"
            [data]="selectionToArticleCard(article)"
            [hasBackground]="true"
            [styleID]="ArticleCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-article-card>
        </ng-container>
      </div>

      <!-- AD -->
      <div *ngIf="articles?.[19]" class="ad">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_3'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['mobilrectangle_3'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </div>
      <!-- END AD -->

      <div *ngIf="articles && articles.length > 18" class="recipes">
        <ng-container *ngFor="let article of articles | slice: 18">
          <mindmegette-recipe-card
            *ngIf="!article?.column"
            [data]="selectionToRecipeCard(article)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-recipe-card>
          <mindmegette-article-card
            *ngIf="article?.column"
            [data]="selectionToArticleCard(article)"
            [hasBackground]="true"
            [styleID]="ArticleCardType.TopImageLeftAlignedCard"
          >
          </mindmegette-article-card>
        </ng-container>
      </div>

      <mindmegette-pager
        *ngIf="limitables && limitables.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [isCountPager]="false"
        [isListPager]="true"
        [maxDisplayedPages]="5"
        [rowAllCount]="limitables?.rowAllCount!"
        [rowOnPageCount]="limitables?.rowOnPageCount!"
      >
      </mindmegette-pager>

      <!-- AD -->
      <div *ngIf="limitables && limitables.pageMax! > 0" class="ad">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.['roadblock_4'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.['medium_rectangle_4_bottom'] as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
          }"
        ></kesma-advertisement-adocean>
      </div>
      <!-- END AD -->

      <app-external-recommendations></app-external-recommendations>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
<ng-template #thumbnailTemplate let-view="view">
  <img
    [alt]="selectionData?.data?.coverImage?.altText"
    [class]="view"
    [src]="selectionData?.data?.coverImage?.thumbnailUrl || '/assets/images/placeholder.jpg'"
    class="thumbnail"
    loading="eager"
    fetchpriority="high"
  />
</ng-template>
