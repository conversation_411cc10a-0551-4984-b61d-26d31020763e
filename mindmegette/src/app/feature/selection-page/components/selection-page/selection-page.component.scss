@use 'shared' as *;

:host {
  display: block;

  ::ng-deep {
    mindmegette-article-card,
    mindmegette-recipe-card {
      margin-bottom: 0px;
    }

    mindmegette-pager {
      .pager {
        margin: 80px 0;
      }
    }
  }

  .thumbnail {
    width: 100%;
    margin-bottom: 24px;
    aspect-ratio: 2 / 1;
    object-fit: cover;
    display: block;

    @include media-breakpoint-down(sm) {
      margin-bottom: 16px;
      aspect-ratio: 4 / 3;
    }

    @include media-breakpoint-up(md) {
      border-radius: 8px;
    }

    &.mobile {
      @include media-breakpoint-up(md) {
        display: none;
      }
    }

    &.desktop {
      @include media-breakpoint-down(sm) {
        display: none;
      }
    }
  }

  .photo {
    color: var(--kui-gray-400);
    font-family: var(--kui-font-secondary);
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.48px;
    text-transform: uppercase;
    margin-bottom: 24px;
    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }

  .title {
    color: var(--kui-gray-950);
    font-family: var(--kui-font-primary);
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 48px;
    letter-spacing: -0.4px;
    margin-bottom: 12px;
    @include media-breakpoint-down(md) {
      font-size: 24px;
      line-height: 28px;
      letter-spacing: 0.12px;
    }
  }

  .description {
    color: var(--kui-gray-950);
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.16px;
    @include media-breakpoint-down(md) {
      margin-bottom: 40px;
    }
  }

  .recipes {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-gap: 40px 32px;
    margin-bottom: 40px;

    &.first {
      margin-top: 60px;
    }

    @include media-breakpoint-down(md) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
      grid-gap: 20px 16px;
    }
  }
}
