import { ComponentRef } from '@angular/core';
import { LayoutElementContentType, LayoutPageType } from '@trendency/kesma-ui';

interface ILayoutConfig<ComponentType> {
  componentName: string;
  supportedContentTypes: LayoutElementContentType[];
  componentImport: () => Promise<ComponentType>;
  setProps?: (props: ILayoutComponentProps<ComponentType>) => void;
  showFirstItemOnly?: boolean;
  skipExtractorCheck?: boolean;
  extractedDataIsArray?: boolean;
  skipHydration?: boolean;
}

interface ILayoutComponentProps<ComponentType> {
  componentRef: ComponentRef<ComponentType>;
  layoutElement: any;
  desktopWidth: number;
  index: number;
  layoutPageType: LayoutPageType;
  isExperienceOccasionList: boolean;
  isGastroPage: boolean;
}

export const LAYOUT_RENDERER_CONFIG: ILayoutConfig<any>[] = [
  {
    componentName: 'ArticleCardComponent',
    supportedContentTypes: [LayoutElementContentType.Article, LayoutElementContentType.PrBlock],
    componentImport: () => import('@shared/components/article-card/article-card.component'),
    setProps: ({ componentRef, layoutElement, index, layoutPageType }): void => {
      componentRef.setInput('data', layoutElement?.extractorData?.[index]);
      componentRef.setInput('styleID', layoutElement.styleId);
      componentRef.setInput('hasBackground', layoutElement?.hasBackground || false);
      componentRef.setInput('isSidebar', layoutPageType === LayoutPageType.SIDEBAR);
      componentRef.setInput('isPrBlock', layoutElement.contentType === LayoutElementContentType.PrBlock);
    },
    extractedDataIsArray: true,
  },
  {
    componentName: 'RecipeCardComponent',
    supportedContentTypes: [LayoutElementContentType.RECIPE],
    componentImport: () => import('@shared/components/recipe-card/recipe-card.component'),
    setProps: ({ componentRef, layoutElement, index, desktopWidth, layoutPageType }): void => {
      componentRef.setInput('data', layoutElement?.extractorData?.[index]);
      componentRef.setInput('styleID', layoutElement.styleId);
      componentRef.setInput('desktopWidth', desktopWidth);
      componentRef.setInput('hasBackground', layoutElement.hasBackground);
      componentRef.setInput('isSidebar', layoutPageType === LayoutPageType.SIDEBAR);
    },
    extractedDataIsArray: true,
  },
  {
    componentName: 'MaestroCardComponent',
    supportedContentTypes: [LayoutElementContentType.AUTHOR],
    componentImport: () => import('@shared/components/maestro-card/maestro-card.component'),
    setProps: ({ componentRef, layoutElement, index }): void => {
      componentRef.setInput('data', layoutElement?.config?.selectedAuthors?.[index]);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'ArticleCardListComponent',
    supportedContentTypes: [LayoutElementContentType.ARTICLE_SLIDER],
    componentImport: () => import('@shared/components/article-card-list/article-card-list.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('isSidebar', layoutPageType === LayoutPageType.SIDEBAR);
    },
    showFirstItemOnly: true,
  },
  {
    componentName: 'TurpiBoxComponent',
    supportedContentTypes: [LayoutElementContentType.TURPI_BOX],
    componentImport: () => import('@shared/components/turpi-box/turpi-box.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
    },
    showFirstItemOnly: true,
  },
  {
    componentName: 'MindmegetteVotingComponent',
    supportedContentTypes: [LayoutElementContentType.Vote],
    componentImport: () => import('@shared/components/voting/mindmegette-voting.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('extractorData', layoutElement?.extractorData);
      componentRef.setInput('data', layoutElement?.extractorData?.data);
      componentRef.setInput('voteId', layoutElement?.extractorData?.votedId);
    },
  },
  {
    componentName: 'MindmegetteQuizComponent',
    supportedContentTypes: [LayoutElementContentType.Quiz],
    componentImport: () => import('@shared/components/quiz/mindmegette-quiz.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('styleID', layoutElement?.styleId);
    },
  },
  {
    componentName: 'TextBoxComponent',
    supportedContentTypes: [LayoutElementContentType.TEXT_BOX],
    componentImport: () => import('@shared/components/text-box/text-box.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.config?.text);
      componentRef.setInput('styleID', layoutElement.styleId);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'DailyTurpiComponent',
    supportedContentTypes: [LayoutElementContentType.TURPI_CARD],
    componentImport: () => import('@shared/components/daily-turpi/daily-turpi.component'),
    setProps: ({ componentRef, layoutElement, index }): void => {
      componentRef.setInput('data', layoutElement?.extractorData?.[index]);
    },
    extractedDataIsArray: true,
  },
  {
    componentName: 'LabelSelectWithRecipesComponent',
    supportedContentTypes: [LayoutElementContentType.RECIPE_CATEGORY_SELECT],
    componentImport: () => import('@shared/components/label-select-with-recipes/label-select-with-recipes.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData?.recipeCategories);
      componentRef.setInput('blockTitle', layoutElement?.extractorData?.blockTitle);
    },
  },
  {
    componentName: 'WeeklyMenuComponent',
    supportedContentTypes: [LayoutElementContentType.WEEKLY_MENU],
    componentImport: () => import('@shared/components/weekly-menu/weekly-menu.component'),
    setProps: ({ componentRef, desktopWidth }): void => {
      componentRef.setInput('isWeeklyComponent', true);
      componentRef.setInput('desktopWidth', desktopWidth);
      componentRef.setInput('blockTitle', 'Heti menü');
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'MaestroBoxComponent',
    supportedContentTypes: [LayoutElementContentType.MAESTRO_BOX],
    componentImport: () => import('@shared/components/maestro-box/maestro-box.component'),
    setProps: ({ componentRef, layoutElement, desktopWidth }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('desktopWidth', desktopWidth);
    },
  },
  {
    componentName: 'SortingBoxComponent',
    supportedContentTypes: [LayoutElementContentType.SELECTION],
    componentImport: () => import('@shared/components/sorting-box/sorting-box.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('styleID', layoutElement.styleId);
    },
  },
  {
    componentName: 'HighlightedSelectionAdapterComponent',
    supportedContentTypes: [LayoutElementContentType.HIGHLIGHTED_SELECTION],
    componentImport: () => import('@shared/components/highlighted-selection-adapter/highlighted-selection-adapter.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType }): void => {
      componentRef.setInput('highlightedSelection', layoutElement?.config?.selection);
      componentRef.setInput('loading', layoutPageType === LayoutPageType.HOME ? 'lazy' : 'eager');
    },
  },
  {
    componentName: 'DailyMenuAdapterComponent',
    supportedContentTypes: [LayoutElementContentType.DAILY_MENU],
    componentImport: () => import('@shared/components/daily-menu-adapter/daily-menu-adapter.component'),
    skipExtractorCheck: true,
  },
  {
    componentName: 'NewsletterComponent',
    supportedContentTypes: [LayoutElementContentType.NewsletterBlock],
    componentImport: () => import('@shared/components/newsletter/newsletter.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType, desktopWidth }): void => {
      componentRef.setInput('desktopWidth', layoutPageType === LayoutPageType.SIDEBAR ? 1 : desktopWidth);
      componentRef.setInput('styleID', layoutElement.styleId);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'MostViewedListComponent',
    supportedContentTypes: [LayoutElementContentType.MOST_VIEWED],
    componentImport: () => import('@shared/components/most-viewed-list/most-viewed-list.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
    },
  },
  {
    componentName: 'IngredientListComponent',
    supportedContentTypes: [LayoutElementContentType.INGREDIENT],
    componentImport: () => import('@shared/components/ingredient-list/ingredient-list.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType, desktopWidth }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('desktopWidth', layoutPageType === LayoutPageType.SIDEBAR ? 1 : desktopWidth);
    },
  },
  {
    componentName: 'GuaranteeBoxComponent',
    supportedContentTypes: [LayoutElementContentType.GUARANTEE_BOX],
    componentImport: () => import('@shared/components/guarantee-box/guarantee-box.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType, desktopWidth }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('desktopWidth', layoutPageType === LayoutPageType.SIDEBAR ? 1 : desktopWidth);
    },
  },
  {
    componentName: 'RecipeListComponent',
    supportedContentTypes: [LayoutElementContentType.RECIPE_SWIPER],
    componentImport: () => import('@shared/components/recipe-list/recipe-list.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType, isExperienceOccasionList, isGastroPage }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('styleID', layoutElement.styleId);
      componentRef.setInput('isSidebar', layoutPageType === LayoutPageType.SIDEBAR);
      componentRef.setInput('isExperienceOccasionList', isExperienceOccasionList);
      componentRef.setInput('isGastroPage', isGastroPage);
    },
  },
  {
    componentName: 'OfferListComponent',
    supportedContentTypes: [LayoutElementContentType.OFFER_BOX],
    componentImport: () => import('@shared/components/offer-list/offer-list.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('blockTitle', layoutElement?.blockTitle || layoutElement?.config?.blockTitle);
    },
  },
  {
    componentName: 'GastroExperienceComponent',
    supportedContentTypes: [LayoutElementContentType.EXPERIENCE_GIFT],
    componentImport: () => import('@feature/gastro/components/gastro-experience/gastro-experience.component'),
    skipExtractorCheck: true,
  },
  {
    componentName: 'GastroFeaturedExperienceRecommenderComponent',
    supportedContentTypes: [LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER],
    componentImport: () => import('@shared/components/gastro-featured-experience-recommender/gastro-featured-experience-recommender.component'),
    setProps: ({ componentRef, layoutElement, desktopWidth }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('styleId', layoutElement?.selectedType?.styleId);
      componentRef.setInput('desktopWidth', desktopWidth);
    },
  },
  {
    componentName: 'ExperienceRecommendationComponent',
    supportedContentTypes: [LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION],
    componentImport: () => import('@feature/gastro/components/experience-recommendation/experience-recommendation.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('styleID', layoutElement.styleId);
    },
  },
  {
    componentName: 'TopRankingGlossaryComponent',
    supportedContentTypes: [LayoutElementContentType.TOP_RANKING_GLOSSARY],
    componentImport: () => import('@shared/components/top-ranking-glossary/top-ranking-glossary.component'),
    skipExtractorCheck: true,
  },
  {
    componentName: 'GastroEventsCalendarProviderComponent',
    supportedContentTypes: [LayoutElementContentType.EVENT_CALENDAR],
    componentImport: () => import('@feature/gastro/components/gastro-events-calendar-provider/gastro-events-calendar-provider.component'),
    skipExtractorCheck: true,
  },
  {
    componentName: 'BrandingBoxComponent',
    supportedContentTypes: [LayoutElementContentType.BrandingBoxEx],
    componentImport: () => import('@shared/components/branding-box/branding-box.component'),
    setProps: ({ componentRef, layoutElement, desktopWidth }): void => {
      componentRef.setInput('desktopWidth', desktopWidth);
      componentRef.setInput('brand', layoutElement.brand);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'GastroExperienceOccasionCardComponent',
    supportedContentTypes: [LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION],
    componentImport: () => import('@shared/components/gastro-experience-occasion-card/gastro-experience-occasion-card.component'),
    setProps: ({ componentRef, layoutElement, index }): void => {
      componentRef.setInput('data', layoutElement?.extractorData?.[index]);
      componentRef.setInput('styleId', layoutElement.styleId);
    },
    extractedDataIsArray: true,
  },
  {
    componentName: 'GastroExperienceThematicRecommenderComponent',
    supportedContentTypes: [LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER],
    componentImport: () => import('@shared/components/gastro-experience-thematic-recommender/gastro-experience-thematic-recommender.component'),
    setProps: ({ componentRef, layoutElement, layoutPageType, desktopWidth }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
      componentRef.setInput('styleID', layoutElement.styleId);
      componentRef.setInput('canShowSponsor', layoutElement?.config?.autoFill?.filterExperienceCategories?.length !== 0);
      componentRef.setInput('desktopWidth', layoutPageType !== LayoutPageType.SIDEBAR ? desktopWidth : 3);
    },
  },
  {
    componentName: 'GastroExperienceOccasionSwiperComponent',
    supportedContentTypes: [LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER],
    componentImport: () => import('@feature/gastro/components/gastro-experience-occasion-swiper/gastro-experience-occasion-swiper.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
    },
  },
  {
    componentName: 'MindmegetteSponsoredBoxComponent',
    supportedContentTypes: [LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX],
    componentImport: () => import('@shared/components/sponsored-box/sponsored-box.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.config);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'SecretDaysCalendarAdapterComponent',
    supportedContentTypes: [LayoutElementContentType.SECRET_DAYS_CALENDAR],
    componentImport: () => import('@shared/components/secret-days-calendar-adapter/secret-days-calendar-adapter.component'),
    setProps: ({ componentRef, layoutElement, desktopWidth }): void => {
      componentRef.setInput('id', layoutElement?.config?.selectedCalendar?.id);
      componentRef.setInput('desktopWidth', desktopWidth);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'VariableSponsoredDidYouKnowWrapperComponent',
    supportedContentTypes: [LayoutElementContentType.DID_YOU_KNOW],
    componentImport: () => import('@shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('id', layoutElement?.config?.selectedDidYouKnowBox?.[0]?.id);
    },
    skipExtractorCheck: true,
  },
  {
    componentName: 'SponsoredQuizComponent',
    supportedContentTypes: [LayoutElementContentType.SponsoredQuiz],
    componentImport: () => import('@shared/components/sponsored-quiz/sponsored-quiz.component'),
    setProps: ({ componentRef, layoutElement }): void => {
      componentRef.setInput('data', layoutElement?.extractorData);
    },
  },
];
