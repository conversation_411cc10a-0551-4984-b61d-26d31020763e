import { Injectable } from '@angular/core';
import { backendDateToDate, DataExtractorFunction, LayoutDataExtractorService, LayoutElementContent } from '@trendency/kesma-ui';
import { LayoutElementContentConfigurationGastroThematicRecommender } from '../../gastro/definitions/experience-layout.definitions';
import { GastroExperienceOccasionCard, ThematicExperience } from '../../../shared';

type RESULT_TYPE = ThematicExperience | undefined;

@Injectable()
export class GastroThematicRecommenderExtractor implements LayoutDataExtractorService<RESULT_TYPE> {
  extractData: DataExtractorFunction<RESULT_TYPE> = (element: LayoutElementContent) => {
    const config = element.config as LayoutElementContentConfigurationGastroThematicRecommender;
    if (!config || !config.selectedOccasions) {
      return;
    }

    return {
      data: {
        blockTitle: config.blockTitle,
        buttonUrl: config.buttonUrl,
        occasions: config.selectedOccasions.map(
          (item) =>
            ({
              title: item?.data?.title,
              lead: item?.data?.lead,
              slug: item?.data?.occasionSlug,
              thumbnailUrl: item?.data?.featuredImageUrl,
              author: item?.data?.publicAuthorFullName
                ? {
                    name: item?.data?.publicAuthorFullName,
                    slug: item?.data?.publicAuthorSlug,
                    avatarUrl: item?.data?.publicAuthorAvatar?.thumbnailUrl,
                    isMaestroAuthor: item?.data?.publicAuthorIsMaestroAuthor,
                  }
                : undefined,
              date: item?.data?.startOfExperienceEvent ? this.getThematicOccassionDate(item?.data?.startOfExperienceEvent) : undefined,
              isComingSoon: item?.data?.isComingSoon,
              sponsorship: {
                fontColor: item?.data?.fontColor,
                highlightedColor: item?.data?.highlightedColor,
                logo: item?.data?.sponsorshipLogo,
                url: item?.data?.sponsorshipUrl,
              },
            }) as unknown as GastroExperienceOccasionCard
        ),
      } as ThematicExperience,
      meta: {
        extractedBy: GastroThematicRecommenderExtractor.name,
      },
    };
  };

  getThematicOccassionDate(occassionDate: string | Date): Date | null {
    return typeof occassionDate === 'string' ? backendDateToDate(occassionDate) : occassionDate;
  }
}
