<section class="latest-recipes-page">
  <div class="wrapper with-aside">
    <div class="left-column">
      <h1 class="search-title">{{ 'Legfrissebb receptek' | titleWithPage: (limitables$ | async)?.pageCurrent : true }}</h1>

      <div class="results">
        <div *ngIf="limitables$ | async as limitables" class="results-label">
          Összes találat
          <sup>({{ limitables.rowAllCount }} db)</sup>
        </div>
      </div>

      <div class="article-card-wrapper">
        <ng-container *ngIf="recipes$ | async as recipes">
          <ng-container *ngIf="recipes.length > 0">
            <mindmegette-recipe-card
              *ngFor="let recipe of recipes | slice: 0 : 6"
              [data]="$any(recipe)"
              [hasBackground]="true"
              [styleID]="RecipeCardType.TopImageLeftAlignedCard"
            ></mindmegette-recipe-card>
          </ng-container>
        </ng-container>
      </div>

      <ng-container *ngIf="recipes$ | async as recipes">
        <!-- AD -->
        <ng-container *ngIf="recipes?.length && recipes.length >= 4">
          <kesma-advertisement-adocean
            *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_1'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_1_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>
        </ng-container>
        <!-- END AD -->
      </ng-container>

      <ng-container *ngIf="recipes$ | async as recipes">
        <div *ngIf="recipes?.length && recipes.length > 6" class="article-card-wrapper">
          <mindmegette-recipe-card
            *ngFor="let recipe of recipes | slice: 6 : 12"
            [data]="$any(recipe)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          ></mindmegette-recipe-card>
        </div>
      </ng-container>

      <ng-container *ngIf="recipes$ | async as recipes">
        <!-- AD -->
        <ng-container *ngIf="recipes?.length && recipes.length >= 10">
          <kesma-advertisement-adocean
            *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_2'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_2_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>
        </ng-container>
      </ng-container>
      <!-- END AD -->

      <ng-container *ngIf="recipes$ | async as recipes">
        <div *ngIf="recipes?.length && recipes.length > 12" class="article-card-wrapper">
          <mindmegette-recipe-card
            *ngFor="let recipe of recipes | slice: 12 : 18"
            [data]="$any(recipe)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          ></mindmegette-recipe-card>
        </div>
      </ng-container>

      <ng-container *ngIf="recipes$ | async as recipes">
        <!-- AD -->
        <ng-container *ngIf="recipes?.length && recipes.length >= 10">
          <kesma-advertisement-adocean
            *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_3'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_3_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.['mobilrectangle_3'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>
        </ng-container>
      </ng-container>
      <!-- END AD -->

      <ng-container *ngIf="recipes$ | async as recipes">
        <div *ngIf="recipes?.length && recipes.length > 18" class="article-card-wrapper">
          <mindmegette-recipe-card
            *ngFor="let recipe of recipes | slice: 18 : 24"
            [data]="$any(recipe)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          ></mindmegette-recipe-card>
        </div>
      </ng-container>

      <ng-container *ngIf="recipes$ | async as recipes">
        <!-- AD -->
        <ng-container *ngIf="recipes?.length && recipes.length >= 10">
          <kesma-advertisement-adocean
            *ngIf="showDesktopAd && adverts?.desktop?.['roadblock_4'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!showDesktopAd && box_4_tablet as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.['mobilrectangle_4'] as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
            }"
          ></kesma-advertisement-adocean>
        </ng-container>
      </ng-container>
      <!-- END AD -->

      <ng-container *ngIf="recipes$ | async as recipes">
        <div *ngIf="recipes?.length && recipes.length > 24" class="article-card-wrapper">
          <mindmegette-recipe-card
            *ngFor="let recipe of recipes | slice: 24 : 30"
            [data]="$any(recipe)"
            [hasBackground]="true"
            [styleID]="RecipeCardType.TopImageLeftAlignedCard"
          ></mindmegette-recipe-card>
        </div>
      </ng-container>

      <ng-container *ngIf="limitables$ | async as limitable">
        <mindmegette-pager
          *ngIf="limitable && limitable.pageMax! > 0"
          [allowAutoScrollToTop]="true"
          [hasFirstLastButton]="false"
          [hasSkipButton]="true"
          [isCountPager]="false"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="limitable.rowAllCount!"
          [rowOnPageCount]="limitable.rowOnPageCount!"
        ></mindmegette-pager>
      </ng-container>

      <app-external-recommendations></app-external-recommendations>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </aside>
  </div>
</section>
