<ng-container *ngIf="ingredients">
  <div class="ingredients-wrapper">
    <div class="title">Hozzávalók</div>
    <div class="ingredients" *ngFor="let item of ingredients">
      <strong class="ingredients-group" *ngIf="item?.group as group">{{ group }}:</strong>
      <div class="ingredients-meta" *ngFor="let data of item.ingredients">
        <input
          class="ingredients-checkbox"
          type="checkbox"
          [attr.aria-label]="data?.ingredient?.title + ' hozzávaló.'"
          [checked]="isChecked(data.ingredient?.id!)"
          (change)="handleSelect(data)"
        />
        <strong *ngIf="data?.quantity as quantity">{{ quantity }}</strong>
        <span *ngIf="data?.unit as unit">{{ unit }}</span>

        <ng-container *ngIf="data?.ingredient?.slug as slug; else withoutSlug">
          <a class="ingredients-link" [routerLink]="['/', 'hozzavalo', slug]">
            {{ data?.ingredient?.title | lowercase }}
          </a>
        </ng-container>

        <ng-template #withoutSlug>
          <span>{{ data?.ingredient?.title }}</span>
        </ng-template>

        <ng-container *ngIf="!data?.required">
          <small>(opcionális)</small>
        </ng-container>
        <small *ngIf="data?.comment as comment">({{ comment }}) </small>
        <small class="ingredients-comment" *ngIf="data?.ingredient?.description as description">{{ description }}</small>
      </div>
    </div>

    <mindmegette-simple-button color="primary" (click)="openAuthenticatedRoute('/profil/bevasarlolista')">
      <div class="save">
        <div class="save-text">Mentés listába:</div>
        <div class="save-count">{{ selectedIngredients?.length }}db <i class="icon mindmegette-icon-arrow-right"></i></div>
      </div>
    </mindmegette-simple-button>
  </div>
</ng-container>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.['box_hozzavalok'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
</kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_hozzavalok'] as ad" [ad]="ad" [style]="{ margin: 'var(--ad-margin)' }">
</kesma-advertisement-adocean>
