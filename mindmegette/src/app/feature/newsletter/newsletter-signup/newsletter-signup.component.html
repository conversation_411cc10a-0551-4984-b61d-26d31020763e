<app-sticky-image-wrapper [isNewsletterSignup]="true">
  <section class="newsletter-subscribe-body">
    <ng-container *ngTemplateOutlet="signupBody"></ng-container>
    <app-latest-recipes-recommendation class="recommendation"></app-latest-recipes-recommendation>
  </section>
</app-sticky-image-wrapper>

<ng-template #signupBody>
  <h1><PERSON><PERSON>rle<PERSON><PERSON><PERSON></h1>
  <section class="form-wrapper">
    <span id="automizy-form-e3gpvsjjl6dbtrel4arnz3yex3k">
      <form [action]="action | bypass: 'url'" class="automizy-form-form" id="automizy-form-e3gpvsjjl6dbtrel4arnz3yex3k-form" method="post">
        <div class="automizy-form-fields">
          <div class="name-wrapper">
            <div class="automizy-form-input-box" data-automizy-field="firstname">
              <label class="automizy-form-input-label"
                >Vezetéknév<img [src]="'/assets/images/icons/required-symbol-icon.svg'" alt="" class="required-symbol"
              /></label>
              <input class="automizy-form-input" maxlength="500" name="fields[1]" placeholder="Vezetéknév" required type="text" />
            </div>
            <div class="automizy-form-input-box" data-automizy-field="lastname">
              <label class="automizy-form-input-label"
                >Keresztnév<img [src]="'/assets/images/icons/required-symbol-icon.svg'" alt="" class="required-symbol"
              /></label>
              <input class="automizy-form-input" maxlength="500" name="fields[2]" placeholder="Keresztnév" required type="text" />
            </div>
          </div>
          <div class="automizy-form-input-box" data-automizy-field="email">
            <label class="automizy-form-input-label"
              >E-mail cím<img [src]="'/assets/images/icons/required-symbol-icon.svg'" alt="" class="required-symbol"
            /></label>
            <input
              class="automizy-form-input"
              maxlength="250"
              name="email"
              pattern="^([a-zA-Z0-9_-.]+)@(([[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.)|(([a-zA-Z0-9-]+.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(]?)$"
              placeholder="pl.: <EMAIL>"
              required
              type="email"
            />
          </div>
          <div class="automizy-form-input-box radios" data-automizy-field="hirlevel_tipusa">
            <label class="mindmegette-form-radio" for="heti_hirlevel">
              <input name="fields[10]" id="heti_hirlevel" value="heti_hirlevel" type="radio" [checked]="!isGastro" />
              <span>Mindmegette hírlevél</span>
            </label>
            <label class="mindmegette-form-radio" for="elmeny_hirlevel">
              <input name="fields[10]" id="elmeny_hirlevel" value="elmeny_hirlevel" type="radio" [checked]="isGastro" />
              <span>Élmények hírlevél</span>
            </label>
            <label class="mindmegette-form-radio" for="mindketto">
              <input name="fields[10]" id="mindketto" value="mindketto" type="radio" />
              <span>Feliratkozom mindkét hírlevélre</span>
            </label>
          </div>

          <div class="automizy-form-privacy">
            A feliratkozom a hírlevélre gomb megnyomásával elismerem, hogy elolvastam, megértettem és elfogadom az
            <a href="https://mediaworks.hu/wp-content/uploads/2025/02/9199536Mindmegetteadatvedelmitajekoztato20250214.pdf" target="_blank" class="terms">
              Adatvédelmi szabályzatot</a
            >, mely alapján az oldal az adataimat kezeli.
          </div>
          <div class="automizy-form-input-box marketing" data-automizy-field="marketing_hozzajarulas">
            <input id="marketing-hozzájárulás" name="fields[11]" type="checkbox" />
            <label class="automizy-form-input-label" for="marketing-hozzájárulás">
              Érdekelnek a Mediaworks Hungary Zrt. és más harmadik felek különleges ajánlatai és reklámjai (ideértve például a recept és más tartalmi
              ajánlókat), ezért szeretnék direkt marketing - közvetlen üzletszerzési célú - hírleveleket kapni e-mailen. Hozzájárulok a megadott személyes
              adataim kezeléséhez az adatkezelési tájékoztatóban foglaltak szerint (a személyes adatokat csak a Mediaworks Hungary Zrt. fogja kezelni).
            </label>
          </div>
          @if (isActivePeriod) {
            <h2 class="automizy-form-nyeremenyjatek-title">Iratkozzon fel a hírlevélre és nyerjen!</h2>

            <div class="automizy-form-nyeremenyjatek">
              <span>Kijelentem, hogy részt szeretnék venni a hírlevél feliratkozást ösztönző nyereményjátékban.</span>
              <span
                >(Szervező tájékoztatja a feliratkozókat, hogy a nyereményjátékban csak abban az esetben vesznek részt, amennyiben az alábbi checkboxot
                elfogadják. Amennyiben a nyereményjátékban nem kívánnak részt venni a lenti checkbox kipipálása nélkül a Feliratkozom a hírlevélre gomb
                megnyomásával iratkozhatnak fel a Hírlevél szolgáltatásra).</span
              >
            </div>

            <div class="automizy-form-input-box nyeremenyjatek" data-automizy-field="nyeremenyjatek">
              <input id="nyereményjáték" name="fields[12]" type="checkbox" />
              <label for="nyereményjáték" class="automizy-form-input-label with-link"
                >Elolvastam és megértettem a <a href="https://mediaworks.hu/jatekszabalyzat/" target="blank">játékszabályokat</a>. Kijelentem, hogy érdekelnek a
                Mediaworks Hungary Zrt. és más harmadik felek különleges ajánlatai és reklámjai (ideértve például a recept és más tartalmi ajánlókat), ezért
                hozzájárulok, hogy – hozzájárulásom visszavonásáig –, direkt marketing - közvetlen üzletszerzési célú - tájékoztatást kapjak e-mailben, továbbá
                az adatkezelő a személyes adataimat a játékszabályzat és adatvédelmi tájékoztatóban foglaltak szerint kezelje.</label
              >
            </div>
          }
        </div>
        <div class="automizy-form-button-box">
          <input
            type="submit"
            id="automizy-form-e3gpvsjjl6dbtrel4arnz3yex3k-captcha-submit"
            class="automizy-form-button g-recaptcha"
            data-sitekey="6LdOfLgqAAAAAO0D_ZIeqAl4ilRn2X3zFTwatGL7"
            data-callback="automizy_form_mzwvlhgezjlltkg0y93f_7ua2awOnSubmit"
            value="Feliratkozom a hírlevélre"
          />
          <input
            type="submit"
            id="automizy-form-e3gpvsjjl6dbtrel4arnz3yex3k-submit"
            class="automizy-form-button"
            style="display: none"
            value="Feliratkozom a hírlevélre"
          />
        </div>
      </form>
    </span>
  </section>
</ng-template>
