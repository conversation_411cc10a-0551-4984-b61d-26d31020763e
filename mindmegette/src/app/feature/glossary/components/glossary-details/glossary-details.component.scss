@use 'shared' as *;

:host {
  display: block;
  width: 1384px;
  margin: 0 auto 32px;
  max-width: calc(100% - 30px);

  .glossary {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin-bottom: 32px;

    &-title {
      display: flex;
      gap: 6px;
      align-items: center;

      kesma-icon {
        flex-shrink: 0;
      }

      &-text {
        font-size: 32px;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.96px;
        color: var(--kui-green-900);
        text-transform: capitalize;
      }
    }
  }

  .articles {
    &-text {
      font-family: var(--kui-font-primary);
      font-size: 24px;
      font-weight: 600;
      line-height: 28px;
      letter-spacing: 0.12px;
      color: var(--kui-gray-950);
      margin-bottom: 16px;

      sup {
        font-weight: 400;
        font-size: 14px;
      }
    }

    &-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 32px;

      ::ng-deep {
        mindmegette-recipe-card {
          margin-bottom: unset;
        }
      }

      @include media-breakpoint-down(lg) {
        grid-template-columns: repeat(2, 1fr);
      }

      @include media-breakpoint-down(xs) {
        grid-template-columns: 1fr;
        grid-gap: 24px;
      }
    }

    mindmegette-newsletter {
      margin-bottom: 32px;
    }
  }

  .recipe-card {
    display: grid;
    grid-template-columns: 3fr 2fr;
    grid-template-rows: 1fr;
    grid-column-gap: 0;
    grid-row-gap: 0;
    padding: 32px 0;
    border-radius: 8px;
    background-color: var(--kui-white);
    outline: 1px solid var(--kui-gray-100);
    margin-bottom: 24px;

    @include media-breakpoint-down(lg) {
      display: flex;
      flex-direction: column-reverse;
      margin: 0 0 24px;
      row-gap: 16px;
      padding: 16px;
    }

    &-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 32px;
      align-self: stretch;
      margin: 41px 0;
      @include media-breakpoint-down(lg) {
        gap: 8px;
        margin: 0;
      }

      &-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        align-self: stretch;

        .category-label-wrapper {
          display: flex;
          flex-direction: row;
          gap: 4px;
        }

        .recipe-title {
          font-family: var(--kui-font-secondary);
          color: var(--kui-black);
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 26px;
          text-align: center;
          margin: 0px 16px;
        }

        .recipe-data {
          display: flex;
          flex-direction: row;
          gap: 12px;
        }

        .recipe-button {
          display: flex;
          padding: 10px 20px;
          justify-content: center;
          align-items: center;
          gap: 6px;
          margin-top: 24px;
          border-radius: 8px;
          font-family: var(--kui-font-secondary);
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          letter-spacing: 0.16px;
          color: var(--kui-white);
          background-color: var(--kui-green-700);
          cursor: pointer;
          @include media-breakpoint-down(lg) {
            margin-top: 8px;
            width: 100%;
          }

          i {
            width: 20px;
            height: 20px;
          }
        }
      }
    }

    &-right {
      display: flex;
      padding-right: 32px;
      flex-direction: column;
      justify-content: center;
      align-items: flex-end;
      gap: 10px;
      align-self: stretch;

      img {
        border-radius: 8px;
        object-fit: cover;
      }

      @include media-breakpoint-down(lg) {
        padding: 0;
        width: 100%;
      }
    }

    &-details {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      overflow: hidden;
    }

    &-meta {
      padding: 0 12px;
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;

      &.divider {
        border-right: 1px solid var(--kui-gray-300);
      }
    }
  }

  mindmegette-wysiwyg-box {
    ::ng-deep {
      .custom-text-style {
        &.underlined-text {
          margin: 0;
          @include media-breakpoint-down(sm) {
            padding: unset;
          }
        }
      }

      .block-content {
        > * {
          margin-bottom: 24px;
        }
      }

      :last-child {
        margin-bottom: 0;
      }
    }
  }

  mindmegette-wysiwyg-box::ng-deep .table-wrapper {
    overflow-x: auto;

    table {
      display: block;
    }
  }
}
