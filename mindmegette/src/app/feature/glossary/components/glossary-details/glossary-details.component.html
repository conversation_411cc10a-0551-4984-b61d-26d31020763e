<section class="wrapper with-aside">
  <div class="left-column">
    <section class="glossary">
      <div class="glossary-title">
        <kesma-icon name="icon-right-arrow" [size]="24" />
        <h1 class="glossary-title-text">
          {{ resolvedData()?.glossary?.title }}
        </h1>
      </div>
      <div class="glossary-description">
        <ng-container [ngTemplateOutletContext]="{ body: glossaryBody() }" [ngTemplateOutlet]="bodyContent"></ng-container>
      </div>
    </section>

    <div class="articles">
      <h2 class="articles-text">
        Cikkek és receptek a témában <sup>({{ articlesMeta()?.limitable?.rowAllCount ?? 0 }} db)</sup>
      </h2>
      <mindmegette-newsletter [styleID]="newsletterStyle" />
      <ul class="articles-list">
        @for (article of resolvedData()?.articles?.data; track article.id) {
          <li class="articles-item">
            @if (article.contentType === 'recipe') {
              <mindmegette-recipe-card [styleID]="recipeCardStyle" [data]="article" [hasBackground]="true" />
            } @else {
              <mindmegette-article-card [styleID]="articleCardStyle" [data]="article" />
            }
          </li>
        } @empty {
          Nincsennek cikkek vagy receptek ebben a témában.
        }
      </ul>
      @if (hasPages()) {
        <mindmegette-pager
          [allowAutoScrollToTop]="true"
          [hasFirstLastButton]="false"
          [hasSkipButton]="true"
          [isCountPager]="false"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="articlesMeta()?.limitable?.rowAllCount ?? 0"
          [rowOnPageCount]="articlesMeta()?.limitable?.rowOnPageCount ?? 0"
        />
      }
    </div>
  </div>
  <aside>
    <app-sidebar [adPageType]="adPageType" />
  </aside>
</section>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <mindmegette-wysiwyg-box [html]="(wysiwygDetail?.value | wrapTable) || ''" trArticleFileLink></mindmegette-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <!-- Author doesn't appear. It seems BE doesn't send publicAuthor property in dbcache -->
        <mindmegette-article-card
          [data]="simpleArticleRecommendation(element?.details[0])"
          [styleID]="ArticleCardType.LeftImageLongCard"
        ></mindmegette-article-card>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <mindmegette-slider-gallery
          (fullscreenLayerClicked)="openGalleryDedicatedRouteLayer($event)"
          (slideChanged)="handleGallerySlideChange(galleries()[element?.details[0]?.value?.id], $event)"
          *ngIf="galleries()[element?.details[0]?.value?.id]"
          [data]="galleries()[element?.details[0]?.value?.id]"
        ></mindmegette-slider-gallery>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Recipe">
        <div class="recipe-card">
          <div class="recipe-card-left">
            <div class="recipe-card-left-wrapper">
              <div class="category-label-wrapper">
                <mindmegette-category-label [styleID]="CategoryLabelType.RECIPE" [title]="'Recept'"></mindmegette-category-label>
                <mindmegette-category-label
                  *ngIf="element?.details?.[0]?.value?.difficulty as difficulty"
                  [styleID]="CategoryLabelType.CATEGORY"
                  [title]="difficulty"
                ></mindmegette-category-label>
              </div>
              <a [routerLink]="['/', 'recept', element?.details[0]?.value?.slug]" class="recipe-title">{{ element?.details[0]?.value?.title }}</a>
              <div class="recipe-card-details">
                <div class="recipe-card-meta divider">{{ element?.details[0]?.value?.totalTime }} perc</div>
                <div [ngClass]="{ divider: element?.details[0]?.value?.cost }" class="recipe-card-meta">
                  {{ element?.details[0]?.value?.madeForPeople }} adag
                </div>
                <div *ngIf="element?.details[0]?.value?.cost" class="recipe-card-meta">{{ element?.details[0]?.value?.cost }}</div>
              </div>
              <a [routerLink]="['/', 'recept', element?.details[0]?.value?.slug]" class="recipe-button">
                Tovább a recepthez
                <i class="icon mindmegette-icon-white-right-arrow"></i>
              </a>
            </div>
          </div>
          <div class="recipe-card-right">
            <img
              withFocusPoint
              [data]="element?.details[0]?.value?.coverImageFocusedImages"
              [alt]="'recept'"
              [displayedUrl]="element?.details[0]?.value?.coverImage || '/assets/images/placeholder.jpg'"
              [displayedAspectRatio]="{ desktop: '1:1' }"
              class="thumbnail"
              loading="lazy"
            />
          </div>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
