import { Component, computed, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { map } from 'rxjs/operators';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { IGlossaryResolverData } from '@feature/glossary/definitions/glossary.definitions';
import {
  ArticleCardComponent,
  CategoryLabelComponent,
  MindmegettePagerComponent,
  MindmegetteWysiwygBoxComponent,
  MMESliderGalleryComponent,
  NewsletterComponent,
  RecipeCardComponent,
  SliderGalleryFullscreenLayerClickedEvent,
} from '@shared/components';
import { CategoryLabelTypes, MindmegetteArticleCardType, MindmegetteNewsletterCardType, RecipeCardType } from '@shared/definitions';
import {
  AnalyticsService,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleVideoComponent,
  FocusPointDirective,
  GalleryData,
  GalleryElementData,
  IconComponent,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
} from '@trendency/kesma-ui';
import { SidebarComponent } from '@feature/layout/components/sidebar/sidebar.component';
import { NgClass, NgForOf, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { WrapTablePipe } from '@shared/pipes';
import { forkJoin } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { GalleryService } from '@shared/services';

@Component({
  selector: 'app-glossary-details',
  imports: [
    MindmegetteWysiwygBoxComponent,
    ArticleCardComponent,
    MindmegettePagerComponent,
    IconComponent,
    SidebarComponent,
    NewsletterComponent,
    RecipeCardComponent,
    ArticleFileLinkDirective,
    ArticleVideoComponent,
    CategoryLabelComponent,
    FocusPointDirective,
    NgForOf,
    NgIf,
    NgSwitchCase,
    WrapTablePipe,
    RouterLink,
    NgClass,
    NgSwitch,
    NgTemplateOutlet,
    MMESliderGalleryComponent,
  ],
  templateUrl: './glossary-details.component.html',
  styleUrl: './glossary-details.component.scss',
})
export class GlossaryDetailsComponent implements OnInit {
  private readonly seo = inject(SeoService);
  private readonly utilsService = inject(UtilService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly router = inject(Router);
  private readonly galleryService = inject(GalleryService);
  private readonly destroyRef = inject(DestroyRef);

  readonly route = inject(ActivatedRoute);
  readonly resolvedData = toSignal(this.route.data.pipe(map(({ data }) => data as IGlossaryResolverData)));
  readonly articlesMeta = computed(() => this.resolvedData()?.articles.meta);
  readonly hasPages = computed(() => (this.articlesMeta()?.limitable?.pageMax ?? 0) > 0);
  readonly glossaryBody = computed(() => this.#prepareGlossaryBody(this.resolvedData()?.glossary?.description as ArticleBody[]));

  readonly articleCardStyle = MindmegetteArticleCardType.TopImageLeftAlignedCard;
  readonly recipeCardStyle = RecipeCardType.TopImageLeftAlignedCard;
  readonly adPageType = PAGE_TYPES.search_page;
  readonly newsletterStyle = MindmegetteNewsletterCardType.LeftImageCard;

  readonly galleries = signal<Record<string, GalleryData>>({});

  readonly ArticleCardType = MindmegetteArticleCardType;
  readonly CategoryLabelType = CategoryLabelTypes;
  readonly ArticleBodyType = ArticleBodyType;

  ngOnInit(): void {
    this.route.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.#loadEmbeddedGalleries();
    });
  }

  #prepareGlossaryBody(body: ArticleBody[]): ArticleBody[] {
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareGlossaryBodyDetail(detail, bodyPart.type),
      })),
    }));
  }

  #prepareGlossaryBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    if (type === ArticleBodyType.Article) {
      return {
        ...detail,
        value: previewBackendArticleToArticleCard(detail.value),
      };
    }
    return {
      ...detail,
    };
  }

  simpleArticleRecommendation(articleBodyDetail: ArticleBodyDetails): ArticleCard {
    const article = articleBodyDetail.value;
    return {
      ...article,
      columnSlug: article?.category?.slug,
      columnTitle: article?.category?.name,
    } as ArticleCard;
  }

  #loadEmbeddedGalleries(): void {
    const bodyElements = (this.glossaryBody() as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
          this.galleries.update((galleries) => ({
            ...galleries,
            [gallery.id]: galleryData,
          }));
        });
      });
  }

  /**
   * When changing slides in the gallery we should send a pageView to Google Analytics and Gemius.
   * We need to explicitly send the href, title and referrers as these pageViews are just "virtual" views, because
   * they are not triggered by a real navigation in the browser.
   * @param gallery gallery that should receive the page views.
   * @param params parameters of the slide change event. For example the index of the image
   */
  handleGallerySlideChange(gallery: GalleryData, params: any): void {
    const { index } = params;
    const galleryUrl = [this.seo.hostUrl, 'galeria', gallery.slug, ...(index ? [index + 1] : [])].join('/');
    const pageViewParams = {
      href: galleryUrl,
      title: gallery.title,
      referrer: this.seo.currentUrl,
    } as any;
    this.analyticsService.sendPageView(pageViewParams, 'Galéria');
    if (typeof pp_gemius_hit !== 'undefined') {
      pp_gemius_hit(environment.gemiusId, `page=${galleryUrl}`);
    }
  }

  openGalleryDedicatedRouteLayer({ gallery, selectedImageIndex }: SliderGalleryFullscreenLayerClickedEvent): void {
    if (!gallery || !this.utilsService.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }
}
