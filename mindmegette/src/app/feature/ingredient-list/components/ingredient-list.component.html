<section>
  <app-breadcrumb [data]="[{ label: 'Hozz<PERSON><PERSON>ók' }]" />
  <div class="wrapper with-aside">
    <main class="left-column">
      <mindmegette-block-title [data]="{ text: 'Hozz<PERSON><PERSON>ók' }" [headingLevel]="1" [styleID]="MindmegetteBlockTitleType.PageTitle"></mindmegette-block-title>

      <app-search-bar (handleSearchResult)="onSearch($event)" searchBackendKey="title_filter" searchButtonText="Hozzávaló keresése"></app-search-bar>

      <app-ingredient-abc (handleSearch)="onSearch($event)"></app-ingredient-abc>

      @if (ingredients?.length) {
        <div class="ingredients">
          <mindmegette-ingredient-card [data]="ingredient" [hasBackground]="true" *ngFor="let ingredient of ingredients"></mindmegette-ingredient-card>
        </div>
      } @else {
        <div class="no-result">
          <PERSON><PERSON><PERSON>, erre a keresőszóra nem találunk semmit! <PERSON><PERSON> le<PERSON>t, ho<PERSON> hib<PERSON><PERSON> betűzted a szót? Ha nem találsz semmit, keress rá hasonló szóval!
        </div>
      }

      <mindmegette-pager
        *ngIf="limitables && limitables.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [isCountPager]="false"
        [isListPager]="true"
        [maxDisplayedPages]="5"
        [rowAllCount]="limitables.rowAllCount!"
        [rowOnPageCount]="limitables.rowOnPageCount!"
      >
      </mindmegette-pager>

      <app-external-recommendations></app-external-recommendations>
    </main>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
