import { Routes, UrlMatchResult, UrlSegment } from '@angular/router';
import { StaticPageResolver } from '../static-page/api/static-page.resolver';
import { GastroExperienceOccasionListResolver } from './gastro-experience-occasion-list/gastro-experience-occasion-list.resolver';
import { gastroExperienceOccasionPageResolver } from './gastro-experience-occasion-page/gastro-experience-occasion-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { gastroExperienceOccasionNotificationFormResolver } from './gastro-experience-occasion-notification-form/gastro-experience-occasion-notification-form.resolver';
import { gastroPurchasePageResolver } from './gastro-purchase-page/gastro-purchase-page.resolver';
import { gastroPurchaseResultPageResolver } from './gastro-purchase-result-page/gastro-purchase-result-page.resolver';
import { AuthGuard, autoOccasionListResolver } from '../../shared';
import { gastroSidebarResolver } from './shared/gastro-sidebar.resolver';

export const gastroRoutes: Routes = [
  {
    matcher: gastroMatcher,
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadComponent: () => import('./gastro-static-page/gastro-static-page.component').then((m) => m.GastroStaticPageComponent),
        data: {
          omitGlobalPageView: true,
        },
        resolve: {
          data: StaticPageResolver,
        },
        providers: [StaticPageResolver],
      },
      {
        path: 'esemenyek',
        loadComponent: () =>
          import('./gastro-experience-occasion-list/gastro-experience-occasion-list.component').then((m) => m.GastroExperienceOccasionListComponent),
        canActivate: [PageValidatorGuard],
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        resolve: {
          data: GastroExperienceOccasionListResolver,
          sidebar: gastroSidebarResolver,
        },
      },
      {
        path: 'esemenyek/ertesites/:slug',
        loadComponent: () =>
          import('./gastro-experience-occasion-notification-form/gastro-experience-occasion-notification-form.component').then(
            (m) => m.GastroExperienceOccasionNotificationFormComponent
          ),
        resolve: {
          data: gastroExperienceOccasionNotificationFormResolver,
        },
      },
      {
        path: 'vasarlas',
        loadComponent: () => import('./gastro-purchase-result-page/gastro-purchase-result-page.component').then((m) => m.GastroPurchaseResultPageComponent),
        canActivate: [AuthGuard],
        resolve: {
          data: gastroPurchaseResultPageResolver,
          occasionList: autoOccasionListResolver,
        },
      },
      {
        path: 'vasarlas/:slug',
        loadComponent: () => import('./gastro-purchase-page/gastro-purchase-page.component').then((m) => m.GastroPurchasePageComponent),
        canActivate: [AuthGuard],
        resolve: {
          data: gastroPurchasePageResolver,
        },
      },
      {
        path: 'esemenyek/:slug',
        loadComponent: () =>
          import('./gastro-experience-occasion-page/gastro-experience-occasion-page.component').then((m) => m.GastroExperienceOccasionPageComponent),
        resolve: {
          data: gastroExperienceOccasionPageResolver,
          sidebar: gastroSidebarResolver,
        },
      },
      {
        path: ':slug',
        loadComponent: () => import('./gastro-static-page/gastro-static-page.component').then((m) => m.GastroStaticPageComponent),
        resolve: {
          data: StaticPageResolver,
          occasionList: autoOccasionListResolver,
        },
        data: {
          omitGlobalPageView: true,
        },
        providers: [StaticPageResolver],
      },
    ],
  },
];

function gastroMatcher(): UrlMatchResult {
  return {
    consumed: [],
    posParams: {
      gastroSlug: new UrlSegment('elmenyek', {}),
    },
  };
}
