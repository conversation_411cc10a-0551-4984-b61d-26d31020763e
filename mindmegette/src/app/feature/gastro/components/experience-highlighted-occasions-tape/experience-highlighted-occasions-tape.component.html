<ng-template #itemTemplate let-occasion="data">
  <a [routerLink]="['/elmenyek', 'esemenyek', occasion.occasionSlug]" class="occasion">
    <img
      withFocusPoint
      [data]="occasion?.featuredImageUrlFocusedImages"
      [displayedUrl]="occasion?.featuredImageUrl"
      [displayedAspectRatio]="{ desktop: '1:1' }"
      [alt]="occasion?.title ?? ''"
      class="occasion-img"
    />

    <div class="occasion-data">
      <div class="occasion-title">{{ occasion?.title }}</div>
      <div *ngIf="occasion?.startOfExperienceEvent" class="occasion-date">
        {{ parseISO(occasion.startOfExperienceEvent?.toString()) | dfnsFormat: 'MMMM dd. HH:mm' | titlecase }}
      </div>
    </div>
  </a>
</ng-template>
<ng-template #previousNavigation><kesma-icon name="mindmegette-icon-white-left-arrow" /></ng-template>
<ng-template #nextNavigation><kesma-icon name="mindmegette-icon-white-right-arrow" /></ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="true"
  [data]="data"
  [breakpoints]="breakpoints"
></div>
