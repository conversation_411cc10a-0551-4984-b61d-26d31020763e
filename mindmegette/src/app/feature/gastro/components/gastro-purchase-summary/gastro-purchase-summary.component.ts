import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  InputSignal,
  output,
  OutputEmitterRef,
  Signal,
  signal,
  WritableSignal,
} from '@angular/core';
import { GastroDurationPipe } from '../../shared/gastro-duration.pipe';
import { GastroPurchaseOccasionComponent } from '../gastro-purchase-occasion/gastro-purchase-occasion.component';
import { BackendCoupon, BackendOrderError, IconComponent, PortalConfigSetting, ThousandSeparatorPipe, User } from '@trendency/kesma-ui';
import { FormatDatePipe, UtilService } from '@trendency/kesma-core';
import { BackendExperienceOccasionDetails, BackendExperienceOccasionPartialDetails } from '../../definitions/experience.definitions';
import { GastroPurchaseService } from '../../shared/gastro-purchase.service';
import { GastroPurchaseSessionData, GastroPurchaseStepType, GastroPurchaseSummaryStepData } from '../../definitions/gastro-purchase.definitions';
import { catchError, retry, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { EMPTY, Observable, of, throwError } from 'rxjs';
import { gastroPurchaseSessionDataToBackendRequest } from '../../utils/purchase-session-data-to-backend-request';
import { AuthService, MindmegetteSimpleButtonComponent, PortalConfigService, SecureApiService } from '../../../../shared';
import { toSignal } from '@angular/core/rxjs-interop';
import { calculateDiscountTotal } from '@feature/gastro/utils/calculate-discount-total';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';

@Component({
  selector: 'app-gastro-purchase-summary',
  imports: [
    GastroDurationPipe,
    GastroPurchaseOccasionComponent,
    MindmegetteSimpleButtonComponent,
    FormatDatePipe,
    ThousandSeparatorPipe,
    IconComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './gastro-purchase-summary.component.html',
  styleUrl: './gastro-purchase-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GastroPurchaseSummaryComponent {
  readonly #gastroPurchaseService: GastroPurchaseService = inject(GastroPurchaseService);
  readonly #secureApiService: SecureApiService = inject(SecureApiService);
  readonly #utilsService: UtilService = inject(UtilService);
  readonly #authService: AuthService = inject(AuthService);
  readonly portalConfigService: PortalConfigService = inject(PortalConfigService);

  readonly onStepNavigation: OutputEmitterRef<void> = output<void>();

  readonly sessionData: WritableSignal<GastroPurchaseSessionData> = this.#gastroPurchaseService.sessionData;
  readonly details: InputSignal<BackendExperienceOccasionDetails> = input.required<BackendExperienceOccasionDetails>();
  readonly selectedOccasion: Signal<BackendExperienceOccasionPartialDetails> = computed(
    () => this.details()?.nextOccasions.find((item) => item.occasion.id === this.sessionData().occasion) ?? this.details()
  );
  readonly totalAmount: Signal<number> = computed(
    () => this.details().experience.studyAmount.find((item) => item.studentNumber === this.sessionData().quantity)?.amount || 0
  );
  readonly discountedTotal: Signal<number | undefined> = computed(() => calculateDiscountTotal(this.totalAmount(), this.sessionData().coupon));
  readonly isLoading: WritableSignal<boolean> = signal<boolean>(false);
  readonly error: WritableSignal<string | null> = signal<string | null>(null);
  readonly user: Signal<User | undefined> = toSignal(this.#authService.currentUserSubject);

  readonly isCouponEnabled: boolean = this.portalConfigService.isConfigSet(PortalConfigSetting.MENU_TYPE_ORDERS_COUPON);
  readonly isCouponValidating: WritableSignal<boolean> = signal<boolean>(false);
  readonly isCouponError: WritableSignal<boolean> = signal<boolean>(false);

  readonly formFields = {
    couponCode: 'couponCode',
  } as const;

  readonly formGroup = new FormGroup({
    [this.formFields.couponCode]: new FormControl<string | null>(this.sessionData().coupon?.couponCode ?? null, [Validators.maxLength(50)]),
  });

  validateOrDeleteCoupon(): void {
    if (this.sessionData().coupon) {
      this.formGroup.controls[this.formFields.couponCode].patchValue(null);
      this.#gastroPurchaseService.saveStepData(GastroPurchaseStepType.SUMMARY, { coupon: undefined } as GastroPurchaseSummaryStepData);
      return;
    }

    if ((this.formGroup.controls[this.formFields.couponCode].value ?? '').length === 0) {
      return;
    }

    this.isCouponValidating.set(true);
    this.isCouponError.set(false);

    this.#secureApiService
      .checkoutOrderPurchase(
        gastroPurchaseSessionDataToBackendRequest({
          ...this.sessionData(),
          ...({
            coupon: {
              couponCode: this.formGroup.controls[this.formFields.couponCode].value ?? '',
            },
          } as Partial<BackendCoupon>),
        })
      )
      .subscribe({
        next: ({ data }) => {
          this.isCouponValidating.set(false);
          this.#gastroPurchaseService.saveStepData(GastroPurchaseStepType.SUMMARY, { coupon: data?.orderItems?.[1]?.coupon } as GastroPurchaseSummaryStepData);
        },
        error: ({ error }) => {
          this.isCouponValidating.set(false);

          if (error.errors['orderItems.1.id']) {
            this.isCouponError.set(true);
            this.#gastroPurchaseService.saveStepData(GastroPurchaseStepType.SUMMARY, { coupon: undefined } as GastroPurchaseSummaryStepData);
          } else {
            this.handleGeneralApiErrors(error);
          }
        },
      });
  }

  backToPreviousStep(): void {
    this.#gastroPurchaseService.navigateToNextStep(GastroPurchaseStepType.BILLING);
    this.onStepNavigation.emit();
  }

  navigateToNextStep(): void {
    this.error.set(null);
    this.isLoading.set(true);

    // First handle the step to save the data
    this.#gastroPurchaseService.handleStep(GastroPurchaseStepType.SUMMARY);

    const apiCall$: Observable<{ getPaymentUrl: string; orderId: string }> = this.sessionData().orderId
      ? this.#secureApiService.retryOrderPurchase(this.sessionData().orderId ?? '')
      : this.#secureApiService.saveOrderPurchase(gastroPurchaseSessionDataToBackendRequest(this.sessionData()));

    apiCall$
      .pipe(
        switchMap(({ getPaymentUrl, orderId }) => {
          if (environment.type === 'prod') {
            getPaymentUrl = getPaymentUrl.replace(/^http:\/\//, 'https://');
          }

          // Save order ID
          this.#gastroPurchaseService.handleStep(GastroPurchaseStepType.SUMMARY, {
            orderId,
          });

          return this.pollPaymentUrl(getPaymentUrl);
        }),
        tap((paymentUrl: string) => {
          if (environment.type === 'prod') {
            paymentUrl = paymentUrl.replace(/^http:\/\//, 'https://');
          }

          if (this.#utilsService.isBrowser()) {
            window.location.href = paymentUrl;
          }
        }),
        catchError(({ error }) => {
          this.handleGeneralApiErrors(error);
          return EMPTY;
        })
      )
      .subscribe();
  }

  pollPaymentUrl(getPaymentUrlRequest: string): Observable<string> {
    // Poll for payment URL (in every 1 sec for 30 sec)
    return this.#secureApiService.getPaymentUrl(getPaymentUrlRequest).pipe(
      switchMap(({ status, simplePayPaymentUrl }) => {
        if (!simplePayPaymentUrl) {
          // If purchase is not processed yet, throw an error to run retry function
          return throwError(() => status);
        }

        // If purchase is processed, we have the payment URL, return that
        return of(simplePayPaymentUrl);
      }),
      retry({ count: 30, delay: 1000 })
    );
  }

  private handleGeneralApiErrors(error: BackendOrderError): void {
    let errorText = 'Ismeretlen hiba történt! Kérjük ellenőrizze az adatokat és próbálja újra vagy vegye fel a kapcsolatot a szerkesztőséggel!';

    if (error?.errors['orderItems.0.quantity']) {
      errorText = 'Sajnáljuk, az esemény ebben a pillanatban megtelt, kérjük válasz másik időpontot!';
    }

    this.error.set(errorText);
    this.isLoading.set(false);
  }
}
