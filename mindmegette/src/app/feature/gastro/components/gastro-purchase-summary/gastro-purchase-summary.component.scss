@use 'shared' as *;

.purchase {
  &-section {
    &-title {
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      display: flex;
      justify-content: space-between;
      gap: 12px;

      a {
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
        color: var(--kui-green-700);
        text-decoration: underline;
        transition: color 0.3s ease;

        &:hover {
          color: var(--kui-green-800);
        }
      }
    }

    &-content {
      padding: 8px 0 32px;
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 18px;
      font-weight: 400;
      line-height: 24px;

      &.coupon-code {
        padding-bottom: 0;
      }
    }
  }

  &-coupon-code {
    margin: 16px 0 32px;
    display: flex;
    align-items: center;
    gap: 32px;

    input {
      margin: 0;
    }

    mindmegette-simple-button {
      flex-shrink: 0;
    }
  }

  &-coupon-code-result {
    font-size: 12px;
    line-height: 22px;
    letter-spacing: 0.12px;
    font-family: var(--kui-font-secondary);
    margin-top: -24px;
    margin-bottom: 32px;

    kesma-icon {
      display: inline-block;
      margin-right: 8px;
      vertical-align: top;
    }

    &.success {
      color: var(--kui-green-700);
    }

    &.error {
      color: var(--kui-pink-600);
    }
  }

  &-total {
    display: flex;
    justify-content: space-between;
    color: var(--kui-gray-950);
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0.16px;
    margin-bottom: 20px;

    &.discounted,
    &.discounted h4 {
      text-decoration: line-through;
      font-size: 14px;
      line-height: 20px;
      font-weight: 400;
      margin-bottom: 0;
    }
  }

  &-action {
    mindmegette-simple-button ::ng-deep button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

app-gastro-purchase-occasion {
  display: block;
  margin-top: 16px;
}

.mindmegette-form-general-error {
  margin-bottom: 32px;

  @include media-breakpoint-between(lg, lg) {
    margin-bottom: 24px;
  }

  @include media-breakpoint-down(sm) {
    margin-bottom: 24px;
  }

  &.mt-12 {
    margin-top: 12px;
  }

  kesma-icon {
    display: inline-block;
    margin-right: 8px;
    vertical-align: top;
  }
}
