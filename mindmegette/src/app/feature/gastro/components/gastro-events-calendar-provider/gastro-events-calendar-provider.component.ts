import { ChangeDetectionStrategy, Component, computed, ElementRef, inject, OnInit, signal, viewChild } from '@angular/core';
import { GastroEventsCalendarComponent } from '../gastro-events-calendar/gastro-events-calendar.component';
import { of, take } from 'rxjs';
import { GastroApiService } from '../../shared/gastro-api.service';
import { catchError, map } from 'rxjs/operators';
import { addMonths, isAfter, isSameDay } from 'date-fns';
import { backendDateToDate, UtilService } from '@trendency/kesma-core';
import { occasionDetailToOccasionCard } from '../../utils/occasion-detail-to-occasion-card';
import { GastroExperienceOccasionCardComponent, GastroExperienceOccasionCardType } from '../../../../shared';
import { BackendExperienceOccasion } from '../../definitions/occasion-definitions';

export type Month = {
  year: number;
  month: number;
};
const EXTRA_MONTHS_BEFORE = 1;
const EXTRA_MONTHS_AFTER = 1;
const FUTURE_EVENTS_ONLY = true;
const MOBILE_BREAKPOINT_PX = 576;

@Component({
  selector: 'app-gastro-events-calendar-provider',
  templateUrl: './gastro-events-calendar-provider.component.html',
  styleUrls: ['./gastro-events-calendar-provider.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [GastroEventsCalendarComponent, GastroExperienceOccasionCardComponent, GastroExperienceOccasionCardComponent],
})
export class GastroEventsCalendarProviderComponent implements OnInit {
  private readonly api = inject(GastroApiService);
  private readonly utils = inject(UtilService);
  private readonly scrollTarget = viewChild<ElementRef<HTMLDivElement>>('scrollTarget');

  /**
   * List of events in a given month.
   */
  readonly monthsData = signal<Record<string, BackendExperienceOccasion[]>>({});
  /**
   * List of dates, this will be passed for the calendar component.
   */
  readonly eventDates = computed(() => {
    const data = this.monthsData();
    let events = Object.values(data).flat();
    if (FUTURE_EVENTS_ONLY) {
      // Previous events of the month won't be displayed on the calendar. (KESMA-21986)
      events = events.filter((event) => {
        return isAfter(new Date(event.startOfExperienceEvent), new Date());
      });
    }
    return events.map((event) => {
      return new Date(event.startOfExperienceEvent);
    });
  });
  /**
   * Events that are on the current day.
   */
  readonly currentEvents = computed(() => {
    const data = this.monthsData();
    let events = Object.values(data).flat();
    if (FUTURE_EVENTS_ONLY) {
      // Previous events of the month won't be displayed on the calendar. (KESMA-21986)
      events = events.filter((event) => {
        return isAfter(new Date(event.startOfExperienceEvent), new Date());
      });
    }
    return events.filter((event) => {
      const backendDate = backendDateToDate(event.startOfExperienceEvent);
      if (backendDate) {
        return isSameDay(backendDate, this.currentDate());
      }
      return false;
    });
  });
  readonly currentEventsCards = computed(() => {
    const events = this.currentEvents();
    return events.map(occasionDetailToOccasionCard);
  });
  private readonly now = new Date() < new Date('2025-10-01') ? new Date('2025-10-01') : new Date();
  private readonly nowYear = this.now.getFullYear();
  private readonly nowMonth = this.now.getMonth() + 1;
  private readonly nowDay = this.now.getDate();
  private init = true;
  readonly currentDate = signal(this.now);
  readonly GastroExperienceOccasionCardType = GastroExperienceOccasionCardType;

  /**
   * Hold the months that are currently being queued to fetch.
   */
  fetchQueue = signal<Month[]>([]);

  handleSelectedDateChanged(date: Date): void {
    this.currentDate.set(date);
    // On Mobile, scroll calendar to the top, so the events are visible.
    if (this.utils.isBrowser() && this.currentEventsCards()?.length && window.innerWidth <= MOBILE_BREAKPOINT_PX) {
      this.scrollTarget()?.nativeElement.scrollIntoView({ behavior: 'smooth' });
    }
  }

  ngOnInit(): void {
    this.handleMonth(this.nowYear, this.nowMonth);
  }

  /*
   * We need to check if the current day or the next days in the current month has any events.
   * If the current day does not have any events, then we should check the following days in the month.
   * If there is an event next in the month, then the selected day should be that one,
   * to immediately show events for the users.
   * */
  handleDefaultDate(): void {
    try {
      // Only do this when the calendar initially loads.
      if (!this.init) {
        return;
      }
      const monthsData = this.monthsData();
      if (!monthsData) {
        // We are only interested in the current month's data.
        return;
      }
      const nowYearMonthString = this.getYearMonthString(this.nowYear, this.nowMonth);
      const currentMonthData = monthsData[nowYearMonthString];
      if (!currentMonthData) {
        return;
      }
      // Filter the events as we only need to check the ones that are on today and after today in the month.
      const eventsAfterIndex = currentMonthData.findIndex((event) => {
        const eventDate = backendDateToDate(event.startOfExperienceEvent);
        return eventDate ? eventDate.getDate() >= this.nowDay : false;
      });
      this.init = false;
      // If there are any events, then we should change the date to that one.
      if (eventsAfterIndex >= 0) {
        const eventDate = backendDateToDate(currentMonthData[eventsAfterIndex].startOfExperienceEvent);
        if (!eventDate) {
          return;
        }
        this.currentDate.set(eventDate);
      }
    } catch (e) {
      // This is not a crucial feature, so it should not block any other functionalities.
      console.error('GastroEventsCalendarProviderComponent > unable to determine default data.');
    }
  }

  /**
   * Adds a given month in a year to the queue to download it's events.
   * It will not download data for months that are already requested and downloaded.
   * @param year
   * @param month
   */
  private addToQueue(year: number, month: number): void {
    // Check if the given month is already in the queue.
    if (this.fetchQueue().some((m) => this.compareMonths(m, { year, month }))) {
      return;
    }

    // Check if the given month is already fetched and downloaded.
    if (Object.keys(this.monthsData()).includes(`${year}-${month}`)) {
      return;
    }

    // Download the events in the month.
    const req$ = this.api.getExperienceOccasionsInMonth(year, month).pipe(
      map((r) => r.data),
      catchError(() => of([] as BackendExperienceOccasion[]))
    );
    this.fetchQueue.set([...this.fetchQueue(), { year, month }]);
    req$.pipe(take(1)).subscribe((data) => {
      this.monthsData.set({
        ...this.monthsData(),
        [this.getYearMonthString(year, month)]: data,
      });
      this.fetchQueue.set(this.fetchQueue().filter((m) => !this.compareMonths(m, { year, month })));
      this.handleDefaultDate();
    });
  }

  /**
   * Creates a string to identify a given month in a year.
   * @param year
   * @param month
   */
  getYearMonthString(year: number, month: number): string {
    return `${year}-${month}`;
  }

  /**
   * Handles retrieving the data for a month. Use this to fetch a given month.
   * It not only fetches the requested month, but you can also issue offsets to also request months before or after
   * the specified month. This way you can enhance the UX by showing additional events and improving load times between
   * month switches.
   * @param year
   * @param month
   */
  handleMonth(year: number, month: number): void {
    const date = new Date(year, month - 1);
    // Fetch future events only. (KESMA-21986)
    if (FUTURE_EVENTS_ONLY && isAfter(new Date(this.nowYear, this.nowMonth - 1), date)) {
      return;
    }
    this.addToQueue(year, month);
    if (EXTRA_MONTHS_BEFORE > 0) {
      for (let i = 1; i <= EXTRA_MONTHS_BEFORE; i++) {
        const modifiedDate = addMonths(date, -i);
        this.addToQueue(modifiedDate.getFullYear(), modifiedDate.getMonth() + 1);
      }
    }

    if (EXTRA_MONTHS_AFTER > 0) {
      for (let i = 1; i <= EXTRA_MONTHS_AFTER; i++) {
        const modifiedDate = addMonths(date, i);
        this.addToQueue(modifiedDate.getFullYear(), modifiedDate.getMonth() + 1);
      }
    }
  }
  compareMonths(a: Month, b: Month): boolean {
    return a.year === b.year && a.month === b.month;
  }
}
