<div class="events-calendar">
  <h2 class="title">Eseménynaptár</h2>
  <div class="day-selector">
    <h3 class="day-selector-day">{{ selectedDate | dfnsFormat: 'yyyy. MMMM' }}</h3>

    <div class="day-selector-buttons">
      <button (click)="handlePreviousButtonClick()" class="button-arrow prev" aria-label="Előző hónap">
        <kesma-icon name="icon-gastro-right-arrow" [size]="20"></kesma-icon>
      </button>
      <button (click)="handleNextButtonClick()" class="button-arrow next" aria-label="Következő hónap">
        <kesma-icon name="icon-gastro-right-arrow" [size]="20"></kesma-icon>
      </button>
    </div>
  </div>

  <div [id]="id"></div>
  @if (eventsListingTemplate) {
    <div class="listing-divider"></div>
    <ng-container *ngTemplateOutlet="eventsListingTemplate"></ng-container>
  }
</div>

<!-- Empty element is needed here, because core component uses this -->
<div #customFooter></div>
