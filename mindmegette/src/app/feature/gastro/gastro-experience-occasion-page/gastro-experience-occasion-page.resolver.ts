import { ResolveFn, Router } from '@angular/router';
import { BackendExperienceOccasionDetails } from '../definitions/experience.definitions';
import { inject } from '@angular/core';
import { catchError, map, share, switchMap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { forkJoin, of, throwError } from 'rxjs';
import { ExperienceRecommendationCard } from '../components/experience-recommendation/definitions/experience-recommendation.definitions';
import { format } from 'date-fns';
import { GastroApiService } from '../shared/gastro-api.service';
import { backendOccasinListToOccasionList } from '../../../shared';

type GastroExperienceOccasionPageResolverResult = {
  details: BackendExperienceOccasionDetails;
  upcoming: ExperienceRecommendationCard[];
};

export const gastroExperienceOccasionPageResolver: ResolveFn<GastroExperienceOccasionPageResolverResult> = (route) => {
  const api = inject(GastroApiService);
  const router = inject(Router);
  const slug = route.params['slug'];
  const now = format(new Date(), 'yyyy-MM-dd HH:mm:ss');

  const details$ = api.getExperienceOccasionDetails(slug).pipe(
    catchError((error: HttpErrorResponse | Error) => {
      router.navigate(['/', '404'], {
        state: { errorResponse: JSON.stringify(error) },
        skipLocationChange: true,
      });
      return throwError(() => error);
    }),
    share()
  );
  const upcoming$ = details$.pipe(
    switchMap((res) => {
      const id = res?.experience.id;
      if (!id) {
        return of([]);
      }
      return api
        .getExperienceOccasionsAuto({
          excludedExperienceId_filter: id,
          'remaining_number_of_seats_order[]': 'asc',
          rowCount_limit: '3',
          occasionDateGreater_filter: now,
          displayComingSoon: '0',
        })
        .pipe(
          map((res) => backendOccasinListToOccasionList(res.data)),
          catchError(() => {
            return [];
          })
        );
    })
  );
  return forkJoin({
    details: details$,
    upcoming: upcoming$,
  });
};
