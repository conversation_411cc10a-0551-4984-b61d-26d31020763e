<section>
  @if (sponsorship(); as sponsorship) {
    <div class="sponsor-stripe" [style.color]="sponsorship.fontColor" [style.background-color]="sponsorship.highlightedColor">
      <span>Az eseményt támogatja: {{ sponsorship.sponsorshipTitle }}</span>
      @if (sponsorship.sponsorshipUrl) {
        <a [href]="sponsorship.sponsorshipUrl" target="_blank">
          <img [src]="sponsorship.sponsorshipLogo" [alt]="sponsorship.sponsorshipTitle" />
        </a>
      } @else {
        <img [src]="sponsorship.sponsorshipLogo" [alt]="sponsorship.sponsorshipTitle" />
      }
    </div>
  }
  <div class="wrapper">
    <main>
      @if (experience()?.featuredImageUrl) {
        <img [src]="experience()?.featuredImageUrl" alt="" class="featured-image-img" />
        @if (experience()?.featuredImageCreator) {
          <div class="featured-image-creator">{{ experience()?.featuredImageCreator }}</div>
        }
      }
      <h1 class="title">{{ experience()?.title }}</h1>
      @if (experience()?.lead) {
        <p class="lead">{{ experience()?.lead }}</p>
      }
      <div class="details">
        <div class="details-meta">
          <div class="details-meta-box">
            <app-gastro-experience-occasion-meta-box [title]="'Az esemény részletei'">
              <div class="details-meta-list">
                @if (mainCategory(); as mc) {
                  <app-gastro-experience-occasion-meta-item
                    icon="icon-utensils"
                    iconTitle="Az élmény fő kategóriája"
                    [label]="mc.title"
                  ></app-gastro-experience-occasion-meta-item>
                }
                @if (occasion()?.duration; as duration) {
                  <app-gastro-experience-occasion-meta-item
                    icon="icon-hourglass"
                    iconTitle="Az élmény időtartama"
                    [label]="duration + ' óra'"
                  ></app-gastro-experience-occasion-meta-item>
                }
                @if (publicAuthor()?.name; as authorName) {
                  <app-gastro-experience-occasion-meta-item
                    icon="icon-chef-hat"
                    iconTitle="Az élményhez tartozó alkalom házigazdája"
                    [label]="authorName"
                  ></app-gastro-experience-occasion-meta-item>
                }
                @if (occasion()?.startOfExperienceEvent; as startOfExperienceEvent) {
                  <app-gastro-experience-occasion-meta-item
                    icon="icon-calendar"
                    iconTitle="Az élményhez tartozó alkalom napja és időpontja"
                    [label]="startOfExperienceEvent | formatDate: 'y-l-d-h-m'"
                  ></app-gastro-experience-occasion-meta-item>
                }
                @if (occasion()?.place; as place) {
                  <app-gastro-experience-occasion-meta-item
                    icon="icon-gastro-location"
                    iconTitle="Az élményhez tartozó alkalom helyszíne"
                    [label]="place"
                  ></app-gastro-experience-occasion-meta-item>
                }
                @if (experience()?.studyAmount?.[0]?.amount; as price) {
                  <app-gastro-experience-occasion-meta-item
                    icon="icon-credit-card"
                    iconTitle="Az élmény ára"
                    [label]="(price | thousandSeparator) + ' Ft/fő'"
                  ></app-gastro-experience-occasion-meta-item>
                }
                <ng-container *ngTemplateOutlet="orderButton; context: { isMobile: false }"></ng-container>
              </div>
            </app-gastro-experience-occasion-meta-box>
          </div>
        </div>
        <div class="details-content">
          <div class="details-content-part">
            <h2>Leírás</h2>
            @for (box of experience()?.content; track box.id) {
              @switch (box?.type) {
                @case (ArticleBodyType.Infobox) {
                  <app-gastro-experience-occasion-info-box [infoBoxDetails]="box?.details"></app-gastro-experience-occasion-info-box>
                }
                @default {
                  <mindmegette-wysiwyg-box [html]="box.details?.[0].value" trArticleFileLink></mindmegette-wysiwyg-box>
                }
              }
            }
          </div>
          @if (publicAuthor(); as pa) {
            <div class="details-content-part">
              <h2>Házigazda</h2>
              <app-gastro-experience-public-author [publicAuthor]="publicAuthor()"></app-gastro-experience-public-author>
            </div>
          }
          @if (nextOccasions(); as nextOccasionItems) {
            @if (nextOccasionItems.length) {
              <div class="details-content-part" #nextOccasions>
                <div class="scroll-target-wrapper">
                  <div [id]="hasSpaceForAnotherOccasionFragment() ? fragment() : null"></div>
                </div>
                <h2>Ugyanez az esemény más időpontban</h2>
                <div class="details-content-part-next-occasions">
                  @for (nextOccasion of nextOccasionItems; track nextOccasion.occasion.id) {
                    <app-gastro-experience-occasion-card
                      [showDate]="true"
                      [occasion]="mapBackendExperienceOccasionDetailsToOccasionList(nextOccasion, details())"
                    ></app-gastro-experience-occasion-card>
                  }
                </div>
              </div>
            }
          }
          <div class="details-content-part">
            <app-article-social-share [emailSubject]="experience()?.title!" class="details-content-part-share"></app-article-social-share>
            <div class="details-content-part-tags">
              @if (otherCategories()) {
                <app-gastro-experience-occasion-tags [tags]="tags()"></app-gastro-experience-occasion-tags>
              }
            </div>
          </div>
          @if (upcomingExperiences(); as ue) {
            @if (ue.length) {
              <div class="details-content-part">
                <app-upcoming-events [data]="ue" [title]="'Ne maradj le ezekről sem'"></app-upcoming-events>
              </div>
            }
          }
          <div class="details-content-part">
            <mindmegette-newsletter [styleID]="MindmegetteNewsletterCardType.Gastro"></mindmegette-newsletter>
          </div>
          <div class="details-content-part">
            <app-gastro-experience></app-gastro-experience>
          </div>
          <div #experienceForm class="details-content-part">
            <div class="scroll-target-wrapper">
              <div [id]="!hasSpaceForAnotherOccasionFragment() ? fragment() : null"></div>
            </div>
            @if (details()?.occasion?.startOfExperienceEvent && occasion()?.remainingNumberOfSeats) {
              <app-gastro-experience-purchase-box [details]="details()"></app-gastro-experience-purchase-box>
            } @else {
              <app-gastro-experience-occasion-notification-form [experienceInput]="details()?.experience"></app-gastro-experience-occasion-notification-form>
            }
          </div>
        </div>
      </div>
    </main>
    <aside>
      @if (sidebar(); as sb) {
        <app-layout
          [layoutType]="LayoutPageType.SIDEBAR"
          [adPageType]="adPageType"
          [structure]="$any(sb.struct)"
          [isGastroPage]="true"
          [configuration]="sb.content"
        ></app-layout>
      }
    </aside>
  </div>
  <ng-container *ngTemplateOutlet="orderButton; context: { isMobile: true }"></ng-container>
</section>

<ng-template #orderButton let-isMobile="isMobile">
  <mindmegette-simple-button
    (click)="orderButtonClicked()"
    (keydown)="orderButtonClicked()"
    [class.mobile]="isMobile"
    [class.desktop]="!isMobile"
    class="order-button"
    color="primary"
    icon="mindmegette-icon-white-right-arrow"
    iconPosition="right"
    wide="true"
  >
    {{ details()?.occasion?.startOfExperienceEvent && occasion()?.remainingNumberOfSeats ? 'Megrendelem' : 'Értesítést kérek' }}
  </mindmegette-simple-button>
</ng-template>
@if (isOutOfSpacePopupVisible()) {
  <app-out-of-space-popup
    [hasSpaceForAnotherOccasion]="hasSpaceForAnotherOccasion()"
    (notifyMeClicked)="notifyMeClicked()"
    (eventsListClicked)="eventsListClicked()"
    (anotherDateClicked)="anotherDateClicked()"
    (closeClicked)="closeOutOfSpacePopup()"
  />
}
