import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, effect, ElementRef, inject, signal, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map } from 'rxjs/operators';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { NgTemplateOutlet } from '@angular/common';
import { BackendExperienceOccasionDetails, BackendExperienceOccasionList } from '../definitions/experience.definitions';
import { FormatDatePipe, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  ArticleBodyType,
  ArticleFileLinkDirective,
  createCanonicalUrlForPageablePage,
  Layout,
  LayoutPageType,
  PAGE_TYPES,
  ThousandSeparatorPipe,
} from '@trendency/kesma-ui';
import {
  ArticleSocialShareComponent,
  createMMETitle,
  defaultMetaInfo,
  MindmegetteNewsletterCardType,
  MindmegetteSimpleButtonComponent,
  MindmegetteWysiwygBoxComponent,
  NewsletterComponent,
  UpcomingEventsComponent,
} from '../../../shared';
import { mapBackendExperienceOccasionDetailsToOccasionList } from '../utils/experience-detail-to-list-item';
import { environment } from '../../../../environments/environment';
import { getStructuredDataForExperienceOccasion } from '../utils/gastro-schema';
import { GastroExperienceOccasionInfoBoxComponent } from './components/gastro-experience-occasion-info-box/gastro-experience-occasion-info-box.component';
import { GastroExperienceOccasionNotificationFormComponent } from '../gastro-experience-occasion-notification-form/gastro-experience-occasion-notification-form.component';
import { LayoutComponent } from '../../layout/components/layout/layout.component';
import {
  GastroExperienceOccasionMetaBoxComponent,
  GastroExperienceOccasionMetaItemComponent,
  GastroExperienceOccasionTagsComponent,
  GastroExperiencePublicAuthorComponent,
  GastroExperiencePurchaseBoxComponent,
} from './components';
import { OutOfSpacePopupComponent } from '../components/out-of-space-popup/out-of-space-popup.component';
import { GastroExperienceComponent } from '../components/gastro-experience/gastro-experience.component';
import { GastroExperienceOccasionCardComponent } from '../components/gastro-experience-occasion-card/gastro-experience-occasion-card.component';

enum FRAGMENTS {
  APPLICATION_FORM = 'jelentkezes',
  NOTIFICATION_FORM = 'ertesites',
}

@Component({
  selector: 'app-gastro-experience-occasion-page',
  imports: [
    NgTemplateOutlet,
    GastroExperienceOccasionMetaBoxComponent,
    GastroExperienceOccasionMetaItemComponent,
    GastroExperienceOccasionTagsComponent,
    GastroExperiencePublicAuthorComponent,
    OutOfSpacePopupComponent,
    GastroExperienceComponent,
    GastroExperiencePurchaseBoxComponent,
    UpcomingEventsComponent,
    GastroExperienceOccasionNotificationFormComponent,
    GastroExperienceOccasionInfoBoxComponent,
    ThousandSeparatorPipe,
    MindmegetteWysiwygBoxComponent,
    ArticleFileLinkDirective,
    ArticleSocialShareComponent,
    NewsletterComponent,
    LayoutComponent,
    MindmegetteSimpleButtonComponent,
    GastroExperienceOccasionCardComponent,
    FormatDatePipe,
  ],
  templateUrl: './gastro-experience-occasion-page.component.html',
  styleUrl: './gastro-experience-occasion-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GastroExperienceOccasionPageComponent implements AfterViewInit {
  private readonly utils = inject(UtilService);
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly router = inject(Router);
  private readonly destroyRef = inject(DestroyRef);
  private readonly schemaService = inject(SchemaOrgService);

  readonly isOutOfSpacePopupVisible = signal(false);
  readonly experience = computed(() => this.details()?.experience);
  readonly mainCategory = computed(() => this.details()?.mainCategory);
  readonly otherCategories = computed(() => this.details()?.otherCategories);
  readonly tags = computed(() => this.otherCategories()?.map(({ id, title }) => ({ id, label: title })));
  readonly publicAuthor = computed(() => this.details()?.publicAuthor);
  readonly occasion = computed(() => this.details()?.occasion);
  readonly sponsorship = computed(() => this.details()?.sponsorship);
  readonly nextOccasions = computed(() => this.details()?.nextOccasions.filter((item) => item.occasion.id !== this.occasion()?.id));
  readonly hasSpaceForAnotherOccasion = computed(() => this.nextOccasions()?.some((o) => o.occasion.remainingNumberOfSeats > 0));
  readonly details = toSignal<BackendExperienceOccasionDetails>(this.route.data.pipe(map((data) => data['data']['details'])), {
    requireSync: true,
  });
  readonly sidebar = toSignal<Layout | null>(this.route.data.pipe(map((data) => data['sidebar']?.['data'])), {
    requireSync: true,
  });
  readonly upcomingExperiences = toSignal<BackendExperienceOccasionList[]>(this.route.data.pipe(map((data) => data['data']['upcoming'])), {
    requireSync: true,
  });
  readonly fragment = signal<string | null>(null);
  readonly isApplicationForm = computed(() => this.fragment() === FRAGMENTS.APPLICATION_FORM);
  readonly hasSpaceForAnotherOccasionFragment = computed(() => !this.isApplicationForm() && this.hasSpaceForAnotherOccasion());
  readonly LayoutPageType = LayoutPageType;
  readonly MindmegetteNewsletterCardType = MindmegetteNewsletterCardType;
  ArticleBodyType = ArticleBodyType;

  protected readonly mapBackendExperienceOccasionDetailsToOccasionList = mapBackendExperienceOccasionDetailsToOccasionList;

  @ViewChild('experienceForm', { read: ElementRef })
  private readonly experienceFormElement?: ElementRef;
  @ViewChild('nextOccasions', { read: ElementRef })
  private readonly nextOccasionsElement?: ElementRef;

  adPageType = PAGE_TYPES.other_pages;

  constructor() {
    effect(() => {
      const experience = this.experience();
      if (!experience) {
        return;
      }
      const canonical = createCanonicalUrlForPageablePage('elmenyek/esemenyek', this.route.snapshot);
      if (canonical) this.seo.updateCanonicalUrl(canonical);
      const title: string = createMMETitle(experience.title);
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title,
        ogTitle: title,
        ogImage: experience.featuredImageUrl || '',
        ogDescription: experience.lead || '',
        description: experience.lead || '',
      });
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(getStructuredDataForExperienceOccasion(this.details(), environment?.siteUrl ?? ''));
    });
    effect(() => {
      const occasion = this.occasion();
      const fragment = this.route.snapshot.fragment;
      if (occasion.remainingNumberOfSeats === 0 && !fragment) {
        this.isOutOfSpacePopupVisible.set(true);
      }
    });
  }

  ngAfterViewInit(): void {
    if (!this.utils.isBrowser()) {
      return;
    }
    this.route.fragment.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((fragment) => {
      this.fragment.set(fragment);
      if (fragment === FRAGMENTS.NOTIFICATION_FORM && this.hasSpaceForAnotherOccasion()) {
        this.scrollToNextOccasionsList();
        return;
      }
      if (fragment) {
        this.scrollToExperienceForm();
      }
    });
  }

  orderButtonClicked(): void {
    if (!this.utils.isBrowser()) {
      return;
    }
    this.scrollToExperienceForm();
  }

  eventsListClicked(): void {
    this.router.navigate(['/elmenyek/esemenyek']).then();
  }

  notifyMeClicked(): void {
    this.router.navigate(['/', 'elmenyek', 'esemenyek', 'ertesites', this.occasion()?.slug]).then();
  }

  anotherDateClicked(): void {
    this.isOutOfSpacePopupVisible.set(false);
    if (!this.utils.isBrowser()) {
      return;
    }
    this.scrollToNextOccasionsList();
  }

  closeOutOfSpacePopup(): void {
    this.isOutOfSpacePopupVisible.set(false);
  }

  private scrollToExperienceForm(): void {
    if (this.experienceFormElement?.nativeElement) {
      this.experienceFormElement.nativeElement.scrollIntoView({ behavior: 'smooth' });
    }
  }

  private scrollToNextOccasionsList(): void {
    if (this.nextOccasionsElement?.nativeElement) {
      this.nextOccasionsElement.nativeElement.scrollIntoView({ behavior: 'smooth' });
    }
  }
}
