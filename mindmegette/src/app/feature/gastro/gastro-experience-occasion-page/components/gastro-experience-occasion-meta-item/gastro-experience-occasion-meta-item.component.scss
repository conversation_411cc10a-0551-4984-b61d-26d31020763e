@use 'shared' as *;

:host {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  &:hover {
    .icon {
      background: var(--kui-gray-150);
    }
  }
  .icon {
    padding: var(--spacing-4, 4px);
    width: 28px;
    height: 28px;
    border-radius: 24px;
    border: 1px solid var(--kui-gray-100);
    background: var(--kui-orange-50);
    color: var(--kui-orange-600);
    transition: 0.2s background-color;
    flex-shrink: 0;
  }
  .label {
    color: var(--kui-gray-950);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 137.5% */
    letter-spacing: 0.16px;
  }
}
