import { GastroPurchaseSessionData } from '../definitions/gastro-purchase.definitions';
import { BackendOrderItemDiscriminator, BackendOrderPurchaseSaveRequest } from '@trendency/kesma-ui';

export function gastroPurchaseSessionDataToBackendRequest(data: GastroPurchaseSessionData): BackendOrderPurchaseSaveRequest {
  return {
    orderItems: [
      {
        id: data?.occasion,
        discriminator: BackendOrderItemDiscriminator.EXPERIENCE_OCCASION,
        quantity: data?.quantity,
        isGift: data?.isGift,
      },
      ...(data.coupon
        ? [
            {
              couponCode: data.coupon.couponCode,
              discriminator: BackendOrderItemDiscriminator.COUPON,
              quantity: 1,
            },
          ]
        : []),
    ],
    orderDetail: {
      invoiceName: data?.invoiceName,
      invoiceZip: data?.invoiceZip,
      invoiceCity: data?.invoiceCity,
      invoiceAddress: data?.invoiceAddress,
      taxNumber: data?.taxNumber ?? undefined,
      phoneNumber: data.phoneNumber,
    },
    orderComment: data?.orderComment ?? '',
    marketingLetter: data?.marketing,
  };
}
