import { FakeBool, FocusPointUrlWithAspectRatio } from '@trendency/kesma-ui';

export type BackendExperienceOccasion = Readonly<{
  id: string;
  remaining_number_of_seats?: string;
  featuredImageUrl?: string;
  title: string;
  startOfExperienceEvent: string;
  lead?: string;
  publicAuthorId: string;
  highlightedColor?: string;
  fontColor?: string;
  occasionSlug?: string;
  occasionSug?: string;
  sponsorshipLogo?: string;
  sponsorshipUrl?: string;
  isComingSoon: FakeBool;
  publicAuthorFullName?: string;
  publicAuthorIsMaestroAuthor?: FakeBool;
  publicAuthorSlug?: string;
  featuredImageUrlFocusedImages: FocusPointUrlWithAspectRatio;
  publicAuthorAvatar?: {
    fullSizeUrl?: string;
    thumbnailUrl?: string;
  };
  publicAuthor?: {
    avatarFullSizeUrl?: string;
    fullName?: string;
    slug?: string;
    isMaestroAuthor?: FakeBool;
  };
}>;
