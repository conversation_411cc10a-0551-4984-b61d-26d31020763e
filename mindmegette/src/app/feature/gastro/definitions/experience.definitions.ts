import { ArticleBody, FakeBool, FocusPointUrlWithAspectRatio } from '@trendency/kesma-ui';
import { RecipePublicAuthor } from '@trendency/kesma-ui/lib/definitions/recipe.definitions';

export type BackendExperienceOccasion = Readonly<{
  id: string;
  remaining_number_of_seats?: string;
  featuredImageUrl?: string;
  title: string;
  startOfExperienceEvent: string;
  lead?: string;
  publicAuthorId: string;
  highlightedColor?: string;
  fontColor?: string;
  occasionSlug?: string;
  sponsorshipLogo?: string;
  isComingSoon: FakeBool;
  publicAuthorFullName?: string;
  publicAuthorSlug?: string;
  publicAuthorIsMaestroAuthor?: FakeBool;
  featuredImageUrlFocusedImages: FocusPointUrlWithAspectRatio;
  publicAuthorAvatar?: {
    fullSizeUrl?: string;
    thumbnailUrl?: string;
  };
}>;

export type BackendHighlightedExperienceOccasion = Readonly<{
  occasionId: string;
  experienceId: string;
  title: string;
  startOfExperienceEvent?: string;
  isComingSoon: FakeBool;
  featuredImageUrl?: string;
  featuredImageUrlFocusedImages: FocusPointUrlWithAspectRatio;
  occasionSlug: string;
}>;

export type BackendExperienceCategory = Readonly<{
  id: string;
  title: string;
}>;
export type BackendExperienceOccasionPublicAuthor = Readonly<{
  id: string;
  name: string;
  slug: string;
  avatar: string | null;
  rank: string | null;
  introduction: string | null;
  isMaestroAuthor: boolean;
}>;
export type BackendExperienceStudyAmount = Readonly<{
  studentNumber: number;
  amount: number;
}>;
export type BackendExperienceOccasionDetailsExperience = Readonly<{
  id: string;
  slug: string;
  featuredImageUrl: string | null;
  featuredImageCreator: string | null;
  title: string;
  lead: string | null;
  studyAmount: BackendExperienceStudyAmount[];
  content: ArticleBody[];
}>;
export type BackendExperienceOccasionDetailsOccasion = Readonly<{
  id: string;
  slug: string;
  duration: string;
  place: string | null;
  startOfExperienceEvent: string | null;
  remainingNumberOfSeats: number;
}>;
export type BackendExperienceOccasionSponsorship = Readonly<{
  fontColor: string;
  highlightedColor: string;
  sponsorshipLogo: string;
  sponsorshipUrl: string;
  sponsorshipTitle: string;
}>;
export type BackendExperienceOccasionPartialDetails = Readonly<{
  occasion: BackendExperienceOccasionDetailsOccasion;
  publicAuthor?: BackendExperienceOccasionPublicAuthor;
  sponsorship?: BackendExperienceOccasionSponsorship;
}>;
export type BackendExperienceOccasionDetails = Readonly<
  {
    experience: BackendExperienceOccasionDetailsExperience;
    mainCategory: BackendExperienceCategory;
    otherCategories: BackendExperienceCategory[];
    nextOccasions: BackendExperienceOccasionPartialDetails[];
  } & BackendExperienceOccasionPartialDetails
>;
export type BackendExperienceOccasionList = Readonly<{
  id: string;
  slug: string;
  startOfExperienceEvent: string;
  isComingSoon: FakeBool;
  place?: string;
  duration?: string;
  remainingNumberOfSeats: string;
  maxNumberOfSeats: string;
  isActive: boolean;
  isDeleted: FakeBool;
  createdAt: Date;
  updatedAt: Date;
  experienceTitle: string;
  experienceLead?: string;
  experienceMaxStudentNumber?: number;
  publicAuthorFullName?: string;
  publicAuthorSlug?: string;
  featuredImageUrl?: string;
}>;

export type BackendExperienceOccasionListItem = Readonly<{
  experienceId: string;
  experienceTitle: string;
  experienceLead?: string;
  experienceSlug: string;
  occasionId: string;
  occasionStartOfExperienceEvent?: string;
  occasionSlug: string;
  occasionIsComingSoon?: FakeBool;
  occasionPlace?: string;
  occasionDuration?: string;
  maxNumberOfSeats?: string;
  remainingNumberOfSeats: string;
  featuredImage?: string;
  publicAuthor?: RecipePublicAuthor;
}>;

export type BackendSimpleExperience = Readonly<{
  id: string;
  title: string;
  slug: string;
}>;

export interface NotificationFormData {
  lastName: string;
  firstName: string;
  email: string;
  experienceId: string;
}

export enum ExperienceOccasionCard {
  AuthorExperienceOccasion,
}
