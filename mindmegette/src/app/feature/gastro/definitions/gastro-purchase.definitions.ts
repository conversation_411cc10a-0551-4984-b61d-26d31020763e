import { BackendCoupon } from '@trendency/kesma-ui';

export enum GastroPurchaseStepType {
  PRODUCT = 1,
  BILLING = 2,
  SUMMARY = 3,
}

export interface GastroPurchaseSessionData extends GastroPurchaseProductStepData, GastroPurchaseBillingStepData, GastroPurchaseSummaryStepData {
  nextStep?: GastroPurchaseStepType;
  orderId?: string;
  slug?: string;
}

export interface GastroPurchaseProductStepData {
  occasion?: string;
  quantity?: number;
  isGift?: boolean;
  orderComment?: string;
  terms?: boolean;
  marketing?: boolean;
}

export interface GastroPurchaseBillingStepData {
  invoiceName?: string;
  invoiceZip?: string;
  invoiceCity?: string;
  invoiceAddress?: string;
  taxNumber?: string;
  phoneNumber?: string;
}

export interface GastroPurchaseSummaryStepData {
  coupon?: BackendCoupon;
}
