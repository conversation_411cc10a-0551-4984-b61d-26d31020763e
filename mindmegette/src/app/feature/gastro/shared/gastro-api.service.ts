import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResult } from '@trendency/kesma-ui';
import {
  BackendExperienceOccasionDetails,
  BackendExperienceOccasionList,
  BackendHighlightedExperienceOccasion,
  BackendSimpleExperience,
  NotificationFormData,
} from '../definitions/experience.definitions';
import { BackendExperienceOccasion } from '../definitions/occasion-definitions';

@Injectable({
  providedIn: 'root',
})
export class GastroApiService {
  private readonly reqService = inject(ReqService);

  /**
   * Creates string form a given query params object. This is used to create a query string that already contains
   * the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param params
   * @private
   */
  private paramsToString(params: Record<string, string | string[] | undefined | null>): string {
    const paramsPrepared: Record<string, string>[] = [];
    if (params) {
      Object.keys(params).map((key) => {
        const value = params[key];
        if (value === undefined || value === null) {
          return;
        }
        if (Array.isArray(value)) {
          value.forEach((item) => {
            // Add [] to end of array params, but only if it is not already there.
            const itemKey = key.endsWith(']') ? key : `${key}[]`;
            paramsPrepared.push({ [itemKey]: encodeURIComponent(item) });
          });
          return;
        }
        paramsPrepared.push({ [key]: encodeURIComponent(value) });
      });
    }
    const paramsString = paramsPrepared
      .map((item) =>
        Object.keys(item)
          .map((key) => `${key}=${item[key]}`)
          .join('&')
      )
      .join('&');
    return paramsString;
  }
  /**
   * Creates a query string from the given query params and extra params. This is used to create a query string that
   * already contains the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param queryParams
   * @param extraParams
   * @private
   */
  private searchParamsToString(queryParams: Record<string, string | string[] | undefined | null>, extraParams?: Record<string, string | string[]>): string {
    const queryParamsString = queryParams ? this.paramsToString(queryParams) : '';
    const extraParamsString = extraParams ? this.paramsToString(extraParams) : '';

    return queryParamsString?.length && extraParamsString?.length ? `${queryParamsString}&${extraParamsString}` : queryParamsString || extraParamsString;
  }

  getExperienceOccasionById(id: string): Observable<ApiResult<[BackendExperienceOccasion]>> {
    return this.reqService.get<ApiResult<[BackendExperienceOccasion]>>(`gastro/layout/explicit-by-id/${id}`);
  }
  getExperienceOccasionDetails(slug: string): Observable<BackendExperienceOccasionDetails> {
    return this.reqService.get<BackendExperienceOccasionDetails>(`gastro/experience-occasion/details/${slug}`);
  }
  getExperienceOccasionList(params: Record<string, string | string[] | undefined | null>): Observable<ApiResult<BackendExperienceOccasionList[]>> {
    const paramsAsString = this.searchParamsToString(params);
    return this.reqService.get<ApiResult<BackendExperienceOccasionList[]>>(`gastro/experience/occasion/list${paramsAsString ? `?${paramsAsString}` : ''}`);
  }
  getExperienceOccasionsInMonth(year: number, month: number): Observable<ApiResult<BackendExperienceOccasion[]>> {
    return this.reqService.get<ApiResult<BackendExperienceOccasion[]>>(`gastro/layout/explicit-by-year-and-month/${year}/${month}`);
  }
  getExperienceOccasionsAutoByCategory(categoryId: string, params: Record<string, string | string[]>): Observable<ApiResult<BackendExperienceOccasion[]>> {
    const paramsAsString = this.searchParamsToString(params);
    return this.reqService.get<ApiResult<BackendExperienceOccasion[]>>(
      `gastro/layout/auto-by-category/${categoryId}${paramsAsString ? `?${paramsAsString}` : ''}`
    );
  }
  getExperienceOccasionsAutoByMultipleCategories(
    categoryIds: string[],
    params: Record<string, string | string[]>
  ): Observable<ApiResult<BackendExperienceOccasion[]>> {
    const idParams = this.searchParamsToString({ 'ids[]': categoryIds });
    const paramsAsString = this.searchParamsToString(params);
    return this.reqService.get<ApiResult<BackendExperienceOccasion[]>>(
      `gastro/layout/auto-by-categories${idParams ? `?${idParams}` : ''}${paramsAsString ? `&${paramsAsString}` : ''}`
    );
  }
  getExperienceOccasionsAuto(params: Record<string, string | string[]>): Observable<ApiResult<BackendExperienceOccasion[]>> {
    const paramsAsString = this.searchParamsToString(params);
    return this.reqService.get<ApiResult<BackendExperienceOccasion[]>>(`gastro/layout/auto-occasion-list${paramsAsString ? `?${paramsAsString}` : ''}`);
  }
  getExperienceOccasionsAutoByExperience(experienceId: string, params: Record<string, string | string[]>): Observable<ApiResult<BackendExperienceOccasion[]>> {
    const paramsAsString = this.searchParamsToString(params);
    return this.reqService.get<ApiResult<BackendExperienceOccasion[]>>(
      `gastro/layout/auto-by-experience/${experienceId}${paramsAsString ? `?${paramsAsString}` : ''}`
    );
  }
  getHighlightedExperienceOccasions(): Observable<ApiResult<BackendHighlightedExperienceOccasion[]>> {
    return this.reqService.get<ApiResult<BackendHighlightedExperienceOccasion[]>>('gastro/experience-occasion/highlighted-in-tape-list');
  }
  getExperienceList(): Observable<ApiResult<BackendSimpleExperience[]>> {
    return this.reqService.get<ApiResult<BackendSimpleExperience[]>>('source/gastro/experience', {
      params: { rowCount_limit: 1000 },
    });
  }
  getExperienceCategories(): Observable<ApiResult<{ id: string; title: string }[]>> {
    return this.reqService.get<ApiResult<{ id: string; title: string }[]>>('source/gastro/experience-categories');
  }
  getOccasionAuthors(): Observable<ApiResult<object[]>> {
    return this.reqService.get<ApiResult<object[]>>(`source/gastro/occasion/public-authors`);
  }
  subscribeToNotifications(formData: NotificationFormData): Observable<{ status: boolean }> {
    return this.reqService.post<{ status: boolean }>(`gastro/experience/notification/subscribe`, formData);
  }
}
