import { NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MindmegetteSimpleButtonComponent } from '@shared/components';
import { emailValidator, IconComponent, KesmaFormControlComponent, markControlsTouched, websiteUrlValidator } from '@trendency/kesma-ui';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { environment } from '../../../environments/environment';
import { ApiService } from '@shared/services';
import { finalize } from 'rxjs';
@Component({
  selector: 'app-complaint-handling',
  templateUrl: './complaint-handling.component.html',
  styleUrls: ['./complaint-handling.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, FormsModule, IconComponent, ReactiveFormsModule, KesmaFormControlComponent, MindmegetteSimpleButtonComponent],
})
export class ComplaintHandlingComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly reCaptchaV3Service = inject(ReCaptchaV3Service);
  private readonly apiService = inject(ApiService);
  private readonly router = inject(Router);

  isSaving = signal(false);
  isError = signal(false);
  formGroup: UntypedFormGroup;

  ngOnInit(): void {
    this.initForm();
  }
  save(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.isSaving.set(true);
    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_digital_services_act_form',
      (recaptchaToken: string) => {
        this.sendMail(recaptchaToken);
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.isSaving.set(false);
      }
    );
  }
  initForm(): void {
    this.formGroup = this.formBuilder.group({
      url: [null, [websiteUrlValidator, Validators.required, Validators.maxLength(300)]],
      complaint: [null, [Validators.required, Validators.maxLength(500)]],
      name: [null, [Validators.maxLength(300)]],
      email: [null, [Validators.required, emailValidator]],
      isCrime: [false],
      consentCheckbox: [false, [Validators.requiredTrue]],
    });

    this.formGroup
      .get('isCrime')
      ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        if (value) {
          this.formGroup.controls['email'].removeValidators([Validators.required]);
          this.formGroup.controls['name'].removeValidators([Validators.required]);
        } else {
          this.formGroup.controls['email'].addValidators([Validators.required]);
          this.formGroup.controls['name'].addValidators([Validators.required]);
        }
        this.formGroup.controls['email'].updateValueAndValidity();
        this.formGroup.controls['name'].updateValueAndValidity();
      });
  }

  requiredIf(field: string) {
    return (control: AbstractControl): { required: boolean } | null => {
      const form = control.parent as FormGroup;
      const check = form ? form.get(field) : null;
      if (form && check?.value) return !control.value ? { required: true } : null;

      return null;
    };
  }

  sendMail(recaptchaToken: string): void {
    this.isSaving.set(true);
    this.isError.set(false);
    this.apiService
      .sendComplain(this.formGroup.value, recaptchaToken)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => this.isSaving.set(false))
      )
      .subscribe({
        next: () => {
          this.router.navigate(['/panaszkezeles/sikeres-panasz-bekuldes']);
        },
        error: () => {
          this.isError.set(true);
        },
      });
  }
}
