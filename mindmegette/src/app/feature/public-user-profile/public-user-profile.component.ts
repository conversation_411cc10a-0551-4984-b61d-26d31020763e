import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PublicUserProfileDetails } from './public-user-profile.definitions';
import { of, Subject } from 'rxjs';
import { catchError, map, switchMap, takeUntil } from 'rxjs/operators';
import { SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  MindmegetteAvatarInitialsComponent,
  MindmegetteAvatarInitialTypes,
  MindmegetteSimpleButtonComponent,
  ProfileBadgesComponent,
  ProfileOwnRecipesComponent,
  publicUserPageMetaInfo,
  RecipeCardType,
  ScreenWidthService,
  SecureApiService,
} from '../../shared';
import { createCanonicalUrlForPageablePage, LimitableMeta, User } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-public-user-profile',
  templateUrl: 'public-user-profile.component.html',
  styleUrls: ['public-user-profile.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ProfileBadgesComponent, ProfileOwnRecipesComponent, MindmegetteAvatarInitialsComponent, MindmegetteSimpleButtonComponent],
})
export class PublicUserProfileComponent implements OnInit, OnDestroy {
  publicUser?: PublicUserProfileDetails;
  publicUserID?: string;
  isMyProfile? = false;
  uploadedRecipes?: [];
  hasAnyFavoriteData?: boolean;
  hasAnyGreatestData?: boolean;
  limitable?: LimitableMeta;
  isDesktopView = false;
  isFollowed = false;

  readonly #destroy$: Subject<boolean> = new Subject();

  readonly MindmegetteNameInitialType = MindmegetteAvatarInitialTypes;
  readonly RecipeCardType = RecipeCardType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly screenWidthService: ScreenWidthService,
    private readonly authService: AuthService,
    private readonly secureApiService: SecureApiService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.#destroy$)).subscribe(
      ({
        data: {
          publicUserID,
          publicUser,
          uploadedRecipes: { data, meta },
        },
      }) => {
        this.publicUserID = publicUserID;
        this.publicUser = publicUser;
        this.uploadedRecipes = data;
        this.limitable = meta?.limitable;

        const additional = this.publicUser?.additionalDetails;
        this.hasAnyFavoriteData = Boolean(additional?.favoriteMeal || additional?.favoriteChef || additional?.favoriteSpice || additional?.favoriteIngredient);
        this.hasAnyGreatestData = Boolean(additional?.greatestSuccess || publicUser?.greatestFailure);

        this.setMetaData();
        this.cdr.markForCheck();
      }
    );
    this.screenWidthService.isDesktopWidth$.subscribe((res: boolean) => (this.isDesktopView = res));

    this.authService.currentUserSubject
      .pipe(
        switchMap((user: User | undefined) => {
          return this.secureApiService.getMyFollowedProfiles({ rowCount_limit: 500 }).pipe(
            catchError(() => of({ data: [] })),
            map((followedUsers) => {
              if (this.publicUserID === user?.uid) {
                this.isMyProfile = true;
                return;
              }
              this.isFollowed = followedUsers.data.filter((followedUser) => followedUser.id === this.publicUserID).length > 0;
            })
          );
        })
      )
      .subscribe(() => {
        this.cdr.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next(true);
    this.#destroy$.complete();
  }

  private setMetaData(): void {
    const page = (this.limitable?.pageCurrent || 0) + 1;
    const { userId } = this.route.snapshot.params;
    const canonical = createCanonicalUrlForPageablePage(`profil/${userId}`, this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    this.seo.setMetaData(publicUserPageMetaInfo(this.publicUser?.userName, page));
  }

  onFollowPublicUser(): void {
    this.authService
      .isAuthenticated()
      .pipe(
        switchMap((isLoggedIn: boolean) => {
          if (!isLoggedIn) {
            return this.router.navigate([`/bejelentkezes`]);
          } else {
            return this.secureApiService.followPublicUser(this.publicUserID);
          }
        })
      )
      .subscribe(() => {
        this.isFollowed = true;
        if (this.publicUser) {
          this.publicUser.follower++;
        }
        this.cdr.markForCheck();
      });
  }

  unfollowUserProfile(): void {
    this.secureApiService.unFollowPublicUser(this.publicUserID).subscribe(() => {
      this.isFollowed = false;
      if (this.publicUser) {
        this.publicUser.follower--;
      }
      this.cdr.markForCheck();
    });
  }
}
