import { Routes } from '@angular/router';
import { PublicUserProfileComponent } from './public-user-profile.component';
import { PublicUserProfileResolver } from './public-user-profile.resolver';

export const publicUserProfileRouting: Routes = [
  {
    path: '',
    component: PublicUserProfileComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: {
      data: PublicUserProfileResolver,
    },
  },
];
