export enum RecipeSubmitStepType {
  INGREDIENTS = 1,
  WORKFLOW = 2,
  DETAILS = 3,
}

export interface RecipeSubmitStep {
  type: RecipeSubmitStepType;
  name: string;
}

export interface RecipeSubmitSessionData extends RecipeSubmitIngredientsData, RecipeSubmitWorkflowData, RecipeSubmitDetailsData {
  nextStep?: RecipeSubmitStepType;
}

export interface RecipeSubmitIngredientsData {
  recipeName?: string;
  ingredientGroups?: RecipeSubmitIngredientGroup[];
}

export interface RecipeSubmitWorkflowData {
  workflow?: RecipeSubmitWorkflow[];
}

export interface RecipeSubmitDetailsData {
  portion?: number;
  difficulty?: string;
  cost?: string;
  comment?: string;
  prepareTime?: number;
  cookTime?: number;
  totalTime?: number;
  otherTimes?: RecipeSubmitOtherTime[];
  images?: any;
  shouldSendNotification?: boolean;
  isNationalDish2025?: boolean;
}

export interface RecipeSubmitIngredientGroup {
  ingredientGroupName?: string;
  isOpened?: boolean;
  ingredients?: RecipeSubmitIngredient[];
}

export interface RecipeSubmitIngredient {
  ingredient?: RecipeSubmitIngredientObject;
  quantity?: number;
  unit?: string;
}

export interface RecipeSubmitIngredientObject {
  title: string;
  slug: string | null;
}

export interface RecipeSubmitWorkflow {
  stepText?: string;
  isOpened?: boolean;
  isInEdit?: boolean;
}

export enum RecipeSubmitDifficulty {
  SIMPLE = 'Egyszerű &#127859;',
  COMPLEX = 'Bonyolult &#127858;',
  REFINED = 'Rafinált &#127843;',
  MASTER = 'Mester &#129489;&#8205;&#127859;',
}

export enum RecipeSubmitCost {
  CHEAP = 'Olcsó &#128181;',
  AVERAGE = 'Átlagos &#128184;',
  AFFORDABLE = 'Megfizethető &#129297;',
  EXPENSIVE = 'Költséges &#128176;',
}

export interface RecipeSubmitOtherTime {
  timeName?: string;
  time?: number;
}
