@use 'shared' as *;
@use '../recipe-submit.component' as *;

::ng-deep {
  .recipe-submit-other-time {
    .form-error {
      margin: -8px 0 16px 0 !important;
    }

    .mindmegette-form-select {
      margin-bottom: 16px;
    }
  }
}

.recipe-submit {
  mindmegette-button-selector {
    margin-bottom: 24px;
  }

  &-form-info-text {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 32px;

    .icon {
      width: 20px;
      height: 20px;
    }

    span {
      flex: 1;
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0.12px;
      color: var(--kui-gray-600);
    }
  }

  &-other-time {
    .mobile-divider {
      display: none;
    }

    + .recipe-submit-other-time {
      ::ng-deep {
        label {
          display: none;
        }
      }

      @include media-breakpoint-down(sm) {
        .mobile-divider {
          display: block;
          height: 1px;
          margin-top: 20px;
          margin-bottom: 20px;

          span {
            display: block;
            height: inherit;
            background-color: var(--kui-gray-100);
          }
        }

        ::ng-deep {
          label {
            display: block;
          }
        }
      }
    }

    .mindmegette-form {
      &-input {
        margin-bottom: 16px;
      }
    }

    .icon-trash {
      width: 20px;
      height: 20px;
      cursor: pointer;
      margin-top: 12px;
      transition: opacity 0.3s ease;

      @include media-breakpoint-down(sm) {
        margin: 0;
      }

      &.labelMargin {
        margin-top: 38px;

        @include media-breakpoint-down(sm) {
          margin: 0;
        }
      }

      @media (hover: hover) {
        &:hover {
          opacity: 0.9;
        }
      }
    }

    &-new {
      margin-top: 16px;
      text-align: right;

      @include media-breakpoint-down(sm) {
        text-align: center;
      }
    }

    .col-md-1 {
      text-align: right;

      @include media-breakpoint-down(sm) {
        text-align: left;
      }
    }
  }

  &-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin: 24px 0 32px;
    padding-top: 24px;
    border-top: 1px solid var(--kui-gray-100);

    &-title {
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 700;
      line-height: 16px;
      letter-spacing: 0.48px;
      text-transform: uppercase;
      color: var(--kui-gray-950);
    }

    &-value {
      font-family: var(--kui-font-secondary);
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.16px;
      color: var(--kui-gray-950);
    }
  }

  &-notification {
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.16px;
    color: var(--kui-gray-950);
    margin-top: -8px;

    @include media-breakpoint-down(sm) {
      margin-top: 0;
    }

    &-question {
      margin-bottom: 16px;
    }

    .mindmegette-form-radio {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.16px;
      color: var(--kui-gray-950);
      margin-bottom: 16px;
    }
  }

  &-national-dish {
    &-label {
      display: flex;
      align-items: flex-start;
      gap: 8px;
    }

    a {
      color: var(--kui-green-700);
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
