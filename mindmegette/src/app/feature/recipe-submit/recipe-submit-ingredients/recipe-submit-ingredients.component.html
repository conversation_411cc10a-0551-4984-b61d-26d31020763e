<div class="recipe-submit">
  <form (ngSubmit)="navigateToNextStep()" *ngIf="formGroup" [formGroup]="formGroup">
    <div class="mindmegette-form">
      <!-- Recept neve -->
      <div class="mindmegette-form-row no-margin">
        <kesma-form-control>
          <label class="mindmegette-form-label" for="recipeName">Recept neve<strong>*</strong></label>
          <input class="mindmegette-form-input" formControlName="recipeName" id="recipeName" placeholder="Írd be a recepted nevét..." type="text" />
        </kesma-form-control>
      </div>
    </div>

    <!-- Hozzávaló csoportok -->
    <ng-container *ngFor="let ingredientGroup of getIngredientGroupsFormArray()?.controls; let ingredientGroupIndex = index">
      <div class="recipe-submit-ingredient-group-header">
        <div class="recipe-submit-ingredient-group-name">
          {{
            (ingredientGroup?.value?.ingredientGroupName?.length ?? 0) > 0
              ? ingredientGroup?.value?.ingredientGroupName
              : 'Hozzávalók ' + (ingredientGroupIndex + 1)
          }}
        </div>
        <div class="recipe-submit-ingredient-group-actions">
          <i (click)="ingredientGroupRemoveIndex = ingredientGroupIndex" *ngIf="isIngredientGroupActionsEnabled()" class="icon icon-trash" title="Törlés"></i>
          <i
            (click)="toggleIngredientGroup($any(ingredientGroup))"
            *ngIf="isIngredientGroupActionsEnabled()"
            [ngClass]="{ closed: !ingredientGroup?.value?.isOpened }"
            class="icon icon-toggle-down"
            title="Lenyitás / Visszacsukás"
          ></i>
        </div>
      </div>
      <div *ngIf="(ingredientGroup?.value?.ingredientGroupName?.length ?? 0) === 0" class="recipe-submit-ingredient-group-info-text">
        Nevezd el a hozzávalóid csoportját (például a körethez, a tésztához stb.), ha a recepted nem bontható hozzávalócsoportokra. az étel nevét írd be a
        mezőbe.
      </div>
      <div *ngIf="ingredientGroup?.value?.isOpened" [formGroup]="$any(ingredientGroup)" class="mindmegette-form recipe-submit-ingredient-group-content">
        <!-- Hozzávalók csoport neve -->
        <div class="mindmegette-form-row">
          <kesma-form-control>
            <label class="mindmegette-form-label" for="ingredientGroupName{{ ingredientGroupIndex }}">Megjegyzés</label>
            <input
              class="mindmegette-form-input"
              formControlName="ingredientGroupName"
              id="ingredientGroupName{{ ingredientGroupIndex }}"
              placeholder="Pl: A körethez"
              type="text"
            />
          </kesma-form-control>
        </div>

        <!-- Hozzávalók csoport sorok -->
        <ng-container *ngFor="let ingredient of getIngredientsFormArray($any(ingredientGroup))?.controls; let ingredientIndex = index">
          <div [formGroup]="$any(ingredient)" class="row recipe-submit-ingredient">
            <div class="col-12 mobile-divider">
              <span></span>
            </div>
            <div class="col-6 col-md-3">
              <!-- Mennyiség -->
              <div class="mindmegette-form-row">
                <kesma-form-control>
                  <label class="mindmegette-form-label" for="quantity{{ ingredientGroupIndex }}{{ ingredientIndex }}">Mennyiség<strong>*</strong></label>
                  <input
                    (focus)="selectOnFocus($event)"
                    class="mindmegette-form-input"
                    formControlName="quantity"
                    id="quantity{{ ingredientGroupIndex }}{{ ingredientIndex }}"
                    placeholder="Pl: 1"
                    type="text"
                  />
                </kesma-form-control>
              </div>
            </div>
            <div class="col-6 col-md-3">
              <!-- Mértékegység -->
              <div class="mindmegette-form-row">
                <kesma-form-control>
                  <label class="mindmegette-form-label" for="unit{{ ingredientGroupIndex }}{{ ingredientIndex }}">
                    <span class="unit-label-long">Mértékegység<strong>*</strong></span>
                    <span class="unit-label-short">M.egység<strong>*</strong></span>
                  </label>
                  <input
                    (focus)="selectOnFocus($event)"
                    class="mindmegette-form-input"
                    formControlName="unit"
                    id="unit{{ ingredientGroupIndex }}{{ ingredientIndex }}"
                    placeholder="Pl: csipet"
                    type="text"
                  />
                </kesma-form-control>
              </div>
            </div>
            <div class="col-12 {{ isIngredientRemoveEnabled($any(ingredientGroup)) ? 'col-md-5' : 'col-md-6' }}">
              <!-- Hozzávaló -->
              <div class="mindmegette-form-row">
                <mindmegette-form-select
                  ariaLabel="Hozzávalók"
                  (onAdd)="onAddNewIngredientOption($event, $any(ingredient))"
                  (onScroll)="onIngredientScroll($event, ingredientGroupIndex + '' + ingredientIndex)"
                  (onScrollToEnd)="onIngredientScrollToEnd(ingredientGroupIndex + '' + ingredientIndex)"
                  (onSearch)="onIngredientSearch($event, ingredientGroupIndex + '' + ingredientIndex)"
                  [allowAddNew]="true"
                  [allowBackendSearch]="true"
                  [clearable]="true"
                  [formGroup]="$any(ingredient)"
                  [id]="'ingredient' + ingredientGroupIndex + '' + ingredientIndex"
                  [isRequired]="true"
                  [label]="'Hozzávaló'"
                  [loading]="ingredientsLoadingIndex === ingredientGroupIndex + '' + ingredientIndex"
                  [options]="ingredients"
                  [searchable]="true"
                  bindLabel="title"
                  controlName="ingredient"
                  placeholder="Pl: sáfrány"
                ></mindmegette-form-select>
              </div>
            </div>
            <div *ngIf="isIngredientRemoveEnabled($any(ingredientGroup))" class="col-12 col-md-1">
              <i
                (click)="ingredientRemoveFormGroup = $any(ingredientGroup); ingredientRemoveIndex = ingredientIndex"
                [ngClass]="{ labelMargin: ingredientIndex === 0 }"
                class="icon icon-trash"
                title="Törlés"
              ></i>
            </div>
          </div>
        </ng-container>
        <div *ngIf="(ingredientGroup?.value?.ingredients?.length ?? 0) < maxIngredients" class="recipe-submit-ingredient-group-new">
          <div (click)="addIngredient($any(ingredientGroup))" class="recipe-submit-new-button">
            <span>Új hozzávaló</span>
            <i class="icon icon-plus"></i>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- Új hozzávaló csoport gomb -->
    <div
      (click)="addIngredientGroup()"
      *ngIf="(formGroup?.value?.ingredientGroups?.length ?? 0) < maxIngredientGroups"
      class="mindmegette-form recipe-submit-new-form-button"
    >
      <div class="recipe-submit-new-button">
        <span>Új hozzávalók hozzáadása</span>
        <i class="icon icon-plus"></i>
      </div>
    </div>

    <div *ngIf="error" class="mindmegette-form-general-error">
      {{ error }}
    </div>

    <div class="recipe-submit-navigation">
      <mindmegette-simple-button [isSubmit]="true" color="primary">Tovább</mindmegette-simple-button>
    </div>
  </form>
</div>

<mindmegette-popup
  (resultEvent)="removeIngredientGroup($event)"
  *ngIf="ingredientGroupRemoveIndex !== null"
  [acceptButtonLabel]="'Eltávolítás'"
  [showCloseIcon]="false"
  [title]="'Megerősítés'"
>
  Biztosan eltávolítod a kiválasztott hozzávalók csoportot?
</mindmegette-popup>

<mindmegette-popup
  (resultEvent)="removeIngredient($event)"
  *ngIf="ingredientRemoveIndex !== null"
  [acceptButtonLabel]="'Eltávolítás'"
  [showCloseIcon]="false"
  [title]="'Megerősítés'"
>
  Biztosan eltávolítod a kiválasztott hozzávalót?
</mindmegette-popup>
