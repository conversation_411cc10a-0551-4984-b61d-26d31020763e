import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, RouterLink } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import {
  ApiService,
  createMMETitle,
  defaultMetaInfo,
  LatestRecipesRecommendationComponent,
  MindmegetteSimpleButtonComponent,
  StickyImageWrapperComponent,
} from '../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AdvertisementAdoceanStoreService, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-confirm-registration',
  templateUrl: './confirm-registration.component.html',
  styleUrls: ['./confirm-registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [StickyImageWrapperComponent, NgIf, RouterLink, LatestRecipesRecommendationComponent, MindmegetteSimpleButtonComponent],
})
export class ConfirmRegistrationComponent implements OnInit, OnDestroy {
  isLoading = true;
  error: string | null = null;

  unsubscribe$: Subject<void> = new Subject<void>();

  private readonly adStore: AdvertisementAdoceanStoreService = inject(AdvertisementAdoceanStoreService);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.adStore.disableAds();

    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      const id = params['id'];
      const hashedEmail = params['hashedEmail'];
      const expiration = params['expiration'];
      const signature = params['signature'];

      this.setMetaData();

      if (!(id && hashedEmail && expiration && signature)) {
        this.isLoading = false;
        this.error = 'A regisztráció megerősítésére kiküldött link érvénytelen vagy lejárt, kérjük próbáld újra!';
        this.cdr.detectChanges();
        return;
      }

      this.verifyRegister(id, hashedEmail, expiration, signature);
    });
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): void {
    this.apiService.verifyRegister(id, hashedEmail, expiration, signature).subscribe({
      next: () => {
        this.isLoading = false;
        this.error = null;
        this.cdr.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.error = 'A regisztráció megerősítésére kiküldött link érvénytelen vagy lejárt, kérjük próbáld újra!';
        this.cdr.detectChanges();
      },
    });
  }

  ngOnDestroy(): void {
    this.adStore.enableAds();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio/megerosites');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMMETitle('Regisztráció megerősítése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
