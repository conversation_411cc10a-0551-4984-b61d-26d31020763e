import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { catchError, switchMap, takeUntil } from 'rxjs/operators';
import { Subject, throwError } from 'rxjs';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import {
  AdvertisementAdoceanStoreService,
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  KesmaFormControlComponent,
  markControlsTouched,
  nonWhitespaceOnlyValidator,
} from '@trendency/kesma-ui';
import {
  AuthService,
  createMMETitle,
  defaultMetaInfo,
  LatestRecipesRecommendationComponent,
  MindmegetteSimpleButtonComponent,
  SecureApiService,
  SocialProvider,
  StickyImageWrapperComponent,
} from '../../../shared';
import { IMetaData, SeoService, StorageService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-finalize-registration',
  templateUrl: './finalize-registration.component.html',
  styleUrls: ['./finalize-registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    StickyImageWrapperComponent,
    NgIf,
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    KesmaFormControlComponent,
    LatestRecipesRecommendationComponent,
    MindmegetteSimpleButtonComponent,
  ],
})
export class FinalizeRegistrationComponent implements OnInit, OnDestroy {
  formGroup: UntypedFormGroup;
  isLoading = true;
  error: string | null = null;

  isFormLoading = false;
  formError: string | null = null;

  unsubscribe$: Subject<void> = new Subject<void>();
  private readonly adStore: AdvertisementAdoceanStoreService = inject(AdvertisementAdoceanStoreService);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly secureApiService: SecureApiService,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.adStore.disableAds();

    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      if (params['state'] && params['code']) {
        this.verifyOAuthCode(params['state'] as SocialProvider, params['code']);
      } else {
        this.isLoading = false;
        this.error = 'A regisztráció / bejelentkezés folyamata megszakadt, kérjük próbáld újra!';
        this.setMetaData();
        this.cdr.detectChanges();
      }
    });
  }

  verifyOAuthCode(provider: SocialProvider, responseCode: string): void {
    this.authService.authenticateWithSocialProvider(provider, responseCode).subscribe({
      next: (isRegistrationFinished: boolean) => {
        if (isRegistrationFinished) {
          const redirectUrl = this.route.snapshot.queryParams['redirect'] ?? this.storageService.getLocalStorageData('loginRedirectUrl');
          this.storageService.setLocalStorageData('loginRedirectUrl', null);
          if (redirectUrl) {
            this.router.navigate([redirectUrl]);
          } else {
            this.router.navigate(['/profil']);
          }
        } else {
          this.isLoading = false;
          this.setMetaData();
          this.initForm();
          this.cdr.detectChanges();
        }
      },
      error: () => {
        this.isLoading = false;
        this.error = 'Probléma adódott a külső forrásból kapott adatok vizsgálata során, kérjük próbáld újra!';
        this.cdr.detectChanges();
      },
    });
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      lastName: [null, [Validators.required, nonWhitespaceOnlyValidator, Validators.maxLength(50)]],
      firstName: [null, [Validators.required, nonWhitespaceOnlyValidator, Validators.maxLength(50)]],
      username: [null, [Validators.required, nonWhitespaceOnlyValidator, Validators.minLength(6), Validators.maxLength(50)]],
      terms: [false, Validators.requiredTrue],
      marketing: [false],
    });
  }

  finalizeRegistration(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.formError = null;
    this.isFormLoading = true;

    this.secureApiService
      .finishRegister(this.formGroup.value)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User with the same username is already registered
              if (errorKey === 'userName' && !!value.errors) {
                this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.formError = 'Ismeretlen hiba!';
          }
          this.isFormLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.authService.isAuthenticated())
      )
      .subscribe(() => {
        const redirectUrl = this.route.snapshot.queryParams['redirect'] ?? this.storageService.getLocalStorageData('loginRedirectUrl');
        this.storageService.setLocalStorageData('loginRedirectUrl', null);
        if (redirectUrl) {
          this.router.navigate([redirectUrl]);
        } else {
          this.router.navigate(['/profil']);
        }
      });
  }

  ngOnDestroy(): void {
    this.adStore.enableAds();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio/veglegesites');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMMETitle('Fiók véglegesítése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
