import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, inject, On<PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
  AdvertisementAdoceanStoreService,
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  emailValidator,
  KesmaFormControlComponent,
  markControlsTouched,
  nonWhitespaceOnlyValidator,
  passwordValidator,
} from '@trendency/kesma-ui';
import { HttpErrorResponse } from '@angular/common/http';
import { AsyncPipe, DOCUMENT, NgIf, ViewportScroller } from '@angular/common';
import { environment } from '../../../environments/environment';
import { ReCaptchaV3Service } from 'ngx-captcha';
import {
  ApiService,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  createMMETitle,
  defaultMetaInfo,
  LatestRecipesRecommendationComponent,
  MindmegetteSimpleButtonComponent,
  MindmegetteSocialLoginButtonsComponent,
  SocialProvider,
  StickyImageWrapperComponent,
} from '../../shared';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    StickyImageWrapperComponent,
    NgIf,
    FormsModule,
    ReactiveFormsModule,
    KesmaFormControlComponent,
    LatestRecipesRecommendationComponent,
    RouterLink,
    AsyncPipe,
    MindmegetteSimpleButtonComponent,
    MindmegetteSocialLoginButtonsComponent,
  ],
})
export class RegistrationComponent implements OnInit, OnDestroy {
  formGroup: UntypedFormGroup;
  showPassword = false;
  isLoading = false;
  isSubmitted = false;
  error: string | null = null;
  SocialProvider = SocialProvider;
  allowedLoginMethods$: Observable<BackendAllowedLoginMethodsResponse> = this.apiService.getAllowedLoginMethods();

  private readonly adStore: AdvertisementAdoceanStoreService = inject(AdvertisementAdoceanStoreService);

  constructor(
    private readonly apiService: ApiService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly viewportScroller: ViewportScroller,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService,
    private readonly authService: AuthService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
    this.adStore.disableAds();
  }

  ngOnDestroy(): void {
    this.adStore.enableAds();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      lastName: [null, [Validators.required, nonWhitespaceOnlyValidator, Validators.maxLength(50)]],
      firstName: [null, [Validators.required, nonWhitespaceOnlyValidator, Validators.maxLength(50)]],
      username: [null, [Validators.required, nonWhitespaceOnlyValidator, Validators.minLength(6), Validators.maxLength(50)]],
      email: [null, [Validators.required, emailValidator]],
      password: [null, [Validators.required, Validators.minLength(6), passwordValidator]],
      terms: [false, Validators.requiredTrue],
      marketing: [false],
    });
  }

  register(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_register',
      (recaptchaToken: string) => {
        this.apiService.register(this.formGroup.value, recaptchaToken).subscribe({
          next: () => {
            this.isSubmitted = true;
            this.isLoading = false;
            this.formGroup.reset();
            this.viewportScroller.scrollToPosition([0, 0]);
            this.cdr.detectChanges();
          },
          error: (response: HttpErrorResponse) => {
            const backendErrors = response.error as BackendFormErrors;
            let isErrorHandled = false;
            if (backendErrors?.form?.errors?.children) {
              for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
                // User with the same email is already registered
                if (errorKey === 'email' && !!value.errors) {
                  this.formGroup.get('email')?.setErrors({ emailInUse: true });
                  isErrorHandled = true;
                }
                // User with the same username is already registered
                if (errorKey === 'userName' && !!value.errors) {
                  this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                  isErrorHandled = true;
                }
              }
            }
            if (!isErrorHandled) {
              this.error = 'Ismeretlen hiba!';
            }
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  registerWithSocialProvider(provider: SocialProvider): void {
    if (this.utilsService.isBrowser()) {
      this.document.location.href = this.authService.getSocialProviderAuthUrl(provider);
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMMETitle('Regisztráció');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
