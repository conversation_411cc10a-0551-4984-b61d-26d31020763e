import { ChangeDetectionStrategy, Component, inject, input, OnInit, signal, viewChild } from '@angular/core';
import { KesmaSwipeComponent } from '@trendency/kesma-ui';
import { CommonModule } from '@angular/common';
import { LetterUsage } from '@feature/recipe-category-a-z/api/recipe-category-a-z.definitions';
import { SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-recipe-category-abc',
  templateUrl: 'recipe-category-abc.component.html',
  styleUrls: ['recipe-category-abc.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, KesmaSwipeComponent],
})
export class RecipeCategoryAbcComponent implements OnInit {
  private readonly seo = inject(SeoService);
  private readonly swipeComponent = viewChild(KesmaSwipeComponent);

  currentUrl = signal('');

  letterUsage = input.required<LetterUsage[]>();

  swipePrev(): void {
    this.swipeComponent()?.swipePageBack();
  }

  swipeNext(): void {
    this.swipeComponent()?.swipePageForward();
  }

  ngOnInit(): void {
    this.currentUrl.set(this.seo.currentUrl.split('#')[0]);
  }
  scrollTo(id: string, e: Event): void {
    const url = decodeURIComponent(this.seo.currentUrl.split('#')[0]);
    window.history.pushState(null, '', `${url}#kategoria-${id}`);
    const offset = 150;
    e?.preventDefault();
    const element = document.getElementById(`kategoria-${id}`) as HTMLElement;
    if (!element) {
      return;
    }
    const elementPosition = element.getBoundingClientRect().top + window.scrollY;
    const offsetPosition = elementPosition - offset;
    setTimeout(() => {
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }, 100);
  }
}
