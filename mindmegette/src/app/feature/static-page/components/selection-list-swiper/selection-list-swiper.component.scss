@use 'shared' as *;

:host {
  position: relative;
  .group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 60px;
    @include media-breakpoint-down(md) {
      margin-bottom: 40px;
    }

    &-header {
      display: flex;
      justify-content: space-between;
      gap: 24px;
    }

    &-title {
      font-family: var(--kui-font-secondary);
      letter-spacing: 0.12px;
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;

      @include media-breakpoint-up(md) {
        font-family: var(--kui-font-primary);
        font-size: 24px;
        font-weight: 600;
        line-height: 28px;
        letter-spacing: -0.12px;
      }
    }

    &-count {
      font-weight: 500;
      font-size: 16px;
    }

    &-wrapper {
      background-color: var(--kui-white);
      padding: 24px;
      border-radius: 12px;
      border: 1px solid var(--kui-gray-100);
      @include media-breakpoint-down(md) {
        padding: 16px;
      }

      ::ng-deep {
        mindmegette-recipe-card,
        mindmegette-article-card {
          margin-bottom: 0 !important;
          .title {
            font-family: var(--kui-font-secondary);
            font-size: 16px;
            font-weight: 700;
            line-height: 24px;
            letter-spacing: 0.08px;

            @include media-breakpoint-up(md) {
              font-size: 20px;
              line-height: 26px;
            }
          }
        }
      }
    }

    &-empty {
      font-family: var(--kui-font-primary);
    }
  }
  .swiper {
    &-arrows-placeholder {
      display: block;
      min-width: 80px;
    }
  }
  .selection-button {
    display: flex;
    padding: 10px 20px;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: 8px;
    margin-top: 4px;
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.16px;
    color: var(--kui-white);
    background-color: var(--kui-green-700);
    cursor: pointer;
    transition: background-color ease-out 200ms;
    @include media-breakpoint-down(lg) {
      margin-top: 0;
      padding: 8px 16px;
      font-size: 14px;
      line-height: 20px;
      width: 100%;
    }

    i {
      width: 20px;
      height: 20px;
    }

    &:hover {
      background-color: var(--kui-green-800);
    }
  }
}
