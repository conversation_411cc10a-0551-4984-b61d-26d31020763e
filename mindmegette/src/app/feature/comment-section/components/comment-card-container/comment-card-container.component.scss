@use 'shared' as *;

:host {
  display: block;
}

.comment {
  &-edit-form {
    margin-bottom: 10px;
  }

  &-card {
    margin-bottom: 30px;

    &-sub {
      max-width: 792px;
      margin: 20px 0 0 20px;
      padding: 10px;
      border-left: 1px solid var(--kui-gray-100);
      border-top: 1px solid var(--kui-gray-100);
    }
    &-answer-toggle {
      font-size: 12px;
      font-weight: 700;
      margin-bottom: 10px;
      margin-left: 20px;
      &-icon {
        position: relative;
        top: 4px;
        transition: 0.2s all ease-out;
        width: 20px;
        height: 20px;
        transform: rotate(90deg);
      }
    }
  }

  &-date {
    font-size: 12px;
    font-weight: 400;

    &-separator {
      margin: 0 5px;
      color: var(--kui-gray-300);
    }
  }

  &-header {
    display: flex;
    align-items: center;
    gap: 10px;

    &-text {
      h4 {
        font-family: var(--kui-font-secondary);
        font-weight: 700;
        font-size: 18px;
        line-height: 24px;
      }
    }
  }
}

.rotate {
  transform: rotate(-90deg);
}

.show-more-text {
  color: var(--kui-green-700);
  font-family: var(--kui-font-secondary);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px; /* 150% */
  letter-spacing: 0.16px;
}

.response-count {
  width: fit-content;
  margin-bottom: 10px;
}

.ms-n20px {
  margin-left: -20px;
}

.ms-20px {
  margin-left: 20px;
}

.reset-margin {
  margin-left: -10px;
  @media screen and (max-width: 768px) {
    margin-left: -20px;
  }
}

.reaction-spinner {
  position: relative;
  top: -34px;
  left: 230px;
}

.options {
  width: 100%;
  min-width: 320px;
  background-color: var(--kui-white);
  border: 1px solid var(--kui-orange-600);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;

  mindmegette-simple-button {
    margin: 0;

    span {
      line-height: 14px;
    }
  }
}

mindmegette-spinner {
  margin-left: 25px;
}

.italic {
  font-style: italic;
}
