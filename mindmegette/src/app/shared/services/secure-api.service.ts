import { EnvironmentApiUrl, ReqService, UtilService } from '@trendency/kesma-core';
import { environment } from '../../../environments/environment';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  ApiResponseMetaList,
  ApiResult,
  BackendComment,
  BackendCommentWithArticle,
  BackendOrderPurchaseCheckoutResponse,
  BackendOrderPurchaseSaveRequest,
  ReactionsData,
  SimplePayStatus,
  User,
  UserCommentsType,
} from '@trendency/kesma-ui';
import { PortalUser, ProfileEditFormData, RegistrationFormData, ShoppingCartItem, UserCommentCount } from '../definitions';
import { map } from 'rxjs/operators';
import { Params } from '@angular/router';
import { backendPortalUserDataToUser, profileEditFormDataToBackendRequest, registrationFormDataToBackendRequest } from '../utils';
import { RecipeSubmitSessionData } from '../../feature/recipe-submit/recipe-submit.definitions';
import { BestPracticeFormData } from '../../feature/best-practice-submit/definitions/best-practice-form-data';
import { BestPractise } from '../../feature/best-practices/best-practices.definitions';
import { Recipe, RecipeGroup, RecipeGroupWithRecipes, RecipeIsVoted } from '../../feature/recipe-page/recipe-page.definitions';
import { FollowedUserProfile } from '../../feature/profile/profile-followed-users/profile-followed-users.interfaces';
import { recipeSubmitSessionDataToBackendRequest } from '../../feature/recipe-submit/recipe-submit.utils';

@Injectable({
  providedIn: 'root',
})
export class SecureApiService {
  constructor(
    private readonly reqService: ReqService,
    private readonly utilService: UtilService
  ) {}

  get secureApiUrl(): string {
    if (typeof environment.secureApiUrl === 'string') {
      return environment.secureApiUrl;
    }

    const { clientApiUrl, serverApiUrl } = environment.secureApiUrl as EnvironmentApiUrl;
    return this.utilService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getCurrentUser(): Observable<User> {
    return this.reqService.get<PortalUser>(`${this.secureApiUrl}/portal-user`).pipe(map((user) => backendPortalUserDataToUser(user)));
  }

  finishRegister(formData: RegistrationFormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/register-finish`, registrationFormDataToBackendRequest(formData));
  }

  logout(): Observable<void> {
    return this.reqService.get(`${this.secureApiUrl}/logout`);
  }

  submitRecipe(formData: RecipeSubmitSessionData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/recipe/send-in-recipe`, recipeSubmitSessionDataToBackendRequest(formData, recaptchaToken));
  }

  submitBestPractice(formData: BestPracticeFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/create-best-practice`, {
      ...formData,
      recaptcha: recaptchaToken,
    });
  }

  getMyBestPractices(params: Params): Observable<ApiResult<BestPractise[], ApiResponseMetaList>> {
    return this.reqService.get(`${this.secureApiUrl}/best-practices`, { params });
  }

  editCurrentUser(formData: ProfileEditFormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/save`, profileEditFormDataToBackendRequest(formData));
  }

  deleteAccount(passwordOld: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/delete-account`, { password: passwordOld });
  }

  uploadProfileAvatar(formData: FormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/avatar`, formData);
  }

  deleteProfileAvatar(): Observable<void> {
    return this.reqService.delete(`${this.secureApiUrl}/portal-users/portal-user/avatar`);
  }

  getMyVotesFor(
    id: string,
    params: object,
    userId: string,
    type: 'article' | 'recipe' | 'comment' = 'article'
  ): Observable<ApiResult<ReactionsData, ApiResponseMetaList>> {
    const portalUser = type === 'comment' ? `portal-user/${userId}/` : '';
    return this.reqService.get<ApiResult<ReactionsData, ApiResponseMetaList>>(`${this.secureApiUrl}/comments/${portalUser}${type}/${id}/my-votes`, {
      params,
    });
  }

  getUserAllCommentCount(): Observable<ApiResult<UserCommentCount>> {
    return this.reqService.get<ApiResult<UserCommentCount>>(`${this.secureApiUrl}/comments/portal-user/my-comments-count`);
  }

  getMyArticleComments(params: object): Observable<ApiResult<UserCommentsType[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<UserCommentsType[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user-comments`, {
      params,
    });
  }

  getMyComments(params: object, userId: string): Observable<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/my-comments`, {
      params,
    });
  }

  getArticleComments(params: object, userId: string): Observable<ApiResult<UserCommentsType[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<UserCommentsType[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/article-comments`, {
      params,
    });
  }

  getComments(params: object, userId: string): Observable<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/comments`, {
      params,
    });
  }

  getCommentsCount(userId: string): Observable<ApiResult<{ commentCount: number }, ApiResponseMetaList>> {
    return this.reqService.get(`${this.secureApiUrl}/comments/portal-user/${userId}/comments-count`);
  }

  getCommentsFor(id: string, params: object, type: 'article' | 'comment' | 'recipe' = 'article'): Observable<ApiResult<BackendComment[], ApiResponseMetaList>> {
    const urlSuffix = type === 'comment' ? 'answers' : 'comments';
    return this.reqService.get<ApiResult<BackendComment[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/${type}/${id}/${urlSuffix}`, {
      params,
    });
  }

  submitCommentFor(id: string, type: 'article' | 'comment' | 'recipe' = 'article', text: string): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/comments/${type}/${id}/create`, { text });
  }

  editComment(id: string, text: string): Observable<ApiResult<never>> {
    return this.reqService.patch<ApiResult<never>>(`${this.secureApiUrl}/comments/comment/${id}/update`, { text });
  }

  voteComment(id: string, vote: 'like' | 'dislike' | 'clear-like-dislike' | 'report'): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/comments/comment/${id}/${vote}`, {});
  }

  getMyUploadedRecipes(params: Params): Observable<ApiResult<Recipe[], ApiResponseMetaList>> {
    return this.reqService.get(`${this.secureApiUrl}/recipe/my-uploaded-recipes`, {
      params,
    });
  }

  /**
   * List of all created recipe group with their recipes. (user-specific)
   */
  getRecipeGroupsWithArticles(): Observable<ApiResult<RecipeGroupWithRecipes[]>> {
    return this.reqService.get(`${this.secureApiUrl}/recipe/saved-recipe-groups`);
  }

  /**
   * List of all created recipe group. (user-specific)
   */
  getRecipeGroups(): Observable<ApiResult<RecipeGroup[]>> {
    return this.reqService.get(`${this.secureApiUrl}/source/recipe/saved-recipe-groups`);
  }

  /**
   * Groups what added to recipe. (user-specific)
   * @param recipeSlug
   */
  getSavedRecipeGroups(recipeSlug: string): Observable<ApiResult<RecipeGroup[]>> {
    return this.reqService.get(`${this.secureApiUrl}/recipe/saved-recipe-groups-by-recipe/${recipeSlug}`);
  }

  getRecipesByGroup(recipeGroupId: string, params: Params): Observable<ApiResult<Recipe[]>> {
    return this.reqService.get(`${this.secureApiUrl}/recipe/recipes-in-own-recipe-group/${recipeGroupId}`, {
      params,
    });
  }

  saveRecipeToGroup(recipeGroupId: string | null, recipeSlug: string): Observable<never> {
    return this.reqService.post(`${this.secureApiUrl}/recipe/saved-recipe-group/add-new-recipe`, {
      recipeGroupId,
      recipeSlug,
    });
  }

  updateRecipeGroupTitle(recipeGroupId: string, title: string): Observable<never> {
    return this.reqService.patch(`${this.secureApiUrl}/recipe/saved-recipe-group/${recipeGroupId}`, { title });
  }

  createRecipeGroup(title: string): Observable<never> {
    return this.reqService.post(`${this.secureApiUrl}/recipe/saved-recipe-group`, { title });
  }

  removeRecipeFromGroup(recipeGroupId: string, recipeSlug: string): Observable<never> {
    return this.reqService.patch(`${this.secureApiUrl}/recipe/saved-recipe-group/${recipeGroupId}/remove/${recipeSlug}`, {});
  }

  deleteRecipeGroup(recipeGroupId: string): Observable<never> {
    return this.reqService.delete(`${this.secureApiUrl}/recipe/saved-recipe-group/${recipeGroupId}/delete`, {});
  }

  saveRating(recipeSlug: string, rate: number): Observable<any> {
    return this.reqService.post(`${this.secureApiUrl}/recipe/add-rate/${recipeSlug}`, {
      rate,
    });
  }

  getRecipeIsVoted(recipeSlug?: string): Observable<ApiResult<RecipeIsVoted>> {
    return this.reqService.get<ApiResult<RecipeIsVoted>>(`${this.secureApiUrl}/recipe/has-saved-rate/${recipeSlug}`);
  }

  followPublicUser(portalUserID: string | undefined): Observable<never> {
    return this.reqService.post(`${this.secureApiUrl}/followed-portal-user/${portalUserID}/add`, null);
  }

  unFollowPublicUser(portalUserID: string | undefined): Observable<never> {
    return this.reqService.delete(`${this.secureApiUrl}/followed-portal-user/${portalUserID}/delete`, {});
  }

  getMyFollowedProfiles(params?: Params): Observable<ApiResult<FollowedUserProfile[]>> {
    return this.reqService.get(`${this.secureApiUrl}/followed-portal-users`, { params });
  }

  getShoppingList(): Observable<ApiResult<ShoppingCartItem[]>> {
    return this.reqService.get<ApiResult<ShoppingCartItem[]>>(`${this.secureApiUrl}/recipe/cart/list`);
  }

  addToShoppingList(shoppingList: Record<string, string | object>): Observable<never> {
    return this.reqService.post(`${this.secureApiUrl}/recipe/cart/add`, shoppingList);
  }

  clearShoppingList(): Observable<never> {
    return this.reqService.delete(`${this.secureApiUrl}/recipe/cart/clear`);
  }

  deleteIngredientFromShoppingList(recipeSlug: string, ingredientId: string): Observable<never> {
    return this.reqService.delete(`${this.secureApiUrl}/recipe/cart/delete/${recipeSlug}/${ingredientId}`);
  }

  deleteRecipeFromShoppingList(recipeSlug: string): Observable<never> {
    return this.reqService.delete(`${this.secureApiUrl}/recipe/cart/delete/${recipeSlug}`);
  }

  checkoutOrderPurchase(saveRequestData: BackendOrderPurchaseSaveRequest): Observable<ApiResult<BackendOrderPurchaseCheckoutResponse>> {
    return this.reqService.post(`${this.secureApiUrl}/order/purchase/checkout`, saveRequestData);
  }

  saveOrderPurchase(saveRequestData: BackendOrderPurchaseSaveRequest): Observable<{
    getPaymentUrl: string;
    orderId: string;
  }> {
    return this.reqService.post(`${this.secureApiUrl}/order/purchase/save`, saveRequestData);
  }

  retryOrderPurchase(orderId: string): Observable<{
    getPaymentUrl: string;
    orderId: string;
  }> {
    return this.reqService.get(`${this.secureApiUrl}/order/purchase/retry/${orderId}`);
  }

  finishOrderPurchase(params: Params): Observable<{ event: SimplePayStatus }> {
    return this.reqService.post(`${this.secureApiUrl}/order/otp-simple-pay-back`, params);
  }

  getPaymentUrl(getPaymentUrlRequest: string): Observable<{ status: string; simplePayPaymentUrl: string }> {
    return this.reqService.get(`${getPaymentUrlRequest}`);
  }
}
