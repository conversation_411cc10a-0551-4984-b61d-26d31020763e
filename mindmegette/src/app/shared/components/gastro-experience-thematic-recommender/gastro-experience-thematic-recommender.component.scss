@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  &.style-Default {
    .experiences-wrapper {
      flex-direction: row;
      mindmegette-gastro-experience-occasion-card ::ng-deep {
        min-width: 100px;
      }
      @include media-breakpoint-down(xs) {
        flex-wrap: wrap;
      }

      &.narrow-column {
        @include media-breakpoint-up(sm) {
          flex-direction: column;
        }
      }
    }
  }

  &.style-Sidebar {
    .title {
      font-size: 20px;

      &.sponsored {
        .sponsor-image {
          .sponsor-text {
            display: none;
          }
        }
      }
    }

    .experiences-wrapper {
      flex-direction: column;

      mindmegette-gastro-experience-occasion-card ::ng-deep {
        width: 100%;
      }
    }
  }

  &.style-Sidebar,
  &.style-Default {
    @include media-breakpoint-down(xs) {
      .experiences-container {
        // on mobile an extra dom element is needed for horizontal scrolling
        overflow-x: auto;
        -ms-overflow-style: none;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }
      }
      .experiences-wrapper {
        flex-direction: row;
        flex-wrap: nowrap;
        width: fit-content;
        padding: 16px;
        gap: 16px;
        margin-bottom: 16px;
      }

      mindmegette-gastro-experience-occasion-card::ng-deep {
        min-width: 210px !important;

        .experience-title {
          font-size: 16px;
          line-height: 22px;
        }
        .experience-lead {
          display: none;
        }

        .experience-date {
          font-size: 12px;
          line-height: 16px;
          margin-bottom: 6px;
        }

        .experience-thumbnail {
          img {
            border-radius: 8px;
          }
        }
      }
    }
  }

  .title {
    color: var(--kui-gray-950);
    text-align: center;
    font-size: 30px;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: -0.3px;
    margin-bottom: 16px;

    &.sponsored {
      border-radius: 12px;
      text-align: start;
      padding: 4px 8px 4px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      @include media-breakpoint-down(sm) {
        font-size: 24px;
      }

      .sponsor-image {
        display: flex;
        align-items: center;
        img {
          border-radius: 4px;
          width: auto;
          max-height: 50px;
        }
        .sponsor-text {
          font-size: 12px;
          font-weight: 700;
          line-height: 16px;
          margin-right: 12px;
          font-family: var(--kui-font-secondary);

          @include media-breakpoint-down(sm) {
            display: none;
          }
        }
      }
    }
  }

  .experiences-container {
    display: block;
  }

  .experiences-wrapper {
    width: 100%;
    display: flex;
    padding: 24px;
    gap: 24px;
    border-radius: 12px;
    border: 1px solid var(--kui-gray-100);
    background-color: var(--kui-white);
    margin-bottom: 20px;

    mindmegette-gastro-experience-occasion-card ::ng-deep {
      min-width: 280px;
      .experience-thumbnail {
        border-radius: 8px;
      }

      .experience-data {
        padding: 12px 0 0 0;
      }
    }

    &.narrow-column {
      mindmegette-gastro-experience-occasion-card ::ng-deep {
        min-width: unset;
      }
    }
  }

  .more-button {
    width: 100%;
    padding: 10px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 6px;
    align-self: stretch;
    border-radius: 8px;
    background-color: var(--kui-green-700);
    color: var(--kui-white);
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.16px;
    font-weight: 500;
    font-family: var(--kui-font-dm-sans);
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--kui-green-800);
    }

    .icon {
      width: 20px;
      height: 20px;
    }
  }
}
