@if (canShowSponsor && data?.occasions[0]?.sponsorship?.highlightedColor) {
  <h2
    class="title sponsored"
    [style.color]="data?.occasions[0]?.sponsorship?.fontColor"
    [style.background-color]="data?.occasions[0]?.sponsorship?.highlightedColor"
  >
    {{ data?.blockTitle }}
    <span *ngIf="data?.occasions[0]?.sponsorship?.logo" class="sponsor-image">
      <span class="sponsor-text">Támogatja</span>
      @if (data?.occasions[0]?.sponsorship?.url) {
        <a [href]="data?.occasions[0]?.sponsorship?.url" target="_blank">
          <img [src]="data?.occasions[0]?.sponsorship?.logo" [alt]="'Szponzor logó'" />
        </a>
      } @else {
        <img [src]="data?.occasions[0]?.sponsorship?.logo" [alt]="'Szponzor logó'" />
      }
    </span>
  </h2>
} @else {
  <h2 class="title">{{ data?.blockTitle }}</h2>
}
<div class="experiences-container">
  <div class="experiences-wrapper" [class.narrow-column]="desktopWidth < 5">
    @for (occasion of data?.occasions; track trackByFn) {
      @if (occasion && occasion.title) {
        <mindmegette-gastro-experience-occasion-card
          [data]="occasion"
          [styleId]="GastroExperienceOccasionCardType.Default"
        ></mindmegette-gastro-experience-occasion-card>
      }
    }
  </div>
</div>
<a class="more-button" [routerLink]="data?.buttonUrl"> Fedezd fel mind <i class="icon mindmegette-icon-arrow-right"></i> </a>
