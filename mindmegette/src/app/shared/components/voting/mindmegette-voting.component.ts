import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, HostBinding, inject, Input, OnInit } from '@angular/core';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { Sponsorship, VoteDataWithAnswer, VoteService, VotingComponent } from '@trendency/kesma-ui';
import { first } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UtilService } from '@trendency/kesma-core';
import { VoteDataWithVotedId } from '@trendency/kesma-ui/lib/definitions';

@Component({
  selector: 'mindmegette-voting',
  templateUrl: './mindmegette-voting.component.html',
  styleUrls: ['./mindmegette-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, AsyncPipe],
})
export class MindmegetteVotingComponent extends VotingComponent implements OnInit {
  @Input() sponsored?: Sponsorship;
  @Input() desktopWidth = 12;
  // Usually voting only need [data] input (=extractorData.data),
  // however, because of the dynamic layout we had to move some logic into here, therefore, we need the whole VoteData obj
  @Input() extractorData?: VoteDataWithVotedId;

  @HostBinding('attr.style')
  public get vars(): string | null {
    if (!this.sponsored) {
      return null;
    }
    return `
    --sponsored-background-color: ${this.sponsored.highlightedColor || '#00964A'};
    --sponsored-text-color: ${this.sponsored.fontColor || '#fff'};
  `;
  }

  private readonly voteService = inject(VoteService);
  private readonly changeDetector = inject(ChangeDetectorRef);
  private readonly destroyRef = inject(DestroyRef);
  private readonly utilService = inject(UtilService);

  get narrowView(): boolean {
    return this.desktopWidth < 5;
  }

  override ngOnInit(): void {
    super.ngOnInit();

    if (!this.utilService.isBrowser() || !this.extractorData?.data?.id) {
      // SSR doesn't need fresh answer counts
      return;
    }

    this.voteService.setVoteCache(this.extractorData);
    // Usually this is handled with an async pipe, but the dynamic rendering doesn't allow it
    this.voteService.voteCache[this.extractorData?.data?.id].pipe(takeUntilDestroyed(this.destroyRef)).subscribe((vote) => {
      if (!vote || !this.extractorData?.data) {
        return;
      }

      this.extractorData.data.answers = vote.data.answers;
      this.data = this.extractorData.data;
      this.vm.next({
        hasExpired: this.isExpired,
        voteData: this.data,
        userVotedId: vote.votedId,
        showResults: !!vote.votedId || this.isExpired,
      });
    });
  }

  override onVote(): void {
    if (this.vm.state.showResults || !this.extractorData) {
      return;
    }
    this.showResults = true;
    this.onVotingSubmit(this.voteId ?? '', this.extractorData);
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService
      .onVotingSubmit($event, voteData)
      .pipe(first())
      .subscribe(() => this.changeDetector.markForCheck());
  }
}
