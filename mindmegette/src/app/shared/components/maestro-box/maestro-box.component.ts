import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import {
  BaseComponent,
  ButtonStyleInfo,
  IconComponent,
  KesmaSwipeComponent,
  Maestro,
  MaestroBoxType,
  SwipeBreakpoints,
  TapeDataType,
} from '@trendency/kesma-ui';
import { MindmegetteArticleCardType, RecipeCardType } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { RecipeCardComponent } from '../recipe-card/recipe-card.component';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'mindmegette-maestro-box',
  templateUrl: './maestro-box.component.html',
  styleUrls: ['./maestro-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgF<PERSON>, NgIf, RecipeCardComponent, ArticleCardComponent, IconComponent, KesmaSwipeComponent],
})
export class MaestroBoxComponent extends BaseComponent<MaestroBoxType> implements OnInit {
  breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 2,
    },
    680: {
      itemCount: 3,
    },
    1060: {
      itemCount: 4,
    },
  };
  @HostBinding('class') hostClass = 'large-container';

  @Input() set desktopWidth(width: number) {
    this.hostClass = '';
    if (this.breakpoint <= width) {
      this.hostClass = 'large-container';
    }
  } // Value from 1-12 to indicate the width of the card on desktop.

  readonly breakpoint: number = 4;
  readonly maestroBtnStyleInfo: ButtonStyleInfo = {
    button: {
      'white-space': 'nowrap',
    },
  };
  selectedMaestro?: Maestro & { introduction?: string };
  selectedMaestroIndex = 0;
  cardType = TapeDataType;
  recipeCardType = RecipeCardType.TopImageLeftAlignedCard;
  articleCardType = MindmegetteArticleCardType.TopImageLeftAlignedCard;

  onMaestroSelect(index: number): void {
    this.selectedMaestroIndex = index;
    this.selectedMaestro = this.data?.authors[index];
  }

  onViewMaestro(): string | undefined {
    if (!this.selectedMaestro) {
      return;
    }
    return '/szerzo/' + (this.selectedMaestro?.slug || this.selectedMaestro?.name);
  }

  protected override setProperties(): void {
    if (this.data?.authors?.length) {
      this.onMaestroSelect(this.selectedMaestroIndex);
    }
  }
}
