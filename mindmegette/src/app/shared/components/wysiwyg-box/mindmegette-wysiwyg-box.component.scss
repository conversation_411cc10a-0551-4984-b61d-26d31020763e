@use 'shared' as *;

:host {
  &.style-IngredientContent {
    .block-content::ng-deep > p {
      margin-bottom: 40px;
    }
  }
}

.block-content::ng-deep {
  * {
    font-family: var(--kui-font-secondary);
  }

  iframe {
    max-width: 100%;
    max-height: 600px;

    @include media-breakpoint-down(sm) {
      max-height: 450px;
    }
  }

  .table-wrapper {
    display: block;
    overflow-x: auto;
  }

  .table {
    margin: auto;

    table {
      overflow-wrap: break-word;
      margin-bottom: 0;

      td,
      th {
        background-color: initial;
      }
    }

    > figcaption {
      display: table-caption;
      caption-side: top;
      border: 1px solid var(--kui-gray-300);
      border-bottom: none;
      padding: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }
  }
  figure.image-style-align-left {
    margin-right: unset !important;
    padding-right: 10px;
  }

  figure.image-style-align-right {
    margin-left: unset !important;
    padding-left: 10px;
  }

  figure.image ~ p {
    hyphens: auto;
  }

  figure.image {
    margin-bottom: 24px;

    img {
      border-radius: 8px;
    }

    figcaption {
      font-size: 12px;
      color: var(--kui-gray-400);
      font-weight: 500;
      letter-spacing: 0.12px;
      font-style: normal;
      text-align: center;
      line-height: 16px;

      &:before {
        content: none;
      }
    }
  }

  h2 {
    font-family: var(--kui-font-primary);
    font-size: 30px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: -0.3px;

    @include media-breakpoint-down(xs) {
      font-size: 24px;
      line-height: 28px;
      letter-spacing: 0.12px;
    }
  }

  h3 {
    font-family: var(--kui-font-primary);
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: 0.12px;

    @include media-breakpoint-down(xs) {
      font-family: var(--kui-font-secondary);
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
    }
  }

  h4 {
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.16px;
  }

  a {
    color: var(--kui-green-700);
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.16px;
  }

  p {
    font-family: var(--kui-font-secondary);
  }

  h2,
  h3,
  h4 {
    margin-bottom: 24px;
    color: var(--kui-gray-950);

    a {
      font-weight: inherit;
      line-height: inherit;
      letter-spacing: inherit;
    }

    span {
      font-size: inherit;
      line-height: inherit;
    }
  }

  li {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    font-style: normal;
    letter-spacing: 0.16px;
    margin: 0 !important;
    padding-bottom: 8px;
    color: var(--kui-gray-950);
    font-family: var(--kui-font-secondary);
    display: flow-root;
  }

  ul {
    li {
      list-style-type: none;
      padding-left: 24px;

      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        left: 0;
        top: 7px;
        background-size: contain;
        background-repeat: no-repeat;
        height: 10px;
        width: 10px;
        background-image: url('/assets/images/icons/mindmegette-marker.svg');
      }
    }
  }

  ol {
    list-style: none;
    padding: 0;

    li {
      list-style: none;
      padding-left: 24px;
      counter-increment: step-counter;
      position: relative;

      &::before {
        color: var(--kui-green-700);
        content: counter(step-counter) '.';
        font-weight: 700;
        min-width: 24px;
        position: absolute;
        left: 0;
      }
    }
  }

  .custom-text-style.quote {
    border: unset;
    color: var(--kui-green-700);
    font-size: 16px;
    font-style: italic;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.16px;
    padding: 0 24px;
    @include media-breakpoint-down(sm) {
      padding: 0 16px;
      font-size: 14px;
      font-style: italic;
      font-weight: 500;
      line-height: 20px;
    }
  }

  .custom-text-style {
    .quoteBlock-content {
      padding-top: 40px;

      @include media-breakpoint-up(md) {
        padding-top: 52px;
      }

      h2 {
        color: var(--kui-green-700);
        font-style: italic;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.16px;
        text-align: center;
      }

      span {
        background-color: transparent !important;
        color: var(--kui-green-700) !important;
      }

      p {
        font-family: var(--kui-font-secondary);
        font-size: 14px;
        font-style: italic;
        font-weight: 500;
        line-height: 20px;

        @include media-breakpoint-up(md) {
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0.16px;
        }

        &:before,
        &:after {
          display: none;
        }
      }

      &:before {
        content: '';
        position: absolute;
        background: url('/assets/images/icons/mindmegette-icon-quote.svg');
        top: 0;
        left: 0;
        right: 0;
        transform: rotate(180deg);
        width: 38px;
        height: 32px;
        margin: auto;

        @include media-breakpoint-down(sm) {
          top: 0;
          background: url('/assets/images/icons/mindmegette-icon-small-quote.svg');
          width: 29px;
          height: 24px;
        }
      }
    }

    &.border-text {
      background: transparent;
      border: 1px solid var(--kui-green-700);
      border-radius: 8px;
      color: var(--kui-green-700);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0.16px;
      padding: 24px;
      @include media-breakpoint-down(sm) {
        padding: 16px;
        font-size: 14px;
        line-height: 20px;
      }
    }

    &.highlight {
      background: var(--kui-green-700);
      border-radius: 8px;
      color: var(--kui-white);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0.16px;
      padding: 24px;
      @include media-breakpoint-down(sm) {
        padding: 16px;
        font-size: 14px;
        line-height: 20px;
      }

      p {
        font-size: inherit;
      }
    }

    &.highlight-style2 {
      border-left: 4px solid var(--kui-green-700);
      padding-left: 24px;
      background: none;
      margin: 24px 0;
      color: var(--kui-black);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      @include media-breakpoint-down(sm) {
        padding-left: 22px;
        font-size: 14px;
        line-height: 20px;
      }

      p {
        font-size: inherit;
      }
    }

    &.highlight-style3 {
      padding: 32px;
      border-radius: 8px;
      border: 1px solid var(--kui-pink-500);
      border-top-width: 8px;
      background: var(--kui-white);

      @include media-breakpoint-down(xs) {
        padding: 24px 16px;
      }

      &:before {
        content: '';
        position: absolute;
        top: 32px;
        left: 32px;
        width: 32px;
        height: 32px;
        background: url('/assets/images/icons/icon-pin.svg');
        background-repeat: round;

        @include media-breakpoint-down(xs) {
          top: 24px;
          left: 16px;
          width: 24px;
          height: 24px;
        }
      }

      .highlightBlock-style3-content {
        > :first-child {
          text-indent: 40px;
          margin-bottom: 16px;

          @include media-breakpoint-down(xs) {
            text-indent: 32px;
            margin-bottom: 12px;
          }
        }

        > :last-child {
          margin-bottom: 0;
        }

        p {
          margin-bottom: 24px;
          line-height: 28px;

          @include media-breakpoint-down(xs) {
            margin-bottom: 16px;
          }
        }

        @include media-breakpoint-down(xs) {
          p,
          li {
            font-size: 14px;
            line-height: 20px;
          }

          h2 {
            font-size: 20px;
            line-height: 26px;
          }

          li:before {
            top: 4.5px;
          }
        }
      }
    }

    &.highlight-style4 {
      padding: 24px;
      border: 1px solid var(--kui-green-600);
      border-radius: 8px;
      background: none;
      margin: 24px 0;
      color: var(--kui-black);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0.16px;

      @include media-breakpoint-down(sm) {
        padding: 16px;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: normal;
      }

      h2 {
        margin-bottom: 15px;
      }

      ul {
        padding-left: 8px;
        margin-bottom: 8px;
        margin-top: 0;
      }

      p {
        &:last-child {
          margin-bottom: 0;
        }
      }

      li {
        color: var(--kui-gray-950);
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        padding-left: 28px;

        &:before {
          top: 8px;
        }
      }

      p {
        font-size: inherit;
      }
    }

    &.underlined-text {
      display: inline;
      font-size: inherit;
      font-style: inherit;
      font-weight: inherit;
      text-decoration-line: underline;
      text-decoration-color: inherit;
      text-decoration-thickness: 1px;
    }
  }
}
