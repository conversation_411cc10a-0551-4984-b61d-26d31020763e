<div class="slider-wrapper-main">
  <ng-template #itemTemplate let-elem="data">
    <article class="slider-slide-container">
      @if (toBool(data?.isAdult) && !isAcceptedAdultContent && !isInsideAdultArticleBody) {
        <kesma-adult-overlay>
          <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
          <mindmegette-simple-button (click)="acceptAdultContent()" clickStopPropagation custom-overlay-content> Megnézem </mindmegette-simple-button>
        </kesma-adult-overlay>
      } @else {
        <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
      }
      <ng-template #galleryImage
        ><img (click)="onOpenSliderLayer()" alt="{{ elem?.altText ?? elem?.title }}" src="{{ elem?.url?.fullSize }}" loading="lazy"
      /></ng-template>
    </article>
  </ng-template>
  <div class="slider-wrapper">
    <div
      kesma-swipe
      [itemTemplate]="itemTemplate"
      [data]="data?.images"
      [breakpoints]="{
        default: {
          itemCount: 1.2,
          itemAlign: 'center',
          gap: '8px',
        },
      }"
    ></div>
    <div class="bottom-controls-wrapper">
      <div class="page-count-wrapper">
        <div class="page-count">{{ currentIndex() + 1 }}/{{ data?.images?.length }}</div>
      </div>

      <div class="arrows">
        <button class="left" (click)="swipePrev()">
          <i class="icon mindmegette-icon-black-left-arrow"></i>
        </button>
        <button class="right" (click)="swipeNext()">
          <i class="icon mindmegette-icon-black-right-arrow"></i>
        </button>
      </div>
    </div>
  </div>

  <div class="gallery-details">
    <div class="caption">
      {{ data?.images?.[currentIndex()]?.caption || data?.images?.[currentIndex()]?.title || data?.title }}
      <span class="photographer" *ngIf="data?.photographer">(FOTÓ: {{ data?.photographer }})</span>
    </div>
  </div>
</div>

<ng-container *ngIf="isLayerVisible">
  <mindmegette-simplified-gallery-layer
    [currentIndex]="currentIndex() ?? null"
    [data]="data"
    (closeLayer)="onCloseSliderLayer(currentIndex())"
    (slideChange)="layerSlideChanged.emit($event)"
  ></mindmegette-simplified-gallery-layer>
</ng-container>
