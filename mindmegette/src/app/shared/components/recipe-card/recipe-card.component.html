<ng-container [ngSwitch]="styleID">
  <ng-container *ngSwitchCase="RecipeCardType.LeftImageShortCard" [ngTemplateOutlet]="LeftImageShortCard"></ng-container>
  <ng-container *ngSwitchCase="RecipeCardType.LeftImageLongCard" [ngTemplateOutlet]="LeftImageLongCard"></ng-container>
  <ng-container *ngSwitchCase="RecipeCardType.TopImageLeftAlignedCard" [ngTemplateOutlet]="TopImageLeftAlignedCard"></ng-container>
  <ng-container *ngSwitchCase="RecipeCardType.TopImageCenteredCard" [ngTemplateOutlet]="TopImageCenteredCard"></ng-container>
</ng-container>

<ng-template #LeftImageShortCard>
  <div class="horizontal-card" [class.flex-direction-column]="isUsedInGuaranteeBox && desktopWidth < breakpoint" [class.has-background]="hasBackground">
    <ng-container *ngTemplateOutlet="RecipeCardThumbnail"></ng-container>
    <div class="content">
      <ng-container *ngTemplateOutlet="RecipeCategories"></ng-container>
      <ng-container *ngTemplateOutlet="RecipeCardTitle"></ng-container>
      <ng-container *ngTemplateOutlet="RecipePreparation; context: { isCentered: false }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #LeftImageLongCard>
  <div class="horizontal-card" [class.has-background]="hasBackground">
    <ng-container *ngTemplateOutlet="RecipeCardThumbnail"></ng-container>
    <div class="content" [class.medium-width]="desktopWidth < 6">
      <ng-container *ngTemplateOutlet="RecipeCategories"></ng-container>
      <ng-container *ngTemplateOutlet="RecipeCardTitle"></ng-container>
      <ng-container *ngTemplateOutlet="RecipePreparation; context: { isCentered: false }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #TopImageLeftAlignedCard>
  <div class="vertical-card">
    <ng-container *ngTemplateOutlet="RecipeCardThumbnail"></ng-container>
    <div
      [class.has-background]="hasBackground"
      class="content"
      [class.large-width]="6 <= desktopWidth && desktopWidth <= 9"
      [class.medium-width]="3 <= desktopWidth && desktopWidth <= 5"
      [class.small-width]="desktopWidth < 3"
    >
      <ng-container *ngTemplateOutlet="RecipeCategories"></ng-container>
      <ng-container *ngTemplateOutlet="RecipeCardTitle"></ng-container>
      <ng-container *ngTemplateOutlet="RecipePreparation; context: { isCentered: false }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #TopImageCenteredCard>
  <div class="vertical-card" [class.has-background]="hasBackground">
    <ng-container *ngTemplateOutlet="RecipeCardThumbnail"></ng-container>
    <div
      class="content"
      [class.large-width]="6 <= desktopWidth && desktopWidth <= 9"
      [class.medium-width]="3 <= desktopWidth && desktopWidth <= 5"
      [class.small-width]="desktopWidth < 3"
    >
      <ng-container *ngTemplateOutlet="RecipeCategories; context: { isCentered: true }"></ng-container>
      <ng-container *ngTemplateOutlet="RecipeCardTitle; context: { isCentered: true, isSidebar: isSidebar }"></ng-container>
      <ng-container *ngTemplateOutlet="RecipePreparation; context: { isCentered: true }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #RecipeCardThumbnail>
  <a [routerLink]="recipeLink" class="article-card-wrapper">
    <img
      withFocusPoint
      [data]="recipeCard?.thumbnailFocusedImages"
      [alt]="recipeCard?.thumbnail?.alt || recipeCard?.title || ''"
      [displayedUrl]="recipeCard?.thumbnail?.url || 'assets/images/placeholder.jpg'"
      [displayedAspectRatio]="{ desktop: '4:3' }"
      [class.custom-border-radius]="hasBackground"
      class="image"
      [attr.loading]="useLoadingAttribute ? (isPriorityContent ? 'eager' : 'lazy') : undefined"
    />
    <i *ngIf="recipeCard?.guarantee" class="icon mindmegette-icon-guarantee"></i>
  </a>
</ng-template>

<ng-template #RecipeCardTitle let-isCentered="isCentered" let-isSidebarTitle="isSidebar">
  <a [routerLink]="recipeLink">
    <h2
      class="title"
      [class.is-occassion-list-page]="isExperienceOccasionList && styleID === RecipeCardType.TopImageCenteredCard"
      [class.is-sidebar]="isSidebarTitle"
      [class.centered]="isCentered"
      [attr.aria-label]="recipeCard?.title"
    >
      {{ recipeCard?.title }}
    </h2>
  </a>
</ng-template>

<ng-template #RecipeCategories let-isCentered="isCentered">
  <div class="category-wrapper" [class.centered]="isCentered">
    <mindmegette-category-label [styleID]="CategoryLabelTypes.RECIPE" [title]="'Recept'"></mindmegette-category-label>
    <mindmegette-category-label *ngIf="recipeCard?.difficulty" [styleID]="CategoryLabelTypes.CATEGORY" [title]="recipeDifficulty"></mindmegette-category-label>
    <i *ngIf="recipeCard?.hasVideo" class="icon mindmegette-icon-play"></i>
  </div>
</ng-template>

<ng-template #RecipePreparation let-isCentered="isCentered">
  <div *ngIf="recipeCard?.preparation?.time" [class.centered]="isCentered" class="preparation">
    <div>{{ recipeCard?.preparation?.time }} perc</div>
    <div class="separator">|</div>
    <div>{{ recipeCard?.preparation?.servings }} adag</div>
    <ng-container *ngIf="recipeCard?.cost?.title">
      <div class="separator">|</div>
      <div>{{ recipeCard?.cost?.title }}</div>
    </ng-container>
  </div>
</ng-template>
