import { Component, effect, inject, input, signal } from '@angular/core';
import { ApiService } from '../../services';
import { CalendarDay, SecretDaysCalendar, SecretDaysCalendarComponent, SecretDaysCalendarSponsor } from '@trendency/kesma-ui';
import { addYears, isAfter, isBefore } from 'date-fns';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { MindmegetteWysiwygBoxComponent } from '@shared/components';

@Component({
  selector: 'app-secret-days-calendar-adapter',
  imports: [SecretDaysCalendarComponent, SecretDaysCalendarComponent, MindmegetteWysiwygBoxComponent],
  templateUrl: './secret-days-calendar-adapter.component.html',
  styleUrl: './secret-days-calendar-adapter.component.scss',
})
export class SecretDaysCalendarAdapterComponent {
  apiService = inject(ApiService);
  storageService = inject(StorageService);
  utilService = inject(UtilService);

  desktopWidth = input<number>(12);
  id = input<string | undefined>(undefined);
  calendar = signal<SecretDaysCalendar | undefined>(undefined);
  openedDayData = signal<CalendarDay | undefined>(undefined);
  isDayLocked = signal<boolean>(false);
  private readonly now = signal(new Date().toISOString());

  constructor() {
    effect(() => {
      const id = this.id();
      if (!id) return;
      this.apiService.getSecretDaysCalendar(id).subscribe((data) => {
        const calendar = data?.data;
        calendar.isSponsorHeaderActive = this.isSponsorshipActive(calendar.sponsorshipForHeader);
        calendar.isSponsorFooterActive = this.isSponsorshipActive(calendar.sponsorshipForFooter);
        calendar.isSponsorLeftActive = this.isSponsorshipActive(calendar.sponsorshipMainOnLeft);
        const filteredDays = calendar?.days?.filter((day: CalendarDay) => {
          const start = day.visibilityStart ? day.visibilityStart : new Date(0);
          const end = day.visibilityEnd ? day.visibilityEnd : addYears(new Date(), 1);
          return isBefore(start, this.now()) && isAfter(end, this.now());
        });
        filteredDays?.map((day: any) => {
          if (this.isOpened(day.id)) {
            day.backgroundImageUrl = day.backgroundAfterOpenImage?.fullSizeUrl ?? calendar?.defaultDayBackgroundAfterOpenImage?.fullSizeUrl;
          } else {
            day.backgroundImageUrl = day.backgroundBeforeOpenImage?.fullSizeUrl ?? calendar?.defaultDayBackgroundBeforeOpenImage?.fullSizeUrl;
          }
        });
        this.calendar.set({ ...calendar, days: filteredDays });
      });
    });
  }

  openDayModal(id: string): void {
    this.openedDayData.set(undefined);
    this.isDayLocked.set(false);
    this.apiService.getSecretDaysCalendarDay(id).subscribe(
      (data) => {
        const day: CalendarDay = data?.data;
        if (this.isOpenable(day.openableStart, day.openableEnd)) {
          const isOpened = this.isOpened(id);
          day.backgroundImageUrl = isOpened
            ? (day.backgroundAfterOpenImage?.fullSizeUrl ?? this.calendar()?.defaultDayBackgroundAfterOpenImage?.fullSizeUrl)
            : (day.backgroundBeforeOpenImage?.fullSizeUrl ?? this.calendar()?.defaultDayBackgroundBeforeOpenImage?.fullSizeUrl);
          day.isSponsorHeaderActive = this.isSponsorshipActive(day.sponsorshipHeader);
          day.isSponsorFooterActive = this.isSponsorshipActive(day.sponsorshipFooter);
          day.isSponsorLeftActive = this.isSponsorshipActive(day.sponsorshipDayOnLeft);
          if (!isOpened) {
            const calendarDays = this.calendar()?.days;
            if (calendarDays) {
              const updatedDays = calendarDays?.map((day: any): CalendarDay => {
                if (day.id === id) {
                  return {
                    ...day,
                    backgroundImageUrl: day.backgroundAfterOpenImage?.fullSizeUrl ?? this.calendar()?.defaultDayBackgroundAfterOpenImage?.fullSizeUrl,
                  };
                } else {
                  return day;
                }
              });
              this.calendar.update((current) => {
                return current ? { ...current, days: updatedDays } : undefined;
              });
            }
            if (this.utilService.isBrowser()) {
              const openedDaysIds = this.storageService.getSessionStorageData('openedDaysIds') ?? [];
              openedDaysIds.push(id);
              this.storageService.setSessionStorageData('openedDaysIds', openedDaysIds);
            }
          }
          this.openedDayData.set(day);
        } else {
          this.isDayLocked.set(true);
        }
      },
      () => {
        this.isDayLocked.set(true);
      }
    );
  }

  closeModal(): void {
    this.openedDayData.set(undefined);
  }

  isOpenable(startDate?: string, endDate?: string): boolean {
    const start = startDate ?? new Date(0);
    const end = endDate ?? addYears(new Date(), 1);
    return isBefore(start, this.now()) && isAfter(end, this.now());
  }

  isOpened(id: string): boolean {
    if (!this.utilService.isBrowser()) {
      return false;
    }
    return this.storageService.getSessionStorageData('openedDaysIds')?.includes(id);
  }

  isSponsorshipActive(sponsorship?: SecretDaysCalendarSponsor): boolean {
    if (!sponsorship) {
      return false;
    }
    const start = sponsorship.startDate?.date ?? new Date(0);
    const end = sponsorship.endDate?.date ?? addYears(new Date(), 1);
    return isBefore(start, this.now()) && isAfter(end, this.now());
  }
}
