@use 'shared' as *;

.secret-day-data {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
  padding: 15px;
  min-height: 200px;
  min-width: 200px;

  &-body {
    display: flex;
    gap: 10px;
    margin: 10px 0;

    @include media-breakpoint-down(lg) {
      display: block;
    }

    .sponsorship {
      display: inline-block;
      max-width: 30%;
      align-self: center;

      @include media-breakpoint-down(lg) {
        display: none;
      }
    }
  }
}

.day-sponsorship {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.calendar-day-notification {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  justify-content: center;
  padding: 15px;
}
