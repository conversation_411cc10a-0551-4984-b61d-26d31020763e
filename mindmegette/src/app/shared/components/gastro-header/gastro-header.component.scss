@use 'shared' as *;

header {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  background-color: var(--kui-green-700);
  display: flex;
  justify-content: center;
  flex-direction: column;

  .header-top {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 28px;

    @include media-breakpoint-down(md) {
      padding: 8px 16px;
    }

    &-logo-wrapper {
      width: 111px;
      height: 46px;
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      &-with-highlighted-sponsor {
        display: flex;
        gap: 24px;
        align-items: center;
        padding: 8px 0px;
        height: 100%;

        @include media-breakpoint-down(md) {
          gap: 16px;
          padding: 0;
        }
      }

      @include media-breakpoint-down(md) {
        gap: 16px;
      }

      .mindmegette-header-hamburger-icon {
        display: none;
        @include media-breakpoint-down(md) {
          display: block;
          width: 24px;
          height: 24px;
        }
      }

      .mindmegette-header-close-hamburger-icon {
        display: none;
        @include media-breakpoint-down(md) {
          display: block;
          width: 24px;
          height: 24px;
        }
      }
    }

    .search {
      display: flex;
      align-items: center;
      flex: 1;
      background-color: white;
      padding: 9px 16px;
      gap: 6px;
      border-radius: 100px;
      position: relative;

      .search-bar-input {
        width: 100%;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.12px;
        color: var(--kui-gray-500);
        font-family: var(--kui-font-dm-sans);
      }

      @include media-breakpoint-down(md) {
        display: none;
      }

      .live-search {
        position: absolute;
        background-color: var(--kui-white);
        top: 45px;
        left: 0;
        width: 100%;
        z-index: 1001;
        border-radius: 8px;
        border: 1px solid var(--kui-gray-100);
        padding: 8px 0;

        ul {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        &-result {
          cursor: pointer;
          padding: 0 8px;

          &-text {
            color: var(--kui-gray-500);
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            display: flex;
            justify-content: space-between;
            gap: 5px;

            .date {
              font-weight: 300;
              min-width: 110px;
            }
          }

          &:hover {
            background-color: var(--kui-green-50);
          }
        }
      }
    }

    &-right {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      flex: 1;

      .mme-button {
        display: flex;
        align-items: center;
        height: 36px;
        gap: 10px;
        border-radius: 100px;
        padding: 2px 16px 4px 16px;
        border: 1px solid rgba(255, 255, 255, 0.25);

        img {
          width: 98px;
          height: 26.226px;
          flex-shrink: 0;
        }

        kesma-icon {
          color: var(--kui-white);
        }

        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }

    &-actions-icon {
      width: 32px;
      height: 32px;
      background-color: var(--kui-white);
      padding: 6px;
      border-radius: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      @include media-breakpoint-up(md) {
        width: 36px;
        height: 36px;
      }

      i {
        width: 20px;
        height: 20px;
      }

      &:hover {
        background-color: var(--kui-green-50);
      }

      &.search-button {
        display: none;

        @include media-breakpoint-down(md) {
          display: inline-block;
        }
      }
    }
  }

  .header-bottom {
    display: flex;
    width: 100%;
    padding: 0 28px 12px 28px;
    justify-content: space-between;
    align-items: center;
    gap: 20px;

    .header-bottom-menu-list-item-link {
      color: var(--kui-white);
      text-align: center;
      font-family: var(--kui-font-secondary);
      font-size: 13px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 0.52px;
      text-transform: uppercase;
      cursor: pointer;
      width: max-content;

      &.tag {
        text-transform: none;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.12px;
      }

      &:hover {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    &-menu-list {
      max-width: 50%;
      display: flex;
      align-items: center;
    }

    &-tags {
      display: flex;
      justify-content: flex-end;
      max-width: 50%;

      ::ng-deep {
        li {
          display: flex;
          align-items: center;
          padding: 13px 12px;
          border-radius: 100px;
          border: 1px solid rgba(255, 255, 255, 0.25);
          font-size: 12px;
          font-weight: 500;
          line-height: 16px;
          letter-spacing: 0.12px;
        }
      }
    }

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  .mobile-search {
    display: flex;
    width: 100%;
    padding: 0 16px 8px 16px;
    justify-content: center;
    align-items: center;
    position: relative;

    &-bar {
      display: flex;
      width: 100%;
      padding: 6px 16px;
      background-color: var(--kui-white);
      align-items: center;
      gap: 6px;
      border-radius: 100px;

      &-input {
        width: 100%;
      }

      .live-search {
        position: absolute;
        top: 35px;
        margin: 0 16px;
        left: 0;
        background-color: var(--kui-white);
        z-index: 1001;
        border-radius: 8px;
        border: 1px solid var(--kui-gray-100);
        padding: 8px 0;
        width: calc(100% - (2 * 16px));

        ul {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        &-result {
          cursor: pointer;
          padding: 0 8px;

          &-text {
            color: var(--kui-gray-500);
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            display: flex;
            justify-content: space-between;
            gap: 5px;

            .date {
              font-weight: 300;
              min-width: 110px;
            }
          }
        }
      }
    }
  }

  .mobile-menu {
    width: 100%;
    height: calc(100vh - 62px);
    z-index: 1000;
    background-color: var(--kui-white);
    overflow-y: scroll;

    &-list {
      padding: 4px 8px;

      &-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        &-link {
          width: 100%;
          padding: 10px 4px 10px 8px;
          display: flex;
          justify-content: space-between;
          font-family: var(--kui-font-secondary);
          color: var(--kui-gray-400);
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          line-height: 20px;
          letter-spacing: 0.7px;
          text-transform: uppercase;
        }
      }
    }
  }
}

.highlighted-sponsor {
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 6px 28px;
  margin-bottom: 10px;

  @include media-breakpoint-down(md) {
    display: none;
  }

  &-title {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;

    @include media-breakpoint-down(md) {
      font-size: 11px;
      line-height: 14px;
      letter-spacing: 0.11px;
    }
  }

  &-thumbnail {
    height: 32px;
    border-radius: 4px;
    width: auto;

    @include media-breakpoint-down(md) {
      height: 24px;
    }

    &.header-bottom {
      height: 24px;
    }
  }
}
