@use 'shared' as *;

:root {
  position: relative;

  .flatpickr {
    + .mindmegette-icon-close-gray2 {
      width: 14px;
      height: 14px;
      right: 40px;
      margin-bottom: 3px;
    }

    &-custom-content {
      font-weight: 600;
      position: absolute;
      left: 0;
      right: 0;
      top: 16px;
      font-family: var(--kui-font-secondary);
      color: var(--kui-gray-950);
    }

    &-custom-footer {
      display: flex;
      align-items: center;
      border-top: 1px solid var(--kui-gray-200);
      padding: 10px;
      gap: 10px;

      mindmegette-simple-button {
        width: 100%;
      }

      button {
        padding: 8px 16px;
      }
    }
  }

  .flatpickr-calendar {
    border-radius: 8px;
    margin: 4px 0;
    border: 1px solid var(--kui-gray-200);
    box-shadow: 4px 4px 16px 0 rgba(0, 0, 0, 0.06);

    &.open {
      z-index: 5;
    }

    &:before,
    &:after {
      display: none;
    }

    .flatpickr-months {
      display: flex;
      justify-content: space-between;
      margin: 10px;

      .flatpickr-month {
        height: initial;
      }

      &.noDropdowns {
        .flatpickr-month {
          display: none;
        }
      }

      .flatpickr-prev-month,
      .flatpickr-next-month {
        position: relative;
        height: 36px;
        width: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--kui-green-700);
        border-radius: 8px;

        svg {
          width: 12px;
          height: 12px;
          fill: var(--kui-white);
        }
      }
    }

    .flatpickr-current-month {
      display: flex;
      align-items: center;
      width: initial;
      position: relative;
      left: initial;
      margin: 0 8px;
      padding: 0;
      gap: 8px;
      height: 36px;

      .flatpickr-monthDropdown-months {
        border: 1px solid var(--kui-gray-200);
        border-radius: 8px;
        font-family: var(--kui-font-secondary);
        color: var(--kui-gray-500);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin: 0;
        padding: 0 8px;
        order: 2;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none'%3E%3Cpath d='M14.1671 7.64174C14.0109 7.48653 13.7997 7.39941 13.5796 7.39941C13.3594 7.39941 13.1482 7.48653 12.9921 7.64174L10.0004 10.5917L7.0504 7.64174C6.89427 7.48653 6.68306 7.39941 6.4629 7.39941C6.24275 7.39941 6.03154 7.48653 5.8754 7.64174C5.7973 7.71921 5.7353 7.81138 5.693 7.91293C5.65069 8.01448 5.62891 8.1234 5.62891 8.23341C5.62891 8.34342 5.65069 8.45234 5.693 8.55389C5.7353 8.65544 5.7973 8.74761 5.8754 8.82507L9.40874 12.3584C9.48621 12.4365 9.57837 12.4985 9.67992 12.5408C9.78147 12.5831 9.89039 12.6049 10.0004 12.6049C10.1104 12.6049 10.2193 12.5831 10.3209 12.5408C10.4224 12.4985 10.5146 12.4365 10.5921 12.3584L14.1671 8.82507C14.2452 8.74761 14.3072 8.65544 14.3495 8.55389C14.3918 8.45234 14.4136 8.34342 14.4136 8.23341C14.4136 8.1234 14.3918 8.01448 14.3495 7.91293C14.3072 7.81138 14.2452 7.71921 14.1671 7.64174Z' fill='%236D6D6D'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 4px center;
        background-size: 20px 20px;
        min-width: 120px;

        &:hover {
          background-color: transparent;
        }
      }

      .numInputWrapper {
        border: 1px solid var(--kui-gray-200);
        border-radius: 8px;
        margin: 0;
        padding: 0 8px;
        width: initial;
        order: 1;

        &:hover {
          background: initial;
        }

        input.cur-year {
          height: 33px;
          padding: 0;
          font-family: var(--kui-font-secondary);
          color: var(--kui-gray-500);
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
        }

        span {
          border-color: var(--kui-gray-200);
          opacity: 1;

          &:hover {
            background: initial;
          }

          &.arrowUp {
            border-top: 0;
            border-right: 0;

            &:after {
              border-bottom-color: var(--kui-gray-500);
            }
          }

          &.arrowDown {
            border-right: 0;
            border-bottom: 0;

            &:after {
              border-top-color: var(--kui-gray-500);
            }
          }
        }
      }
    }

    .flatpickr-innerContainer {
      margin-left: -1px;

      .flatpickr-rContainer {
        .flatpickr-weekdays {
          .flatpickr-weekdaycontainer {
            .flatpickr-weekday {
              color: var(--kui-gray-950);
              font-family: var(--kui-font-secondary);
            }
          }
        }

        .flatpickr-days {
          .dayContainer {
            .flatpickr-day {
              font-family: var(--kui-font-secondary);
              color: var(--kui-gray-950);

              &.selected {
                background-color: var(--kui-green-700);
                border-color: var(--kui-green-700);
                color: var(--kui-white);
                border-radius: 8px;
              }

              &.today {
                border: none;
              }

              &:hover:not(.selected) {
                background-color: var(--kui-white);
                border-color: var(--kui-green-800);
                color: var(--kui-gray-950);
                border-radius: 8px;
              }

              &.prevMonthDay,
              &.nextMonthDay {
                color: var(--kui-gray-500);

                &:hover {
                  color: var(--kui-gray-500);
                }

                &.selected {
                  color: var(--kui-white);
                }
              }

              &.flatpickr-disabled {
                color: var(--kui-gray-300);

                &:hover {
                  background-color: transparent;
                  border-color: transparent;
                  color: var(--kui-gray-300);
                }
              }
            }
          }
        }
      }
    }

    .flatpickr-time {
      border-color: var(--kui-gray-200);

      .numInputWrapper {
        input {
          color: var(--kui-gray-950);
          font-family: var(--kui-font-secondary);

          &:hover,
          &:focus {
            background-color: var(--kui-gray-50);
          }
        }
      }
    }
  }
}
