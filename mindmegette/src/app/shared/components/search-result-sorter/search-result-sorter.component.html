<ng-select
  [(ngModel)]="activeSorts['content_types[]']"
  bindValue="value"
  [searchable]="false"
  [clearable]="false"
  [items]="contentTypeSorts"
  (change)="onChangeSelect()"
  aria-label="Szűrés tartalomtípus szerint"
  *ngIf="isVisible('contentType')"
>
</ng-select>

<ng-select
  [(ngModel)]="activeSorts[this.searchBackendKey]"
  bindValue="value"
  [searchable]="false"
  [clearable]="false"
  [items]="publishDateSorts"
  (change)="onChangeSelect()"
  *ngIf="isVisible('publishDate')"
  ariaLabel="Sorbarendezési beállítások"
>
</ng-select>
