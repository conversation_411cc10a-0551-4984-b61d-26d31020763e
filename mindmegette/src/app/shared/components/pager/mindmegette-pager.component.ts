import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { PagerComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'mindmegette-pager',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.html',
  styleUrls: ['./../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.scss', './mindmegette-pager.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink],
})
export class MindmegettePagerComponent extends PagerComponent {
  @Input() override showFirstPage = true;
  @Input() override showLastPage = true;
  @Input() override hideFirstPageInParams = true;
  @Input() override allowQueryParamFiltering: boolean = true;
  @Input() override allowedQueryParams: string[] = [
    'page',
    'publishDate_order[]',
    'global_filter',
    'order',
    'content_types[]',
    'search',
    'title_filter',
    'initial_filter',
    'sort',
    'column',
    'total_time',
    'isMmeWarranty',
    'experience_slug_filter',
    'category_ids[]',
    'occasionDateFrom_filter',
    'occasionDateTo_filter',
    'remainingNumberOfSeats_filter',
    'author_id_filter',
  ];
}
