import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, signal, viewChild } from '@angular/core';
import { BaseComponent, IconComponent, KesmaSwipeComponent } from '@trendency/kesma-ui';
import { MindmegetteArticleCardType, RecipeCardType } from '@shared/definitions';

@Component({
  selector: 'mindmegette-tag-as-button',
  templateUrl: './tag-as-button.component.html',
  styleUrls: ['./tag-as-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, KesmaSwipeComponent],
})
export class TagAsButtonComponent extends BaseComponent<{ title: string }[]> implements OnInit {
  private readonly swipeComponent = viewChild(KesmaSwipeComponent);
  @Input() desktopWidth = 12; // Value from 1-12 to indicate the width of the card on desktop.
  @Input() startingTagIndex = 0;
  selectedTagIndex = signal<number>(0);
  @Output() tagSelected = new EventEmitter<number>();

  readonly breakpoint: number = 4;

  override ngOnInit(): void {
    super.ngOnInit();
    this.selectedTagIndex.set(this.startingTagIndex);
  }

  onTagClicked(index: number): void {
    this.selectedTagIndex.set(index);
    this.tagSelected.emit(index);
    this.swipeComponent()?.swipeTo(index);
  }

  protected readonly ArticleCardType = MindmegetteArticleCardType;
  protected readonly RecipeCardType = RecipeCardType;
}
