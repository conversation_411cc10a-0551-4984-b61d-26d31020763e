<ng-template #itemTemplate let-tag="data" let-i="index">
  <button (click)="onTagClicked(i)" [class.selected]="selectedTagIndex() === i" class="tag">{{ tag?.title }}</button>
</ng-template>
<ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" /></ng-template>
<ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" /></ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="false"
  [usePagination]="false"
  [data]="data"
  [breakpoints]="{
    default: {
      itemCount: 'auto',
      gap: '8px',
    },
  }"
></div>
