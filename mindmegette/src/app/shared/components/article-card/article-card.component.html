<article *ngIf="articleCard">
  <ng-container [ngSwitch]="styleID">
    <ng-container *ngSwitchCase="ArticleCardType.TopImageLeftAlignedCard" [ngTemplateOutlet]="TopImageLeftAlignedCard"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.TopImageCenteredCard" [ngTemplateOutlet]="TopImageCenteredCard"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.TopImageWithLeadCenteredCard" [ngTemplateOutlet]="TopImageWithLeadCenteredCard"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.LeftImageShortCard" [ngTemplateOutlet]="LeftImageShortCard"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.LeftImageShortCardAuthor" [ngTemplateOutlet]="LeftImageShortCardAuthor"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.LeftImageLongCard" [ngTemplateOutlet]="LeftImageLongCard"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.NoImageCard" [ngTemplateOutlet]="NoImageCard"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.ExternalRecommendation" [ngTemplateOutlet]="ExternalRecommendation"></ng-container>
  </ng-container>
</article>

<ng-template #TopImageLeftAlignedCard>
  <div class="vertical-card" [class.flex-column]="isUsedInGuaranteeBox && desktopWidth < breakpoint">
    <ng-container *ngTemplateOutlet="ArticleCardThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
    <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" class="content">
      <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel"></ng-container>
      <ng-container *ngTemplateOutlet="ArticleCardTitle"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #TopImageCenteredCard>
  <div class="vertical-card">
    <ng-container *ngTemplateOutlet="ArticleCardThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
    <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" class="content">
      <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel; context: { isCentered: true }"></ng-container>
      <ng-container *ngTemplateOutlet="ArticleCardTitle; context: { isCentered: true, isSidebar: isSidebar }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #TopImageWithLeadCenteredCard>
  <div class="vertical-card">
    <ng-container *ngTemplateOutlet="ArticleCardThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
    <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" class="content">
      <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel; context: { isCentered: true }"></ng-container>
      <ng-container *ngTemplateOutlet="ArticleCardTitle; context: { isCentered: true, isSidebar: isSidebar }"></ng-container>
      <ng-container *ngTemplateOutlet="ArticleCardLead"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #LeftImageShortCard>
  <ng-container *ngIf="desktopWidth <= breakpoint; else initialCard">
    <ng-container *ngTemplateOutlet="TopImageLeftAlignedCard"></ng-container>
  </ng-container>
  <ng-template #initialCard>
    <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" [class.flex-column]="isUsedInGuaranteeBox" class="horizontal-card">
      <ng-container *ngTemplateOutlet="ArticleCardThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
      <div class="content">
        <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel; context: { isLive: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleCardTitle"></ng-container>
      </div>
    </div>
  </ng-template>
</ng-template>

<ng-template #LeftImageShortCardAuthor>
  <ng-container *ngIf="desktopWidth <= breakpoint; else initialCard">
    <ng-container *ngTemplateOutlet="TopImageLeftAlignedCard"></ng-container>
  </ng-container>
  <ng-template #initialCard>
    <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" class="horizontal-card">
      <ng-container *ngTemplateOutlet="ArticleCardThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
      <div class="content">
        <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel; context: { isLive: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleCardTitle"></ng-container>
      </div>
    </div>
  </ng-template>
</ng-template>

<ng-template #LeftImageLongCard>
  <ng-container *ngIf="desktopWidth <= breakpoint; else initialCard">
    <ng-container *ngTemplateOutlet="TopImageLeftAlignedCard"></ng-container>
  </ng-container>
  <ng-template #initialCard>
    <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" class="horizontal-card">
      <ng-container *ngTemplateOutlet="ArticleCardThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
      <div class="content">
        <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel; context: { isLive: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleCardTitle"></ng-container>
      </div>
    </div>
  </ng-template>
</ng-template>

<ng-template #NoImageCard>
  <div [class.has-background]="hasBackground || isPrBlock" [class.pr-block]="isPrBlock" class="horizontal-card">
    <div class="content">
      <ng-container *ngTemplateOutlet="isPrBlock ? ArticleCardPRLabel : ArticleCardLabel; context: { isLive: true }"></ng-container>
      <ng-container *ngTemplateOutlet="ArticleCardTitle"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #ExternalRecommendation>
  <div [class.has-background]="hasBackground || isPrBlock" class="vertical-card">
    <a [href]="data?.url" target="_blank" class="external">
      <div class="url-name">{{ data?.category?.name }}</div>
      <img
        [alt]="articleCard?.thumbnail?.alt || articleCard?.title || ''"
        [src]="articleCard?.thumbnail?.url || 'assets/images/mme-placeholder.jpg'"
        class="thumbnail"
        loading="lazy"
      />
      <div class="content">
        <div class="title">{{ articleCard?.title }}</div>
      </div>
    </a>
  </div>
</ng-template>

<ng-template #ArticleCardThumbnail let-displayedAspectRatio="displayedAspectRatio">
  <a [routerLink]="articleLink" class="article-card-wrapper" [class.offer-box-thumbnail]="isInOfferBox">
    <img
      withFocusPoint
      [data]="articleCard?.thumbnailFocusedImages"
      [displayedAspectRatio]="displayedAspectRatio"
      [alt]="articleCard?.thumbnail?.alt || articleCard?.title || ''"
      [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/mme-placeholder.jpg'"
      [class.custom-border-radius]="(hasBackground || isPrBlock) && !isInOfferBox"
      [class.offer-box-thumbnail-rounded]="isInOfferBox"
      class="image"
      [class.is-guarantee-box]="isUsedInGuaranteeBox"
      [loading]="isPriorityContent ? 'eager' : 'lazy'"
    />
    <a *ngIf="articleCard?.minuteToMinute === MinuteToMinuteState.RUNNING" [routerLink]="columnLink" class="icon-live-wrapper">
      <span>Élő</span>
      <i class="icon mindmegette-icon-live"></i>
    </a>
    <i *ngIf="articleCard?.isGuaranteeType" class="icon mindmegette-icon-guarantee"></i>
  </a>
</ng-template>

<ng-template #ArticleCardPRLabel let-isCentered="isCentered">
  <div [class.centered]="isCentered" class="pr-label-wrapper">
    <a [routerLink]="columnLink" class="pr-label-wrapper-label">{{ overrideColumnTitle ?? 'PR CIKK' }}</a>
  </div>
</ng-template>
<ng-template #ArticleCardLabel let-isCentered="isCentered" let-isLive="isLive">
  <div [class.centered]="isCentered" class="label-wrapper" *ngIf="hasLabel(isLive)">
    <a *ngIf="articleCard?.columnTitle" [routerLink]="columnLink" class="label-wrapper-label">{{ overrideColumnTitle ?? articleCard?.columnTitle }}</a>
    <a *ngIf="isLive && articleCard?.minuteToMinute === MinuteToMinuteState.RUNNING" [routerLink]="columnLink" class="label-wrapper-live">
      <i class="icon mindmegette-icon-live"></i>
      <span class="label-wrapper-live-text">Élő</span>
    </a>
    <i *ngIf="articleCard?.isAdultsOnly" class="icon mindmegette-icon-adult-only"></i>
    <i *ngIf="articleCard?.isVideoType" class="icon mindmegette-icon-play"></i>
    <i *ngIf="articleCard?.hasGallery" class="icon mindmegette-icon-gallery"></i>
  </div>
</ng-template>

<ng-template #ArticleCardTitle let-isCentered="isCentered" let-isSidebarTitle="isSidebar">
  <a [routerLink]="articleLink" class="title-wrapper">
    <h2
      class="title"
      [class.is-sidebar]="isSidebarTitle"
      [class.centered]="isCentered"
      [class.pr-block]="isPrBlock"
      [attr.aria-label]="articleCard?.recommendedTitle || articleCard?.title"
    >
      {{ articleCard?.recommendedTitle || articleCard?.title }}
    </h2>
  </a>
</ng-template>

<ng-template #ArticleCardLead>
  <a *ngIf="articleCard?.lead" [routerLink]="articleLink" class="title-wrapper">
    <p [class.pr-block]="isPrBlock" class="lead">{{ articleCard?.lead }}</p>
  </a>
</ng-template>
