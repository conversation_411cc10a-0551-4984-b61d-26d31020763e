@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  margin-bottom: 20px;

  //isGuaranteeType
  .mindmegette-icon-guarantee {
    width: 104px;
    height: 104px;
    position: absolute;
    border-top-left-radius: 8px;
    top: 0;
    left: 0;
  }

  @include media-breakpoint-up(md) {
    margin-bottom: 24px;
  }

  &:hover {
    .title {
      color: var(--kui-green-700);
    }

    .pr-block {
      color: var(--kui-gray-100);
    }

    .image {
      transform: scale(1.1);
    }
  }

  .article-card-wrapper {
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 8px;
  }

  .image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    transition: 0.3s;
  }

  .icon-live-wrapper {
    position: absolute;
    z-index: 1;
    top: 12px;
    right: 12px;
    width: 100%;
    max-width: 60px;
    min-height: 30px;
    background-color: var(--kui-gray-950-o30);
    border-radius: 20px;
    padding: 4px 8px 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    span {
      color: var(--kui-white);
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 500;
    }

    .mindmegette-icon-live {
      width: 16px;
      height: 16px;
    }
  }

  .label-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;

    .icon {
      width: 20px;
      height: 20px;
      min-width: 20px;
      min-height: 20px;
    }

    &-left,
    &-right {
      display: flex;
      gap: 7px;
      align-items: center;
      flex-wrap: wrap;
    }

    &-label {
      padding: 2px 6px;
      border-radius: 4px;
      background-color: var(--kui-pink-500);
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      color: var(--kui-white);
      text-transform: uppercase;
      text-align: center;
    }

    &-live {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid var(--kui-pink-500);

      .mindmegette-icon-live {
        width: 16px;
        height: 16px;
      }

      &-text {
        font-family: var(--kui-font-secondary);
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        color: var(--kui-pink-500);
        text-transform: uppercase;
      }
    }

    .mindmegette-icon-play {
      margin-left: auto;
    }
  }

  .title {
    font-family: var(--kui-font-secondary);
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    color: var(--kui-gray-950);
    transition: 0.1s color ease-in;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    @include media-breakpoint-down(sm) {
      font-size: 18px;
      line-height: 24px;
    }
  }

  .lead {
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    text-align: center;
    line-height: 24px;
    color: var(--kui-gray-950);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }

  .pr-block {
    background-color: var(--kui-pink-500) !important;
    color: var(--kui-white);
    border-radius: 0 0 8px 8px !important;
    border: 0 !important;
  }

  .pr-label-wrapper {
    @extend .label-wrapper;
    margin-bottom: 6px;

    &-label {
      @extend .label-wrapper-label;
      background-color: var(--kui-white);
      color: var(--kui-pink-500);
    }
  }

  .vertical-card {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
  }

  .horizontal-card {
    @extend .vertical-card;
    flex-direction: row;
  }

  .has-background {
    border: 1px solid var(--kui-gray-100);
    border-radius: 0 0 8px 8px;
    border-top: 0;
    background: var(--kui-white);
  }

  .custom-border-radius {
    border-radius: 8px 8px 0 0 !important;
  }

  .centered {
    justify-content: center;
    text-align: center;

    .mindmegette-icon-play {
      margin-left: unset !important;
    }
  }

  .content {
    width: 100%;
    padding-top: 16px;
    @include media-breakpoint-down(sm) {
      padding-top: 12px;
    }
  }

  .external {
    font-family: var(--kui-font-secondary);
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    text-transform: uppercase;
    color: var(--kui-gray-600);
    margin-bottom: 4px;

    .image {
      transform: none;
    }
  }

  &.style-TopImageLeftAlignedCard {
    .label-wrapper {
      margin-bottom: 8px;
    }

    .title {
      margin-bottom: 8px;

      @include media-breakpoint-down(xs) {
        font-size: 20px;
      }
    }

    .has-background {
      padding: 16px;
    }
  }

  &.style-TopImageCenteredCard {
    .label-wrapper {
      margin-bottom: 8px;
    }

    .title {
      @include media-breakpoint-down(sm) {
        font-size: 20px !important;
        line-height: 26px !important;
      }
    }

    .lead {
      margin-bottom: 16px;
    }

    .is-sidebar {
      font-family: var(--kui-font-secondary);
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
    }

    .has-background {
      padding: 16px;
    }
  }

  &.style-TopImageWithLeadCenteredCard {
    .label-wrapper {
      margin-bottom: 8px;
    }

    .lead {
      margin-bottom: 16px;
    }

    .content {
      padding-left: 0;
      border: none;
    }

    .has-background {
      border: 1px solid var(--kui-gray-100);
      border-radius: 0 0 8px 8px;
      padding: 16px;
    }
  }

  &.style-LeftImageShortCard {
    .mindmegette-icon-guarantee {
      width: 44px;
      height: 44px;
    }
    .content {
      min-height: 100%;
      padding-left: 16px;
    }

    .content {
      flex: 2;
    }

    .image {
      width: 88px;
      height: 88px;
    }

    .is-guarantee-box {
      width: 100%;
    }

    .icon-live-wrapper {
      display: none;
    }

    .label-wrapper {
      margin-bottom: 8px;
    }

    .title {
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      -webkit-line-clamp: 2;
    }

    .has-background {
      border-top: 1px solid var(--kui-gray-100);
      padding: 8px;
      border-radius: 8px;
    }

    .custom-border-radius {
      border-radius: 8px 0 0 8px !important;
    }

    .offer-box-thumbnail-rounded {
      border-radius: 6px;
    }

    .offer-box-thumbnail {
      width: 88px;
    }

    .pr-block {
      border-radius: 8px !important;
    }
  }

  &.style-LeftImageShortCardAuthor {
    .content {
      min-height: 100%;
    }

    .horizontal-card {
      gap: 24px;

      @include media-breakpoint-down(xs) {
        gap: 12px;
        flex-direction: column;
      }
    }

    .content {
      flex: 2;
    }

    .image {
      border-radius: 8px;
      width: 180px;
      height: 134px;
      min-width: 88px;
      min-height: 88px;
    }

    .icon-live-wrapper {
      display: none;
    }

    .label-wrapper {
      margin-bottom: 8px;
    }

    .title {
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      -webkit-line-clamp: 2;
    }

    .has-background {
      border-radius: 8px;
      border: none;
    }

    .custom-border-radius {
      border-radius: 8px !important;
    }
  }

  &.style-LeftImageLongCard {
    .mindmegette-icon-guarantee {
      width: 44px;
      height: 44px;
    }
    .article-card-wrapper {
      flex-shrink: 0;
    }
    .image {
      max-width: 180px;
      @include media-breakpoint-down(sm) {
        max-width: 150px;
      }
    }

    .content {
      padding-left: 16px;
    }

    .icon-live-wrapper {
      display: none;
    }

    .label-wrapper {
      margin-bottom: 8px;
    }

    .title {
      margin-bottom: 8px;
      font-size: 18px;
    }

    .has-background {
      border-top: 1px solid var(--kui-gray-100);
      border-radius: 8px;

      .content {
        padding-right: 16px;
      }
    }

    .custom-border-radius {
      border-radius: 8px 0 0 8px !important;
    }

    .pr-block {
      border-radius: 8px !important;
    }
  }

  &.style-NoImageCard {
    .content {
      padding: 0;
    }

    .label-wrapper {
      margin-bottom: 8px;
    }

    .title {
      margin-bottom: 8px;
      font-size: 20px;
    }

    .has-background {
      border: 1px solid var(--kui-gray-100);
      border-radius: 8px;
      padding: 16px;
    }
  }

  &.style-ExternalRecommendation {
    &,
    .external {
      margin-bottom: 0;
    }

    .image {
      aspect-ratio: 4/3;
      margin: 8px 0;
      max-width: 322px;
      max-height: 234px;
    }

    .content {
      min-height: unset;
      padding: 0;

      .title {
        font-size: 16px;
        line-height: 24px;
        text-transform: none;

        @include media-breakpoint-down(sm) {
          font-size: 14px;
          line-height: 20px;
        }
      }
    }

    .custom-border-radius {
      border-radius: 8px 0 0 8px !important;
    }
  }

  &.style-guarantee-box {
    margin-bottom: 0 !important;

    &:hover {
      .image {
        transform: scale(1.1);
      }
    }

    .article-card-wrapper,
    .image {
      width: 88px;
      min-width: 88px;
    }

    .vertical-card {
      flex-direction: row;
    }

    .flex-column {
      @media screen and (min-width: 992px) and (max-width: 1200px) {
        flex-direction: column;

        .article-card-wrapper,
        .image {
          width: 100%;
        }

        .image {
          height: 100%;
        }

        .content {
          padding: 16px 0px 0px;
        }
      }
    }

    .image {
      transition: transform 0.3s;
    }
  }
}

%title {
  font-weight: 600;
  line-height: 40px;
  margin-bottom: 12px;
}
