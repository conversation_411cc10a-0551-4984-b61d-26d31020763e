<ng-container *ngIf="formGroup && controlName" [formGroup]="formGroup">
  <kesma-form-control>
    @if (isOccasionSearchPage) {
      <h3 *ngIf="label" class="mindmegette-form-label">{{ label }}</h3>
    } @else {
      <label *ngIf="label" class="mindmegette-form-label" for="{{ id ?? 'select' }}"> {{ label }} <strong *ngIf="isRequired">*</strong></label>
    }

    <ng-select
      #select
      [ariaLabel]="ariaLabel ?? ''"
      (scroll)="onScroll.emit($event)"
      (scrollToEnd)="onScrollToEnd.emit()"
      (search)="setSearchParams($event)"
      [bindLabel]="bindLabel ?? 'label'"
      [bindValue]="$any(bindValue)"
      [clearable]="clearable"
      [formControlName]="controlName"
      [id]="id ?? 'select'"
      [items]="options ?? null"
      [loading]="loading"
      [multiple]="multiple"
      [placeholder]="$any(placeholder)"
      [searchable]="searchable"
      [selectOnTab]="true"
      [typeahead]="allowBackendSearch ? $any(typeahead$) : undefined"
      [virtualScroll]="true"
      class="mindmegette-form-select"
      clearAllText="Törlés"
      loadingText="Kérjük várj..."
      notFoundText="Nincs találat a keresésre"
      typeToSearchText="Keresés..."
    >
      <ng-template let-clear="clear" let-item="item" ng-label-tmp>
        <span class="ng-value-label">{{ bindLabel ? item[bindLabel] : item }}</span>
        <span (click)="clear(item)" class="ng-value-icon right">
          <img alt="Törlés" loading="lazy" src="/assets/images/icons/mindmegette-icon-close-white.svg" />
        </span>
      </ng-template>
      <ng-template
        *ngIf="
          allowAddNew &&
          !loading &&
          (searchTerm?.length || 0) > 0 &&
          ((!allowBackendSearch && filteredItems?.length === 0) || (allowBackendSearch && options?.length === 0))
        "
        ng-footer-tmp
      >
        <mindmegette-simple-button (click)="onAdd.emit(searchTerm); select.close()" color="primary"
          >Hozzáadás újként: {{ searchTerm }}</mindmegette-simple-button
        >
      </ng-template>
    </ng-select>
  </kesma-form-control>
</ng-container>
