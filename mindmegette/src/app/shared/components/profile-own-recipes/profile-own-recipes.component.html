<h2 class="title">
  <div>
    @if ((limitable?.pageCurrent || 0) + 1; as page) {
      @if (page > 1 && showPageInTitle) {
        {{ page }}. oldal -
      }
    }
    {{ title }} <sup class="count">({{ limitable?.rowAllCount || 0 }})</sup>
  </div>
  <div class="icons">
    <i
      class="icon"
      [ngClass]="searchBarVisible ? 'mindmegette-icon-search-green' : 'mindmegette-icon-search-gray'"
      (click)="searchBarVisible = !searchBarVisible"
      title="Keresés"
    ></i>
    <i
      class="icon"
      [ngClass]="sortBarVisible ? 'mindmegette-icon-sort-green' : 'mindmegette-icon-sort-gray'"
      (click)="sortBarVisible = !sortBarVisible"
      title="Rendezés"
    ></i>
  </div>
</h2>

<div class="hidden-content-wrapper" [class.is-open]="searchBarVisible">
  <div class="hidden-content">
    <app-search-bar (handleSearchResult)="onFilterChange($any($event))" searchBackendKey="global_filter" searchButtonText="Recept keresése"></app-search-bar>
  </div>
</div>

<div class="hidden-content-wrapper" [class.is-open]="sortBarVisible">
  <div class="hidden-content swiper-container">
    <mindmegette-tag-as-button [data]="sortList" (tagSelected)="onSortChange($event)"></mindmegette-tag-as-button>
  </div>
</div>

<div class="recipes" [class.has-filter]="sortBarVisible || searchBarVisible">
  <ng-container *ngFor="let recipe of recipes; let i = index">
    <mindmegette-recipe-card [styleID]="RecipeCardType.TopImageLeftAlignedCard" [data]="recipe" [hasBackground]="true"></mindmegette-recipe-card>

    @if (i === 5) {
      @if (adverts()?.desktop?.roadblock_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
    }

    @if (i === 11) {
      @if (adverts()?.desktop?.roadblock_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
    }

    @if (i === 17) {
      @if (adverts()?.desktop?.roadblock_3; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_3; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
    }
  </ng-container>
</div>

<mindmegette-pager
  [rowAllCount]="limitable?.rowAllCount!"
  [rowOnPageCount]="limitable?.rowOnPageCount!"
  [isListPager]="true"
  [isCountPager]="false"
  [hasFirstLastButton]="false"
  [hasSkipButton]="true"
  [allowAutoScrollToTop]="true"
  [maxDisplayedPages]="5"
  *ngIf="limitable && limitable?.pageMax! > 0"
>
</mindmegette-pager>
