@use 'shared' as *;

footer {
  width: 100%;
  background: var(--kui-green-700);
  color: var(--kui-white);
  font-family: var(--kui-font-secondary);

  .footer-container {
    margin: 0px 16px;
    padding: 20px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;

    button {
      width: 100%;
    }

    &:not(:last-child) {
      border-bottom: 1px solid var(--kui-white);
    }

    &.banner-container {
      gap: 20px;

      .social-icons-container {
        display: flex;
        justify-content: space-between;
        gap: 16px;

        > * {
          width: 32px;
        }
      }

      > .btn {
        margin-top: 20px;
      }
    }

    &.top-menu-container {
      flex-wrap: wrap;
      gap: 24px;
      line-height: 24px;

      > * {
        min-width: fit-content;
      }

      .category {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 18px;
        cursor: pointer;
      }

      .item-list {
        margin-top: 16px;
        flex-direction: column;
        row-gap: 8px;
        align-items: center;
        display: none;

        &.opened {
          display: flex;
        }
      }
    }

    &.bottom-menu-container {
      gap: 12px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .footer-menu-link {
      padding: 4px 0;
      cursor: pointer;
      color: var(--kui-white);
      font-size: inherit;

      &:hover {
        color: var(--kui-green-200);
      }
    }
  }

  .separator-vertical {
    display: none;
  }

  @include media-breakpoint-up(lg) {
    .footer-container {
      flex-direction: row;
      margin: 0;
      padding: 32px 62px;
      border-bottom: none !important;

      &.banner-container {
        padding-top: 28px;
        padding-bottom: 4px;

        .logo {
          padding-top: 13px;
        }

        img {
          margin-right: auto;
          cursor: pointer;
        }

        > .btn {
          margin-top: 0;
        }
      }

      &.top-menu-container {
        align-items: flex-start;

        .menu-item > * {
          justify-content: left;
        }

        .category img {
          display: none;
        }

        .item-list {
          display: flex;
          align-items: unset;
        }
      }

      &.bottom-menu-container {
        padding-top: 16px;
        padding-bottom: 32px;

        .separator-vertical {
          display: inline-block;
        }
      }

      button {
        width: fit-content;
      }
    }
  }
}
