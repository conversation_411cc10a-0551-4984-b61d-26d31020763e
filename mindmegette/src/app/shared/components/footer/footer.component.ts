import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { SimplifiedMenuItem } from '@trendency/kesma-ui';
import { Router, RouterLink } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgIf, NgTemplateOutlet, RouterLink],
})
export class FooterComponent {
  @Input() footerMenuTop: ({ isOpen?: boolean } & SimplifiedMenuItem)[] = [];
  @Input() footerMenuBottom: SimplifiedMenuItem[] = [];
  @Output() openCookieSettings: EventEmitter<void> = new EventEmitter<void>();
  icons: { src: string; url: string; title: string }[];
  fixMenus: { title: string; action: () => void }[] = [
    { title: 'Süti beállítások', action: (): void => this.onOpenCookieSettings() },
    { title: 'Panaszkezelés', action: (): void => this.onComplaint() },
  ];
  private readonly utils = inject(UtilService);

  constructor(private readonly router: Router) {
    this.initIcons();
  }

  onSubscribe(): void {
    this.router.navigate(['/', 'hirlevel-feliratkozas']);
  }
  onComplaint(): void {
    this.router.navigate(['/', 'panaszkezeles']);
  }

  onLogoClick(): void {
    if (this.router.url === '/') {
      if (this.utils.isBrowser()) {
        window.location.href = '/';
      }
    } else {
      this.router.navigate(['/']);
    }
  }

  private onOpenCookieSettings(): void {
    this.openCookieSettings.emit();
  }

  private initIcons(): void {
    this.icons = [
      {
        src: '/assets/images/icons/facebook-icon.svg',
        url: 'https://www.facebook.com/mindmegette/',
        title: 'Facebook icon',
      },
      {
        src: '/assets/images/icons/videa-icon.svg',
        url: 'https://videa.hu/csatornak/mindmegette-399',
        title: 'Videa icon',
      },
      {
        src: '/assets/images/icons/youtube-icon.svg',
        url: 'https://www.youtube.com/@MindmegetteReceptek',
        title: 'Youtube icon',
      },
      {
        src: '/assets/images/icons/instagram-icon.svg',
        url: 'https://www.instagram.com/mindmegette.hu/',
        title: 'Instagram icon',
      },
      {
        src: '/assets/images/icons/tiktok-icon.svg',
        url: 'https://www.tiktok.com/@mindmegette.hu',
        title: 'TikTok icon',
      },
      {
        src: '/assets/images/icons/pinterest-icon.svg',
        url: 'https://hu.pinterest.com/mindmegettehu/',
        title: 'Pinterest icon',
      },
    ];
  }
}
