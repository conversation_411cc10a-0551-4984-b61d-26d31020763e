<div class="menu-wrapper">
  <div class="description" *ngIf="blockTitle || showCurrentDay">
    <div class="block-title" *ngIf="blockTitle">{{ blockTitle }}</div>
    <div class="current-day" *ngIf="showCurrentDay">{{ getDayName() }}</div>
  </div>
  <ng-template #itemTemplate let-menu="data">
    <a [routerLink]="['/', 'recept', menu?.slug]" class="menu">
      <div class="thumbnail-wrapper">
        <i *ngIf="menu?.hasVideo" class="icon mindmegette-icon-play-without-circle"></i>
        <img loading="lazy" [alt]="menu?.thumbnail?.alt ?? ''" [src]="menu?.thumbnail?.url || 'assets/images/placeholder.jpg'" class="thumbnail" />
      </div>
      <h2 class="title" [attr.aria-label]="menu?.title">{{ menu?.title }}</h2>
    </a>
  </ng-template>
  <ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" /></ng-template>
  <ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" /></ng-template>
  <div
    kesma-swipe
    [itemTemplate]="itemTemplate"
    [previousNavigationTemplate]="previousNavigation"
    [nextNavigationTemplate]="nextNavigation"
    [useNavigation]="true"
    [usePagination]="true"
    [dataTrackByProperty]="'title'"
    [data]="data"
    [breakpoints]="{ default: { itemCount: 1, gap: '0px' } }"
  ></div>
</div>
