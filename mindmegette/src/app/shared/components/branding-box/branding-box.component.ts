import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { MindmegetteBrandingBoxArticle, MindmegetteBrandingBoxBrand, MindmegetteBrandingBoxTypes } from '../../definitions';
import { Observable } from 'rxjs';
import { Async<PERSON>ipe, NgF<PERSON>, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { TrafficDeflectorService } from '../../services';

const BREAKPOINT_WIDTH = 6;
const ARTICLES_PER_BOX = 5;

@Component({
  selector: 'app-branding-box[brand]',
  templateUrl: 'branding-box.component.html',
  styleUrls: ['branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, NgFor, NgIf, AsyncPipe, SlicePipe],
})
export class BrandingBoxComponent implements OnInit {
  @HostBinding('class') hostClass?: string;

  @Input() set brand(brand: MindmegetteBrandingBoxBrand) {
    this._brand = brand;
    switch (brand) {
      case MindmegetteBrandingBoxTypes.DIETA_ES_FITNESZ:
        this.link = 'https://dietaesfitnesz.hu/';
        this.ariaLabel = 'Diéta és Fitnesz weboldal';
        this._traffickingPlatform = 'Diéta és Fitnesz for MME';
        break;
      case MindmegetteBrandingBoxTypes.MNO_REPETA:
        this.link = 'https://magyarnemzet.hu/rovat/repeta';
        this.ariaLabel = 'Magyar Nemzet – Repeta rovat';
        this._traffickingPlatform = 'MNO for MME';
        break;
      case MindmegetteBrandingBoxTypes.VIDEK_IZE:
        this.link = 'https://videkize.hu/';
        this.ariaLabel = 'Vidék Íze weboldal';
        this._traffickingPlatform = 'Vidékíze for MME';
        break;
    }
  }

  @Input() set desktopWidth(desktopWidth: number) {
    this.hostClass = desktopWidth < BREAKPOINT_WIDTH ? 'small' : '';
  }

  link?: string;
  ariaLabel?: string;
  data$: Observable<MindmegetteBrandingBoxArticle[] | undefined>;

  private _traffickingPlatform: string;
  private _brand: MindmegetteBrandingBoxBrand;

  get traffickingPlatform(): string {
    return this._traffickingPlatform;
  }

  get brand(): MindmegetteBrandingBoxBrand {
    return this._brand as MindmegetteBrandingBoxBrand;
  }

  trafficDeflectorService = inject(TrafficDeflectorService);

  ngOnInit(): void {
    this.data$ = this.trafficDeflectorService.getTrafficDeflectorData(this.traffickingPlatform, ARTICLES_PER_BOX);
  }
}
