@if (data$ | async; as brandingBoxArticles) {
  <a [href]="link" target="_blank">
    <img class="branding-box-logo" src="/assets/images/{{ brand }}.svg" [attr.aria-label]="ariaLabel" loading="lazy" alt="" />
  </a>
  @if (brandingBoxArticles?.length) {
    <div class="branding-box">
      <div class="branding-box-column">
        <ng-container
          *ngTemplateOutlet="
            brandingBoxArticleTemplate;
            context: {
              article: brandingBoxArticles?.[0],
              style: 'bigThumbnail',
              showLead: true,
            }
          "
        >
        </ng-container>
      </div>
      <div class="branding-box-column">
        <ng-container *ngFor="let article of brandingBoxArticles | slice: 1">
          <ng-container
            *ngTemplateOutlet="
              brandingBoxArticleTemplate;
              context: {
                article,
                style: 'smallThumbnail',
              }
            "
          >
          </ng-container>
        </ng-container>
      </div>
    </div>
  }

  <ng-template #brandingBoxArticleTemplate let-article="article" let-showLead="showLead" let-style="style">
    <a class="branding-box-link {{ style }}" [href]="article?.url" target="_blank">
      <img class="branding-box-thumbnail" *ngIf="article?.imageUrl" [src]="article.imageUrl" loading="lazy" alt="" />
      <h2 class="branding-box-title">{{ article?.title }}</h2>
      <div class="branding-box-lead" *ngIf="showLead">{{ article?.lead }}</div>
    </a>
  </ng-template>
}
