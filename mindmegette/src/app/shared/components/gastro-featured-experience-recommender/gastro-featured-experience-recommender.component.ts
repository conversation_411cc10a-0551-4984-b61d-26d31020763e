import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent, IconComponent, KesmaSwipeComponent, truncateToUtc } from '@trendency/kesma-ui';
import { type FeaturedExperience, FeaturedExperienceType, GastroExperienceOccasionCard } from '../../definitions';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';
import { GastroExperienceOccasionCardComponent } from '../gastro-experience-occasion-card/gastro-experience-occasion-card.component';

@Component({
  selector: 'mindmegette-gastro-featured-experience-recommender',
  imports: [RouterLink, GastroExperienceOccasionCardComponent, NgIf, IconComponent, GastroExperienceOccasionCardComponent, KesmaSwipeComponent],
  templateUrl: './gastro-featured-experience-recommender.component.html',
  styleUrl: './gastro-featured-experience-recommender.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GastroFeaturedExperienceRecommenderComponent extends BaseComponent<FeaturedExperience> {
  readonly FeaturedExperienceType = FeaturedExperienceType;

  @Input() styleId = FeaturedExperienceType.Default;
  @Input() desktopWidth = 12;

  @HostBinding('class') get hostClass(): string {
    return `style-${this.styleId} ${this.desktopWidth < 6 ? 'mobile' : ''}`;
  }

  get isExternalUrl(): boolean {
    return !!(this.data?.button?.link?.startsWith('http://') || this.data?.button?.link?.startsWith('https://'));
  }

  ocationData(selectedOccasions: GastroExperienceOccasionCard): GastroExperienceOccasionCard {
    return {
      ...selectedOccasions,
      date: truncateToUtc(selectedOccasions.date as Date) ?? selectedOccasions.date,
    };
  }
}
