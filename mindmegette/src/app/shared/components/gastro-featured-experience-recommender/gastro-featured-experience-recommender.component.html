<div class="left">
  <h2 class="title">{{ styleId === FeaturedExperienceType.Default ? 'Élményt ajándé<PERSON>ba!' : 'Bemutatkozik a Mindmegette Élmények' }}</h2>
  <p class="description">{{ data?.description }}</p>
  @if (isExternalUrl) {
    <a class="button" *ngIf="data?.button?.label" [href]="data?.button?.link" target="_blank">
      {{ data?.button.label }}
      <kesma-icon name="icon-right-arrow" class="icon" [size]="20" />
    </a>
  } @else {
    <button class="button" *ngIf="data?.button?.label" [routerLink]="data?.button?.link || ['/']">
      {{ data?.button.label }}
      <kesma-icon name="icon-right-arrow" class="icon" [size]="20" />
    </button>
  }
</div>
<div class="right">
  <img class="image" [src]="data?.featuredImageUrl" loading="lazy" alt="Háttér kép" *ngIf="data?.featuredImageUrl" />
  <div class="experiences">
    <ng-template #itemTemplate let-occasion="data">
      <mindmegette-gastro-experience-occasion-card class="experiences-card" [data]="ocationData(occasion)" />
    </ng-template>
    <div
      kesma-swipe
      [itemTemplate]="itemTemplate"
      [data]="data?.selectedOccasions"
      dataTrackByProperty="slug"
      [breakpoints]="{
        default: {
          itemCount: 1,
        },
      }"
    ></div>
  </div>
</div>
