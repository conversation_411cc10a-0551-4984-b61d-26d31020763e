<ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" aria-label="Előző" /></ng-template>
<ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" aria-label="Következő" /></ng-template>
<ng-template #itemTemplate let-item="data">
  <a [routerLink]="elementLink(item)" class="element">
    <div class="title">{{ item?.title }}</div>
  </a>
</ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [data]="data"
  [useNavigation]="true"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [breakpoints]="breakpoints"
  [class.centered]="data?.length! < 6!"
></div>
