import {
  Article,
  ArticleBody,
  ArticleBodyType,
  ArticleCard,
  ArticleSocial,
  backendDateToDate,
  BackendDossierArticle,
  BackendRecommendedArticle,
  backendVotingDataToVotingData,
  DossierArticle,
  ExternalRecommendation,
  getPrimaryColumnColorComboByColumnTitleColor,
  MindmegetteBrandingBoxArticle,
  PersonalizedRecommendationArticle,
  PreviewBackendArticle,
  toBool,
} from '@trendency/kesma-ui';
import { BackendArticleSocial } from '../definitions';

export const backendArticlesToArticles = (article: any): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  let publishDate: Date | undefined;
  let year: number = 0;
  let month: number = 0;
  if (pubDate) {
    publishDate = backendDateToDate(typeof pubDate === 'string' ? pubDate : pubDate.date) ?? undefined;
    year = publishDate?.getUTCFullYear() ?? 0;
    month = publishDate?.getUTCMonth() ?? 0;
  }
  const last: Date | undefined = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;
  const body: ArticleBody[] = article.body.map((element: ArticleBody) => {
    if (element.type === ArticleBodyType.Voting) {
      const votingValue = backendVotingDataToVotingData(element.details[0]?.value);
      const detail = { ...element.details[0], value: votingValue };
      return { ...element, details: [detail] };
    }
    return element;
  });
  return {
    ...article,
    publicAuthor: article?.publicAuthor ?? 'Mindmegette',
    dossier: dossier?.[0],
    lastUpdated: last,
    publishDate,
    firstPublishDate: article?.firstPublishDate
      ? backendDateToDate(
          typeof article.firstPublishDate === 'string' ? article.firstPublishDate.replace('. ', ' ') : article.firstPublishDate.date.replace('. ', ' ')
        )
      : undefined,
    year,
    month,
    preTitle: article?.preTitle,
    tag: article?.tags?.[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article?.primaryColumn?.title,
    primaryColumnColorCombo: article?.primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(article?.primaryColumn?.titleColor) : undefined,
    body,
    readingTime: article.readingLength,
  };
};

export const backendDossierArticleToDossierArticle = (dossier: BackendDossierArticle): DossierArticle =>
  dossier && {
    ...dossier,
    publishDate: backendDateToDate(dossier.publishDate) as Date,
    length: dossier.length ? parseInt(dossier.length as string, 10) : undefined,
    thumbnailCreatedAt: dossier.thumbnailCreatedAt ? (backendDateToDate(dossier.thumbnailCreatedAt) ?? undefined) : undefined,
    regions: [],
    tags: [],
  };

export const externalRecommendationToArticleCard = (externalRecommendation: ExternalRecommendation): ArticleCard => ({
  id: externalRecommendation.spr,
  title: externalRecommendation.title,
  columnTitle: externalRecommendation.siteName,
  category: {
    name: externalRecommendation.siteName,
    slug: undefined,
  },
  thumbnail: externalRecommendation.imagePath
    ? {
        url: externalRecommendation.imagePath,
      }
    : undefined,
  slug: undefined,
  publishDate: undefined,
  label: {
    text: externalRecommendation.siteName,
    url: externalRecommendation.siteName ?? '',
  },
  publishYear: undefined,
  publishMonth: undefined,
  url: externalRecommendation.url,
});

export const backendRecommendedArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  thumbnailUrl,
  thumbnail,
  excerpt,
  readingLength,
  columnTitle,
  columnTitleColor,
  titleColor,
  columnSlug,
  preTitle,
  preTitleColor,
  thumbnailUrlFocusedImages,
}: BackendRecommendedArticle): ArticleCard => {
  const [publishYear, publishMonth] = publishDate.split('-');
  return {
    id,
    title,
    slug,
    publishDate: backendDateToDate(publishDate) as Date,
    publishMonth,
    publishYear,
    thumbnail:
      thumbnailUrl || thumbnail
        ? {
            url: thumbnailUrl || thumbnail || '',
          }
        : undefined,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    readingTime: readingLength?.toString(),
    columnTitle: columnTitle,
    preTitle: preTitle,
    primaryColumnColorCombo: columnTitleColor
      ? getPrimaryColumnColorComboByColumnTitleColor(columnTitleColor ? columnTitleColor : (titleColor ?? ''))
      : undefined,
    columnSlug,
    preTitleColor,
    lead: excerpt,
    label: {
      text: preTitle ?? '',
    },
    thumbnailFocusedImages: thumbnailUrlFocusedImages,
  };
};

export const previewBackendArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  primaryColumn,
  column,
  preTitle,
  excerpt,
  thumbnailUrl,
}: PreviewBackendArticle): ArticleCard => {
  if (!id) {
    id = '';
  }
  let publishDateFromBackend: Date | undefined;
  let publishYear = 0;
  let publishMonth = 0;
  if (publishDate) {
    publishDateFromBackend = backendDateToDate(typeof publishDate === 'string' ? publishDate : publishDate.date) ?? undefined;
    publishYear = publishDateFromBackend?.getUTCFullYear() ?? 0;
    publishMonth = (publishDateFromBackend?.getUTCMonth() ?? 0) + 1;
  }

  return {
    id,
    slug,
    title,
    preTitle,
    excerpt,
    publishDate: publishDateFromBackend,
    columnTitle: column?.title,
    columnSlug: column?.slug,
    thumbnail: thumbnailUrl
      ? {
          url: thumbnailUrl,
        }
      : undefined,
    primaryColumnColorCombo: primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(primaryColumn?.titleColor) : undefined,
    publishYear,
    publishMonth,
    category: { ...primaryColumn, name: primaryColumn?.title },
    label: {
      text: 'Ezt ne hagyd ki!',
    },
  };
};

export const backendArticleSocialToSocial = (backendArticleSocial: BackendArticleSocial): ArticleSocial => ({
  likeCount: backendArticleSocial.likeCount,
  dislikeCount: backendArticleSocial.dislikeCount,
  commentCount: backendArticleSocial.commentCount,
  isCommentsDisabled: backendArticleSocial.disableComments,
  isLikesAndDislikesDisabled: backendArticleSocial.disableLikesAndDislikes,
});

export const mapTrafficDeflectorArticlesToBrandingBoxArticle = (
  personalizedRecommendationArticle: PersonalizedRecommendationArticle
): MindmegetteBrandingBoxArticle => ({
  title: personalizedRecommendationArticle?.title,
  lead: personalizedRecommendationArticle?.head,
  thumbnail: personalizedRecommendationArticle?.image,
  imageUrl: personalizedRecommendationArticle?.image,
  url: personalizedRecommendationArticle?.url,
  isAdult: toBool(personalizedRecommendationArticle?.is_adult),
  hasGallery: false,
  hasVideo: toBool(personalizedRecommendationArticle?.is_video),
});
