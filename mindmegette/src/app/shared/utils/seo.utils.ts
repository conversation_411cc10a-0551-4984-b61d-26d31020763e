import { defaultMetaInfo } from '../constants';
import { ArticleBody, ArticleBodyType, ArticleSeoFields, RecipeCard } from '@trendency/kesma-ui';
import { ArticleSchema, ImageObjectSchema, IMetaData, SchemaOrg, SchemaOrgWebpageDataTemplate } from '@trendency/kesma-core';

export function createMMETitle(title: string): string {
  if (!title) {
    return defaultMetaInfo.ogTitle;
  }
  return `${title} | ${defaultMetaInfo.ogSiteName}`;
}

export function createKeywords(seo: ArticleSeoFields): string {
  const seoMainKeyword = seo?.seoMainKeyword ?? '';
  const seoOtherKeywords = seo?.seoOtherKeywords ?? '';
  const seoLongTailKeywords = seo?.seoLongTailKeywords ?? '';

  return [
    ...(seoMainKeyword?.length > 0 ? [seoMainKeyword] : []),
    ...(seoOtherKeywords?.length > 0 ? [seoOtherKeywords] : []),
    ...(seoLongTailKeywords?.length > 0 ? [seoLongTailKeywords] : []),
  ].join(', ');
}

export function mapThumbnailAltForSEO(recipe: RecipeCard): RecipeCard {
  return {
    ...recipe,
    thumbnail: {
      ...recipe.thumbnail,
      alt: recipe.title.endsWith('recept') ? recipe.title : `${recipe.title} recept`,
    },
  };
}

export function publicUserPageMetaInfo(user: string | undefined, page: number): IMetaData {
  const pageTitle = page > 1 ? page + `. oldal - ` : '';
  const defaultTitle = `${user} - Profil | ${defaultMetaInfo.ogSiteName}`;
  const title = pageTitle.concat(defaultTitle);
  const description = `${user} által készített receptek teljes listája a Mindmegette oldalán.
    Ismerd meg ${user} legnagyobb konyhai sikereit, kedvenc fogásait és praktikáit a bemutatkozó profilján`;
  return {
    ...defaultMetaInfo,
    title,
    description,
    ogTitle: title,
    ogDescription: description,
    twitterDescription: description,
  };
}

export function recipeCategoryMetaInfo(recipeCategory: string, page: number): IMetaData {
  const pageTitle = page > 1 ? page + '. oldal - ' : '';
  const defaultTitle = `${recipeCategory} | ${defaultMetaInfo.ogSiteName}`;
  const title = pageTitle.concat(defaultTitle);
  const description = `A legjobb ${recipeCategory} recepteket megtalálod a Mindmegette oldalán, ahol bevált,
      hazai és a szerkesztőség által válogatott receptek között ízlésed szerint válogathatsz.`;
  return {
    ...defaultMetaInfo,
    title,
    description,
    ogTitle: title,
    ogDescription: description,
    twitterDescription: description,
  };
}

export function tagsPageMetaInfo(tag: string, page: number): IMetaData {
  const pageTitle = page > 1 ? page + '. oldal - ' : '';
  const defaultTitle = `${tag} | ${defaultMetaInfo.ogSiteName}`;
  const description = `${tag} témában cikket keresel? Jó helyen jársz: tippek, szakértői tanácsok, bevált főzési trükkök,
      bevált sütési technikák összegyűjtve!`;
  const title = pageTitle.concat(defaultTitle);

  return {
    ...defaultMetaInfo,
    title,
    description,
    ogTitle: title,
    ogDescription: description,
    twitterDescription: description,
  };
}

export function authorPageMetaInfo(author: string, page: number, avatarUrl?: string): IMetaData {
  const pageTitle = page > 1 ? page + '. oldal - ' : '';
  const defaultTitle = `${author} | ${defaultMetaInfo.ogSiteName}`;
  const title = pageTitle.concat(defaultTitle);
  const description = `${author} legfrissebb receptjeit és praktikáit gyűjtjük össze nektek egy helyen.
      Szezonális vagy egész évben alkalmazható receptek és a legérdekesebb gasztro cikkek a Mindmegettén.`;
  return {
    ...defaultMetaInfo,
    title,
    description,
    ogTitle: title,
    ogDescription: description,
    twitterDescription: description,
    ...(avatarUrl ? { image: avatarUrl, ogImage: avatarUrl } : {}),
  };
}

export function selectionPageMetaInfo(page: number): IMetaData {
  const pageTitle = page > 1 ? page + '. oldal - ' : '';
  const defaultTitle = `Válogatások | ${defaultMetaInfo.ogSiteName}`;
  const title = pageTitle.concat(defaultTitle);
  const description = `Receptválogatások - A Mindmegette szerkesztősége által összeállított
   egyedi válogatások amelyek között bármilyen alkalomra megtalálod a legjobb recepteket!`;
  return {
    ...defaultMetaInfo,
    title,
    description,
    ogTitle: title,
    ogDescription: description,
    twitterDescription: description,
  };
}

export function extractVideoSchemasFromWysiwyg(
  wysiwyg?: ArticleBody[],
  additionalValues?: {
    name?: string;
    description?: string;
    uploadDate?: Date | null;
    dateCreated?: Date | null;
    thumbnailUrl?: string;
  }
): SchemaOrg[] {
  return (
    wysiwyg
      ?.filter((element) => element.type === ArticleBodyType.Wysywyg)
      ?.flatMap((element) => element.details)
      ?.flatMap(({ value }) => {
        const matches = [...value.matchAll(/<iframe[^>]+src="([^"]+)"/gi)];
        return matches.map((m) => m[1]);
      })
      ?.filter((embedUrl) => embedUrl !== undefined)
      ?.map(
        (embedUrl) =>
          ({
            '@type': 'VideoObject',
            name: `${additionalValues?.name} | Mindmegette.hu`,
            description: additionalValues?.description ?? '',
            embedUrl,
            uploadDate: additionalValues?.uploadDate?.toISOString() || new Date().toISOString(),
            dateCreated: additionalValues?.dateCreated?.toISOString() || new Date().toISOString(),
            thumbnailUrl: additionalValues?.thumbnailUrl ?? '',
          }) as SchemaOrg
      ) ?? []
  );
}

export function createWebPageSchema(
  additionalData: Partial<ArticleSchema> & {
    url: string;
    primaryImageOfPage?: ImageObjectSchema;
  }
): ArticleSchema {
  const portalSpecificData: Partial<ArticleSchema> = {
    name: 'Mindmegette',
  };

  return {
    ...SchemaOrgWebpageDataTemplate,
    ...portalSpecificData,
    ...additionalData,
  };
}
