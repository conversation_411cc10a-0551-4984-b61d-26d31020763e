import { ArticleCard, ArticleSearchResult, BackendArticleSearchResult } from '@trendency/kesma-ui';

export function backendArticlesSearchResultsToArticleSearchResArticles(article: BackendArticleSearchResult): ArticleSearchResult {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
    columnTitleColor: '',
  } as ArticleSearchResult;
}

export const searchResultToArticleCard = ({
  id,
  title,
  slug,
  columnTitle,
  columnSlug,
  publishDate,
  tag,
  lead,
  thumbnail: thumbnailUrl,
  author: authorName,
  authorSlug: authorSlug,
  year: publishYear,
  month: publishMonth,
  contentType,
  preTitle,
  tags,
  regions,
  isAdultsOnly,
  avatarImage,
  isGuaranteeType,
  thumbnailFocusedImages,
  isVideo,
  hasGallery,
}: ArticleSearchResult & { avatarImage?: string }): ArticleCard =>
  ({
    id,
    title,
    preTitle,
    slug,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    publishDate,
    publishYear,
    publishMonth,
    lead,
    thumbnail: {
      url: thumbnailUrl,
    },
    author: {
      name: authorName,
      avatarUrl: avatarImage,
      slug: authorSlug,
    },
    tags,
    regions,
    contentType,
    columnSlug,
    columnTitle,
    tag,
    isAdultsOnly,
    isGuaranteeType: isGuaranteeType === '1',
    isVideoType: isVideo === '1',
    thumbnailFocusedImages,
    hasGallery,
  }) as ArticleCard;

export function buildExtraParams(extraParams: Record<string, string | string[]>): Record<string, string | string[]> {
  const contentTypes = Array.isArray(extraParams['content_types[]']) ? extraParams['content_types[]'] : [extraParams['content_types[]']];
  if (contentTypes.includes('article')) {
    extraParams['content_types[]'] = ['articleVideo', 'articleGallery', 'articlePodcast', ...contentTypes];
  }
  return extraParams;
}
