import { SchemaOrg } from '@trendency/kesma-core';
import { ArticleBody, ArticleBodyType, backendDateToDate, buildRecipeUrl, RecipeCard, removeHTMLTagsFromString } from '@trendency/kesma-ui';
import { defaultMetaInfo, recipeCuisines } from '../constants';
import { Recipe, RecipeCategory } from '../../feature/recipe-page/recipe-page.definitions';

export const getStructuredDataForRecipe = (recipe: Recipe): SchemaOrg => {
  const seoKeywords: string[] = [
    ...convertSeoKeywordsToSchemaKeywords(recipe?.seo?.seoMainKeyword as string),
    ...convertSeoKeywordsToSchemaKeywords(recipe?.seo?.seoOtherKeywords as string),
    ...convertSeoKeywordsToSchemaKeywords(recipe?.seo?.seoLongTailKeywords as string),
  ];

  const description: string =
    recipe?.seo?.seoDescription ?? `Készítsd el a ${recipe?.title} kipróbált, bevált receptjét. A Mindmegette.hu receptgyűjteményében mindent megtalálsz.`;

  const publishDateStr: string | undefined = recipe?.firstPublishDate ?? recipe?.publishDate;
  let recipeDate: Date | undefined;

  if (publishDateStr) {
    recipeDate = new Date(`${publishDateStr.replace('. ', 'T').replace(/\./g, '-')}:00Z`);
  }

  return {
    '@type': 'Recipe',
    author: { '@type': 'Person', name: recipe?.publicAuthor?.fullName ?? recipe?.sentByUser?.fullName ?? '' },
    prepTime: recipe?.prepareTime ? convertMinuteToSchemaTime(recipe?.prepareTime) : '',
    cookTime: recipe?.cookTime ? convertMinuteToSchemaTime(recipe?.cookTime) : '',
    totalTime: recipe?.totalTime ? convertMinuteToSchemaTime(recipe?.totalTime) : '',
    description,
    datePublished: recipeDate ? recipeDate.toISOString() : undefined,
    image: recipe?.coverImage?.thumbnailUrl ?? '',
    ...(recipe?.makingText?.[0]?.details?.[0]?.value && {
      recipeInstructions: prepareRecipeMakingToSchemaRecipeInstructions(recipe?.makingText?.[0]?.details?.[0]?.value),
    }),
    recipeIngredient: [
      // eslint-disable-next-line no-unsafe-optional-chaining
      ...recipe?.recipeIngredients?.map(({ unit, quantity, ingredient }) => `${quantity ? quantity : ''} ${unit ? unit : ''} ${ingredient?.title}`.trim()),
    ],
    nutrition: {
      '@type': 'NutritionInformation',
      calories: recipe?.energy ? recipe?.energy + ' kcal' : undefined,
      proteinContent: recipe?.protein ? recipe?.protein + ' g' : undefined,
      fatContent: recipe?.fat ? recipe?.fat + ' g' : undefined,
      carbohydrateContent: recipe?.carbohydrate ? recipe?.carbohydrate + ' g' : undefined,
    },
    name: recipe?.title ? `${recipe.title} | ${defaultMetaInfo.ogSiteName}` : '',
    recipeCategory: recipe?.categories?.[0]?.title ?? '',
    recipeCuisine: getRecipeCuisine(recipe?.categories),
    ...(recipe?.madeForPeople && { recipeYield: recipe.madeForPeople }),
    keywords:
      seoKeywords.length > 0
        ? seoKeywords
        : (recipe?.tags ?? [])?.length > 0
          ? recipe?.tags?.[0].title
          : convertRecipeCategoriesToSchemaKeywords(recipe?.categories ?? []),
    ...((recipe?.rateCount || 0) > 0 && recipe?.allowedRating
      ? {
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: Number(recipe?.rateAverage || 0).toFixed(1),
            bestRating: recipe?.highestRate,
            ratingCount: recipe?.rateCount,
          },
        }
      : {}),
    ...convertVideoToSchemaVideo(recipe?.makingText as ArticleBody[], description),
  } as unknown as SchemaOrg;
};

const prepareRecipeMakingToSchemaRecipeInstructions = (makingText: string): Record<string, string>[] | string => {
  if (!makingText.includes('<ol>') || !makingText.includes('</ol>')) {
    return removeHTMLTagsFromString(makingText);
  }
  const listItems = makingText.split('<ol>')?.[1]?.split('</ol')?.[0];
  const listItem = listItems?.split('<li>')?.slice(1);
  if (!listItem?.length) {
    return removeHTMLTagsFromString(makingText);
  }
  return [...listItem.map((item, index) => ({ '@type': 'HowToStep', text: removeHTMLTagsFromString(item), name: `${index + 1}. lépés` }))];
};

const convertVideoToSchemaVideo = (recipeBody: ArticleBody[], alternativeDescription: string): Record<string, object> => {
  const video = recipeBody?.find(({ type }) => type === ArticleBodyType.MediaVideo)?.details?.[0]?.value;
  const urlAsArray = video?.videaUrl?.split?.('/');
  // For example: https://www.youtube.com/watch?v=OUo67NcbR-c -> We need this: OUo67NcbR-c
  // Result: https://img.youtube.com/vi/OUo67NcbR-c/maxresdefault.jpg
  const videoEmbedId = urlAsArray?.[urlAsArray?.length - 1]?.split('=')?.[1];
  return {
    ...(video
      ? {
          video: {
            '@type': 'VideoObject',
            name: video?.title,
            thumbnailUrl: videoEmbedId ? `https://img.youtube.com/vi/${videoEmbedId}/maxresdefault.jpg` : (video?.thumbnailUrl ?? ''),
            embedUrl: video?.videaUrl,
            dateCreated: backendDateToDate(video?.createdAt)?.toISOString(),
            uploadDate: backendDateToDate(video?.createdAt)?.toISOString(),
            about: video?.lead ?? '',
            //keywords: '', // Nincs ilyen mezőnk
            description: video?.description ?? alternativeDescription ?? '',
          },
        }
      : {}),
  };
};

const getRecipeCuisine = (categories: RecipeCategory[]): string | string[] => {
  const titles = categories?.filter(({ slug }) => recipeCuisines.includes(slug))?.map(({ title }) => title);
  return titles?.length ? (titles?.length === 1 ? titles?.[0] : titles) : 'magyar';
};

const convertMinuteToSchemaTime = (time: number): string => {
  const hours = Math.floor(time / 60);
  const minutes = time % 60;

  return `PT${hours ? `${hours}H` : ''}${minutes ? `${minutes}M` : ''}`;
};

const convertSeoKeywordsToSchemaKeywords = (keywords: string): string[] => {
  // eslint-disable-next-line no-unsafe-optional-chaining
  return keywords ? [...keywords?.split(',')?.map((keyword) => keyword?.trim())] : [];
};

const convertRecipeCategoriesToSchemaKeywords = (categories: RecipeCategory[]): string[] => {
  return categories?.map(({ title }) => title) ?? [];
};

interface RecipeListSchema extends SchemaOrg {
  itemListElement: {
    '@type': string;
    position: number;
    url: string[] | string;
    image?: string;
  }[];
}

export const getStructuredDataForRecipeList = (recipes: RecipeCard[], siteUrl: string): RecipeListSchema => ({
  '@type': 'ItemList',
  itemListElement: recipes.map((recipe, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    url: siteUrl + buildRecipeUrl(recipe).join('/').slice(1),
    image: recipe?.thumbnail?.url || 'https://www.mindmegette.hu/assets/images/placeholder.jpg',
  })),
});
