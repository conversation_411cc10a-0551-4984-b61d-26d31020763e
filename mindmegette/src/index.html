<!doctype html>
<html lang="hu">
  <head>
    <base href="/" />
    <title><PERSON><PERSON><PERSON> magyar receptgyűjtemény és gasztro portál | Mindmegette.hu</title>
    <meta charset="utf-8" />
    <meta name="robots" content="index, follow, max-image-preview:large" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <link rel="icon" type="image/x-icon" sizes="32x32" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/svg" href="/favicon.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="mask-icon" href="/favicon-safari-pinned-tab.svg" color="#00D96B" />
    <meta name="msapplication-TileColor" content="#00D96B" />
    <meta name="theme-color" content="#00D96B" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link rel="alternate" type="application/rss+xml" href="https://www.mindmegette.hu/publicapi/hu/rss/mindmegette/articles" />
    <link rel="alternate" type="application/rss+xml" href="https://www.mindmegette.hu/publicapi/hu/rss/mindmegette/recipes" />

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js',
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-N5752H');
    </script>
    <!-- End Google Tag Manager -->

    <script class="structured-data" type="application/ld+json"></script>

    <script src="/assets/scripts/inmobi.js"></script>
    <!-- InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
    <script type="text/javascript" async>
      // Make sure we call gemius init only after InMobi is loaded and inited
      console.log(' >> initializing InMobi ready callback for AdOcean');
      const inMobiReadyCallback = () => {
        console.log(' >> InMobi ready');

        if (!initAdOcean) {
          console.warn(' >> << no adocean init found');
        }
        !adOceanInited && initAdOcean && initAdOcean();
      };

      InMobiHandler.init('gq2uc_c-uMyQL', 'www.mindmegette.hu', inMobiReadyCallback);
    </script>
    <!-- End InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->

    <script>
      window.adocf = {
        useDOMContentLoaded: true,
      };

      window.PRE_adocean_queue = [];
      window.adOceanInited = false;

      window.adoQueueFn = (fn, args) => {
        console.log(' <> queuing ado call: %s', fn, args);
        PRE_adocean_queue.push({ fn, args });
      };
    </script>

    <!-- AD OCEAN GEMIUS -->
    <script type="text/javascript" src="https://hu.adocean.pl/files/js/ado.js"></script>

    <script async type="text/javascript">
      /* (c)AdOcean 2003-2020 */

      window.initAdOcean = () => {
        console.log(' >> init AdOcean');
        if (typeof window.ado !== 'object') {
          window.ado = {};
          window.ado.config = window.ado.preview = window.ado.placement = window.ado.master = window.ado.slave = function () {};
        }

        window.ado.config({
          mode: 'new',
          xml: false,
          consent: true,
          characterEncoding: true,
          attachReferrer: true,
          fpc: 'auto',
          defaultServer: 'hu.adocean.pl',
          cookieDomain: 'SLD',
        });

        window.ado.preview({ enabled: true });

        window.adOceanInited = true;
        window.PRE_adocean_queue.forEach(({ fn, args }) => {
          console.log(' >>> replaying adOcean queue: %s:', fn, args);
          window.ado[fn](...args);
        });
      };

      window.setTimeout(() => {
        if (!window.adOceanInited && window.Ado) {
          console.warn(' >> GFC initialization timeout: activating AdOcean');
          window.initAdOcean();
        } else if (!window.Ado) {
          console.error(' <!> GFC initialization timeout: AdOcean is not loaded! Aborting');
        }
      }, 30000);
    </script>

    <!-- AD OCEAN GEMIUS -->
  </head>

  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        title="Google Tag Manager"
        src="https://www.googletagmanager.com/ns.html?id=GTM-N5752H"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->

    <app-root></app-root>
    <script async defer src="/assets/scripts/version.js"></script>
  </body>
</html>
