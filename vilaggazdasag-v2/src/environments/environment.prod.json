{"production": true, "type": "prod", "apiUrl": {"clientApiUrl": "https://api.vg.hu/publicapi/hu", "serverApiUrl": "http://vgfeapi.app.content.private/publicapi/hu"}, "financialApiUrl": {"clientApiUrl": "https://fd.mediaworks.hu", "serverApiUrl": "http://findata-restapi.app.content.private"}, "iriszApiUrl": {"clientApiUrl": "https://irisz.origo.hu", "serverApiUrl": "http://irisz.app.origo.private"}, "ingatlanbazarApiUrl": {"clientApiUrl": "https://www.ingatlanbazar.hu/api", "serverApiUrl": "http://localhost:30005/ingatlanbazar-api"}, "zoeApiUrl": {"clientApiUrl": "https://zoe.mediaworks.hu", "serverApiUrl": "http://localhost:30005/zoe-api"}, "personalizedRecommendationApiUrl": "https://terelo.mediaworks.hu/api", "facebookAppId": "1534911313439123", "siteUrl": "https://www.vg.hu", "googleSiteKey": "6LeEZMUpAAAAALY1aDgrRgcmGEf1-GozC7RwnkYx", "googleTagManager": "GTM-P5LH6KG", "gemiusId": "d12aCArh.v5nYC5zXw1smpby.tMsbkbs2anSKLr9tJz.57", "httpReqTimeout": 30, "sentry": {"dsn": "https://<EMAIL>/4", "tracingOrigins": ["https://www.vg.hu"], "sampleRate": 0.1}}