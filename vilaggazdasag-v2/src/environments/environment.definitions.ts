import { EnvironmentDedicatedApiUrlSchema, EnvironmentSchema } from '@trendency/kesma-core';
import { z } from 'zod';
import { inject, InjectionToken } from '@angular/core';

export const VgEnvironmentSchema = EnvironmentSchema.extend({
  financialApiUrl: z.union([EnvironmentDedicatedApiUrlSchema, z.string()]),
  iriszApiUrl: z.union([EnvironmentDedicatedApiUrlSchema, z.string()]),
  ingatlanbazarApiUrl: z.union([EnvironmentDedicatedApiUrlSchema, z.string()]),
  zoeApiUrl: z.union([EnvironmentDedicatedApiUrlSchema, z.string()]),
});
export type VgEnvironment = z.infer<typeof VgEnvironmentSchema>;

export const VG_ENVIRONMENT: InjectionToken<VgEnvironment> = new InjectionToken('VG_ENVIRONMENT');

export const injectVgEnvironment = (): VgEnvironment => inject(VG_ENVIRONMENT);
