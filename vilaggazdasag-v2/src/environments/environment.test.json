{"production": true, "type": "beta", "apiUrl": "http://vgfe.apptest.content.private/publicapi/hu", "financialApiUrl": "http://findata.apptest.content.private/restapi", "iriszApiUrl": {"clientApiUrl": "https://irisz.origo.hu", "serverApiUrl": "http://irisz.apptest.origo.private"}, "ingatlanbazarApiUrl": {"clientApiUrl": "https://www.ingatlanbazar.hu/api", "serverApiUrl": "http://localhost:30005/ingatlanbazar-api"}, "zoeApiUrl": {"clientApiUrl": "https://zoe.mediaworks.hu", "serverApiUrl": "http://localhost:30005/zoe-api"}, "personalizedRecommendationApiUrl": "https://terelo.mediaworks.hu/api", "facebookAppId": "1534911313439123", "siteUrl": "http://vgfe.apptest.content.private", "googleSiteKey": "6LeEZMUpAAAAALY1aDgrRgcmGEf1-GozC7RwnkYx", "googleTagManager": "GTM-P5LH6KG", "gemiusId": "d12aCArh.v5nYC5zXw1smpby.tMsbkbs2anSKLr9tJz.57", "httpReqTimeout": 30, "sentry": {"dsn": "", "tracingOrigins": [], "sampleRate": 0.1}}