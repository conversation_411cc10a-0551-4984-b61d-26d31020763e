import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';
import {
  AnalyticsService,
  Article,
  ArticleBodyType,
  ArticleCard,
  ExchangeBoxType,
  GalleryData,
  GalleryElementData,
  previewBackendArticleToArticleCard,
  TenArticleRecommenderData,
  VoteData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { forkJoin, Subject, takeUntil } from 'rxjs';
import { GalleryService } from '../../services';
import { Router } from '@angular/router';
import { REQUEST, SeoService, UtilService } from '@trendency/kesma-core';
import { isMobileApp } from '../../utils';
import { SliderGalleryFullscreenLayerClickedEvent, VgDossierRecommendedData } from '../../definitions';
import { injectVgEnvironment } from '../../../../environments/environment.definitions';

@Component({
  selector: 'app-base-article-page',
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseArticlePageComponent {
  protected readonly environment = injectVgEnvironment();
  galleries: Record<string, GalleryData> = {};
  isMobileApp: boolean = false;
  readonly ArticleBodyType = ArticleBodyType;
  readonly ExchangeBoxType = ExchangeBoxType;
  protected readonly destroy$: Subject<boolean> = new Subject();

  constructor(
    protected readonly voteService: VoteService,
    protected readonly cdr: ChangeDetectorRef,
    protected readonly galleryService: GalleryService,
    protected readonly router: Router,
    protected readonly utils: UtilService,
    protected readonly seo: SeoService,
    protected readonly analyticsService: AnalyticsService,
    @Optional() @Inject(REQUEST) protected readonly request: Request
  ) {
    if (this.utils.isBrowser()) {
      this.isMobileApp = isMobileApp(navigator.userAgent || navigator.vendor || (window as any).opera);
    } else {
      let userAgent: string | undefined = '';

      if (this.request?.headers) {
        userAgent = Object.entries(this.request.headers).find((value) => value?.[0] === 'user-agent')?.[1];
      }

      // SSR: use request headers
      this.isMobileApp = isMobileApp(userAgent || '');
    }
  }

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  public doubleArticleRecommendations(arr: any[]): any[] {
    return arr.filter((elem: any) => elem?.value?.id);
  }

  public doubleArticleLead(arr: any[]): any {
    return arr.find((elem: any) => elem?.key === 'lead');
  }

  public doubleArticleTitle(arr: any[]): any {
    return arr.find((elem: any) => elem?.key === 'title');
  }

  public twoArticle(arr: any[]): ArticleCard[] {
    return arr
      .map((elem: any) => elem?.value)
      .map(
        (article: any): ArticleCard => ({
          ...article,
          thumbnailFocusedImages: article?.thumbnailUrlFocusedImages,
          isAdultsOnly: article?.status?.isAdultsOnly,
          isVideoType: article?.dbcache?.isVideoType,
          isPodcastType: article?.dbcache?.isPodcastType,
          hasGallery: article?.dbcache?.hasGallery,
        })
      );
  }

  public dossierRecommender(articleBodyDetails: any): VgDossierRecommendedData {
    return {
      ...articleBodyDetails,
      lead: articleBodyDetails?.description,
      headerImage: articleBodyDetails?.coverImage,
      articles: articleBodyDetails?.relatedArticles.map((article: any) => ({
        ...article,
        isAdultsOnly: article?.isAdultType,
      })),
    };
  }

  public doubleArticleRecommendation(articleBodyDetails: any): ArticleCard {
    if (articleBodyDetails.thumbnailUrl) {
      articleBodyDetails.thumbnail = { url: articleBodyDetails?.thumbnailUrl };
    }
    return {
      ...articleBodyDetails,
      thumbnailFocusedImages: articleBodyDetails?.thumbnailUrlFocusedImages,
      isAdultsOnly: articleBodyDetails?.status?.isAdultsOnly,
      isVideoType: articleBodyDetails?.dbcache?.isVideoType,
      isPodcastType: articleBodyDetails?.dbcache?.isPodcastType,
      hasGallery: articleBodyDetails?.dbcache?.hasGallery,
      likeCount: articleBodyDetails?.dbcache?.likeCount ?? 0,
      dislikeCount: articleBodyDetails?.dbcache?.dislikeCount ?? 0,
      commentCount: articleBodyDetails?.dbcache?.commentsCount ?? 0,
    };
  }

  getTenArticleRecommenderArticles(element: TenArticleRecommenderData): ArticleCard[] {
    const { details } = element;
    const result: ArticleCard[] = [];
    details?.forEach((detailItem) => {
      if ((detailItem.type === 'Detail.Reference.Article' || detailItem.type === 'Detail.Reference.ArticleOptional') && detailItem.value) {
        const card = previewBackendArticleToArticleCard(detailItem.value);
        result.push(card);
      }
    });
    return result;
  }

  public getVoteData(value: VoteData): VoteDataWithAnswer {
    return this.voteService.getVoteData(value);
  }

  public onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit($event, voteData).subscribe(() => this.cdr.detectChanges());
  }

  openGalleryDedicatedRouteLayer({ gallery, selectedImageIndex }: SliderGalleryFullscreenLayerClickedEvent): void {
    if (!gallery || !this.utils.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }

  trackByFn = (index: number): number => index;

  /**
   * When changing slides in the gallery we should send a pageView to Google Analytics and Gemius.
   * We need to explicitly send the href, title and referrers as these pageViews are just "virtual" views, because
   * they are not triggered by a real navigation in the browser.
   * @param gallery gallery that should receive the page views.
   * @param params parameters of the slide change event. For example the index of the image
   */
  handleGallerySlideChange(gallery: GalleryData, params: any): void {
    const { index } = params;
    const galleryUrl = [this.seo.hostUrl, 'galeria', gallery.slug, ...(index || index === 0 ? [index + 1] : [])].join('/');
    const pageViewParams = {
      href: galleryUrl,
      title: gallery.title,
      referrer: this.seo.currentUrl,
    } as any;
    this.analyticsService.sendPageView(pageViewParams, 'Galéria');
    pp_gemius_hit(this.environment.gemiusId, `page=${galleryUrl}`);
  }

  protected loadEmbeddedGalleries(article?: Article): void {
    const bodyElements = (article?.body as any) ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0]?.value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.destroy$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as any as GalleryData;
          this.galleries[gallery.id] = galleryData;
          this.cdr.markForCheck();
        });
      });
  }
}
