import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostBinding,
  Inject,
  inject,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormatDatePipe, IMetaData, PublishDatePipe, REQUEST, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AnalyticsService,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  ChartComponent,
  getSchemaLiveBlogPosting,
  getSchemaPodcastEpisode,
  getSchemaVideoObject,
  getStructuredDataForArticle,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  NativtereloComponent,
  previewBackendArticleToArticleCard,
  SponsoredTag,
  VoteService,
} from '@trendency/kesma-ui';

import { combineLatest, fromEvent, interval, map, merge, Observable, of, startWith, switchMap, takeUntil, tap } from 'rxjs';
import {
  ArticleCardType,
  ArticleHeaderComponent,
  ArticleRelatedContentComponent,
  BaseArticlePageComponent,
  defaultMetaInfo,
  ExchangeBoxComponent,
  ExternalRecommendationsComponent,
  FoundationRecommendationComponent,
  GalleryService,
  MinuteToMinuteService,
  NewsletterBoxType,
  PersonalizedRecommendationService,
  RecommendationBoxComponent,
  SocialShareComponent,
  VgArticleCardComponent,
  VgArticleSliderGalleryComponent,
  VgDossierRecommenderComponent,
  VgNewsletterBoxComponent,
  VgPodcastBoxComponent,
  VgVotingComponent,
  VgWysiwygBoxComponent,
} from '../../../../shared';
import { GlossaryProcessorService } from '../../../glossary/api/glossary-processor.service';
import { Glossary } from '../../../glossary/definitions/glossary.definitions';
import { AsyncPipe, DOCUMENT, NgClass, NgFor, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { VideoMoreArticlesComponent } from '../video-more-articles/video-more-articles.component';
import { OpinionArticlesComponent } from '../opinion-articles/opinion-articles.component';
import { AuthorMoreArticlesComponent } from '../author-more-articles/author-more-articles.component';
import { ArticleSponsoredHeaderComponent } from '../article-sponsored-header/article-sponsored-header.component';
import { ArticleInterviewHeaderComponent } from '../article-interview-header/article-interview-header.component';
import { ArticleVideoHeaderComponent } from '../article-video-header/article-video-header.component';
import { ArticlePodcastHeaderComponent } from '../article-podcast-header/article-podcast-header.component';
import { ArticleMinuteToMinuteHeaderComponent } from '../article-minute-to-minute-header/article-minute-to-minute-header.component';
import { ArticleOpinionHeaderComponent } from '../article-opinion-header/article-opinion-header.component';
import { AdultContentComponent } from '../adult-content/adult-content.component';
import { SponsoredTagBoxComponent } from '../../../../shared/components/sponsored-tag-box/sponsored-tag-box.component';
import { StockDetailArticleComponent, StockIndexDetailArticleComponent } from '../../../stock-exchange';
import { CurrencyDetailArticleComponent } from '../../../currency-exchange';

@Component({
  selector: 'app-article-page',
  templateUrl: './article-page.component.html',
  styleUrls: ['./article-page.component.scss', './article-page-shared.components.scss'],
  providers: [AutoArticleBodyAdService, FormatDatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AdultContentComponent,
    ArticleOpinionHeaderComponent,
    ArticleMinuteToMinuteHeaderComponent,
    ArticlePodcastHeaderComponent,
    ArticleVideoHeaderComponent,
    ArticleInterviewHeaderComponent,
    ArticleSponsoredHeaderComponent,
    NgClass,
    ArticleHeaderComponent,
    NgTemplateOutlet,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    SocialShareComponent,
    AuthorMoreArticlesComponent,
    OpinionArticlesComponent,
    VideoMoreArticlesComponent,
    NativtereloComponent,
    RecommendationBoxComponent,
    FoundationRecommendationComponent,
    SidebarComponent,
    ArticleRelatedContentComponent,
    ExchangeBoxComponent,
    AsyncPipe,
    VgArticleCardComponent,
    VgNewsletterBoxComponent,
    VgDossierRecommenderComponent,
    VgArticleSliderGalleryComponent,
    VgVotingComponent,
    VgWysiwygBoxComponent,
    VgPodcastBoxComponent,
    AdvertisementAdoceanComponent,
    FormatDatePipe,
    PublishDatePipe,
    ArticleFileLinkDirective,
    SponsoredTagBoxComponent,
    ChartComponent,
    ExternalRecommendationsComponent,
    StockDetailArticleComponent,
    StockIndexDetailArticleComponent,
    CurrencyDetailArticleComponent,
    ArticleVideoComponent,
  ],
})
export class ArticlePageComponent extends BaseArticlePageComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('dataTrigger', { static: false }) readonly dataTrigger: ElementRef<HTMLDivElement>;
  article?: Article;
  sponsoredTag?: SponsoredTag;
  authorArticles?: ArticleCard[];
  opinionArticles?: ArticleCard[];
  videoArticles?: ArticleCard[];
  podcastArticles?: ArticleCard[];
  articleSlug?: string;
  isUserAdultChoice = false;
  categorySlug = '';
  metaData?: IMetaData;
  cannonicalUrl?: string;
  adverts?: ArticleAdvertisements;
  mobile_pr_cikkfix?: Advertisement[];
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  glossary: Glossary[] = [];
  @HostBinding('class') adultOverlay = '';
  readonly ArticleCardType = ArticleCardType;
  readonly MinuteToMinuteState = MinuteToMinuteState;
  readonly NewsletterBoxType = NewsletterBoxType;
  readonly minuteToMinuteRealtime$: Observable<MinuteToMinuteBlock[]> = this.utils?.isBrowser()
    ? interval(30_000).pipe(switchMap(() => this.minuteToMinuteService.getBlocks(this.article)))
    : of([]);
  readonly routeData = this.route.data as Observable<{
    articlePageData: ArticleResolverData & {
      foundationTagSlug?: string;
      foundationTagTitle?: string;
      opinions?: ArticleCard[];
      videos?: ArticleCard[];
      podcasts?: ArticleCard[];
    };
  }>;
  readonly minuteToMinutes$: Observable<MinuteToMinuteBlock[]> = merge(
    this.routeData.pipe(map(({ articlePageData }) => this.minuteToMinuteService.toBlocks(articlePageData?.article?.data))),
    this.minuteToMinuteRealtime$
  );
  newestArticles$: Observable<ArticleCard[]> = this.route.data.pipe(map(({ articlePageData }) => articlePageData['newestArticles']));
  isMobile$ = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth),
        map((width: number) => width <= 768),
        takeUntil(this.destroy$)
      )
    : of(false);
  tereloUrl: string =
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=vg.hu' + '&traffickingPlatforms=Vil%C3%A1ggazdas%C3%A1g%20Nat%C3%ADv' + '&domain=VG';
  private readonly document = inject(DOCUMENT);
  isExceptionAdvertEnabled: boolean;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    protected readonly route: ActivatedRoute,
    protected override readonly router: Router,
    protected readonly adStore: AdvertisementAdoceanStoreService,
    protected readonly storage: StorageService,
    protected override readonly voteService: VoteService,
    private readonly formatDate: FormatDatePipe,
    protected override readonly analyticsService: AnalyticsService,
    protected override readonly cdr: ChangeDetectorRef,
    protected override readonly galleryService: GalleryService,
    protected override readonly seo: SeoService,
    protected readonly schemaService: SchemaOrgService,
    protected override readonly utils: UtilService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService,
    private readonly minuteToMinuteService: MinuteToMinuteService,
    private readonly glossaryService: GlossaryProcessorService,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    @Optional() @Inject(REQUEST) protected override readonly request: Request
  ) {
    super(voteService, cdr, galleryService, router, utils, seo, analyticsService, request);
  }

  get isMinuteByMinute(): boolean {
    return this.article?.minuteToMinute !== MinuteToMinuteState.NOT;
  }

  get showTypeHeader(): boolean {
    return !!(
      this.isMinuteByMinute ||
      this.article?.isPodcastType ||
      this.article?.isInterviewType ||
      this.article?.isVideoType ||
      this.article?.sponsorship ||
      this.article?.isOpinion
    );
  }

  ngOnInit(): void {
    this.routeData.pipe(takeUntil(this.destroy$))?.subscribe(
      ({
        articlePageData: {
          article: { data: article, articles, meta: meta },
          articleSlug,
          url,
          opinions,
          foundationTagSlug,
          foundationTagTitle,
          videos,
          podcasts,
        },
      }) => {
        if (!article) {
          return;
        }
        this.autoArticleBodyAd.init(article.body);
        const body = this.autoArticleBodyAd.autoAd();

        this.foundationTagSlug = foundationTagSlug;
        this.foundationTagTitle = foundationTagTitle;

        this.glossary = meta?.['glossary'] ?? [];
        this.glossaryService.init(this.glossary);

        this.article = {
          ...article,
          slug: articleSlug,
          body: this.#prepareArticleBody(this.glossaryService.linkGlossaryItems(body)),
          excerpt: article?.lead || article?.excerpt,
        };
        this.articleSlug = articleSlug;
        this.authorArticles = articles?.data || [];
        this.opinionArticles = opinions;
        this.videoArticles = videos;
        this.podcastArticles = podcasts;
        this.sponsoredTag = meta?.['sponsoredTag'];

        this.isUserAdultChoice = (this.storage.getSessionStorageData('isAdultChoice', false) ?? false) && this.article?.isAdultsOnly;
        this.adultOverlay = this.article?.isAdultsOnly && !this.isUserAdultChoice ? 'adult-overlay' : '';
        this.cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || (url ? `${this.seo.hostUrl}/${url}` : this.seo.hostUrl);
        this.adStore.setIsAdultPage(this.isUserAdultChoice);
        this.seo.updateCanonicalUrl(this.cannonicalUrl ?? '', { addHostUrl: false, skipSeoMetaCheck: true });
        this.loadEmbeddedGalleries(this.article);
        this.#setMetaData();

        if (this.article) {
          this.schemaService.removeStructuredData();
          this.schemaService.insertSchema(
            getStructuredDataForArticle(this.article, this.seo.currentUrl, this.environment?.siteUrl ?? '', { hasAuthorPageSlug: true })
          );

          // Set PodcastSeries schema
          if (this.article?.isPodcastType) {
            this.schemaService.insertSchema(getSchemaPodcastEpisode(this.article, this.seo.currentUrl));
          }

          // Set VideoObject schema
          if (this.article?.videoType) {
            this.schemaService.insertSchema(getSchemaVideoObject(this.article));
          }

          // Set LiveBlogPosting schema
          if (this.article?.minuteToMinute !== MinuteToMinuteState.NOT) {
            this.schemaService.insertSchema(getSchemaLiveBlogPosting(this.article, this.seo.currentUrl));
          }
        }

        const categorySlug = this.article?.primaryColumn?.slug;
        this.voteService.initArticleVotes(this.article);
        this.cdr.markForCheck();

        if (this.utils.isBrowser()) {
          setTimeout(() => {
            this.analyticsService.sendPageView(
              {
                pageCategory: categorySlug,
                customDim2: this.article?.topicLevel1,
                customDim1: this.article?.aniCode,
                title: this.article?.title,
                articleSource: this.article?.articleSource ? this.article.articleSource : 'no source',
                publishDate: this.formatDate.transform(this.article?.publishDate as Date, 'dateTime'),
                lastUpdatedDate: this.formatDate.transform(
                  (this.article?.lastUpdated ? this.article.lastUpdated : this.article?.publishDate) as Date,
                  'dateTime'
                ),
              },
              'Cikk'
            );
          }, 0);
        }
      }
    );

    (
      combineLatest([
        this.route.data as Observable<{
          articlePageData: ArticleResolverData;
        }>,
        this.adStore.isAdult.asObservable(),
      ]) as Observable<[{ articlePageData: ArticleResolverData }, boolean]>
    )
      .pipe(
        map<[{ articlePageData: ArticleResolverData }, boolean], boolean | undefined>(
          ([
            {
              articlePageData: { article },
            },
          ]) => {
            this.adStore.getAdvertisementMeta(article.data?.tags, article?.data?.isAdultsOnly);
            this.isExceptionAdvertEnabled = article?.data.isExceptionAdvertEnabled;

            return article?.data?.withoutAds;
          }
        ),
        tap(() => this.resetAds()),
        switchMap((withoutAds) => {
          withoutAds ? this.adStore.disableAds() : this.adStore.enableAds();
          return this.adStore.advertisemenets$;
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((adsCollection): void => {
        this.adverts = this.adStore.separateAdsByMedium(adsCollection);

        this.mobile_pr_cikkfix = [
          this.adverts?.mobile?.['prcikkfix_1'],
          this.adverts?.mobile?.['prcikkfix_2'],
          this.adverts?.mobile?.['prcikkfix_3'],
          this.adverts?.mobile?.['prcikkfix_4'],
          this.adverts?.mobile?.['prcikkfix_5'],
        ];

        this.adStore.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    if (this.utils.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }

    this.route.fragment.pipe(takeUntil(this.destroy$)).subscribe((fragment) => {
      // Scrolling is only possible in the browser.
      if (this.utils.isBrowser() && fragment) {
        const elem = this.document.getElementById(fragment);
        if (elem) {
          window.requestAnimationFrame(() => {
            elem.scrollIntoView({ block: 'start' });
          });
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.adStore.onArticleDestroy();
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.adultOverlay = '';
    this.adStore.setIsAdultPage(isUserAdult);
  }

  getMinuteByMinuteShareUrl(id: string): string {
    return `${this.seo.currentUrl.replace(/#.+$/, '')}#pp-${id}`;
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: previewBackendArticleToArticleCard(detail.value),
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }

  #setMetaData(): void {
    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail, secondaryThumbnail } = this.article || {};
    if (!this.article) {
      return;
    }

    const title = this.article.seo?.seoTitle || this.article.title;

    const comboTitle = `${title} - ${defaultMetaInfo?.ogSiteName}`;
    const finalOgTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : `${this.article.title} - ${defaultMetaInfo?.ogSiteName}`;
    const finalTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : comboTitle;
    this.metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: this.article.seo?.seoDescription || this.article.excerpt || this.article.lead || defaultMetaInfo.description,
      robots: this.article.slug === 'cikk-elonezet' ? 'noindex, nofollow' : this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
      ogTitle: finalOgTitle,
      ogImage: secondaryThumbnail || metaThumbnail || thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };

    this.seo.setMetaData(this.metaData, { skipSeoMetaCheck: true });
  }

  private observeArticleEnd(): void {
    if (!this.dataTrigger) {
      return;
    }
    if (!this.dataTrigger?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          if (this.dataTrigger) {
            observer.unobserve(this.dataTrigger.nativeElement);
          }
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    if (!this.article) {
      return;
    }
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: this.article.columnTitle ?? '',
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((this.article.lastUpdated ? this.article.lastUpdated : this.article.publishDate) as Date, 'dateTime'),
    });
  }
}
