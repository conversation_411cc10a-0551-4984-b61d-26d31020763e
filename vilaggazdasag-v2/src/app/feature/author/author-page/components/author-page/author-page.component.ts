import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
  getStructuredDataForProfilePage,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { AuthorData, IAuthorPage } from '../../../author.definitions';
import { capitalize } from 'lodash-es';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { ArticleCardType, defaultMetaInfo, NewsletterBoxType, VgArticleCardComponent, VgNewsletterBoxComponent, VgPagerComponent } from '../../../../../shared';
import { AuthorBoxComponent } from '../author-box/author-box.component';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { injectVgEnvironment } from '../../../../../../environments/environment.definitions';

@Component({
  selector: 'app-author-page',
  templateUrl: './author-page.component.html',
  styleUrls: ['./author-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    VgArticleCardComponent,
    AuthorBoxComponent,
    NgFor,
    AsyncPipe,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class AuthorPageComponent implements OnInit, OnDestroy {
  private readonly environment = injectVgEnvironment();
  readonly ArticleCardType = ArticleCardType;
  readonly NewsletterBoxType = NewsletterBoxType;
  adverts?: AdvertisementsByMedium;
  page = 0;
  numberOfArticles: number;
  author: AuthorData;

  readonly unsubscribe$: Subject<boolean> = new Subject();

  data$: Observable<IAuthorPage> = this.route.data.pipe(
    map(({ data }) => data),
    takeUntil(this.unsubscribe$)
  );
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(
    tap((res) => (this.numberOfArticles = res['data'].limitable.rowAllCount)),
    map((res) => res['data']?.limitable),
    takeUntil(this.unsubscribe$)
  );

  constructor(
    protected readonly route: ActivatedRoute,
    private readonly adStore: AdvertisementAdoceanStoreService,
    protected readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly schemaService: SchemaOrgService
  ) {}

  ngOnInit(): void {
    this.route.queryParams
      .pipe(
        tap(() => this.initAds()),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((params) => {
        let page = parseInt(params['page']);
        this.page = page ? --page : 0;
      });

    (this.route.data as Observable<{ data: IAuthorPage }>)
      .pipe(
        map(({ data }) => data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(({ author }) => {
        this.author = author as AuthorData;

        this.schemaService.removeStructuredData();
        this.schemaService.insertSchema(getStructuredDataForProfilePage(this.author as any, this.environment?.siteUrl ?? ''));

        this.setMetaData();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(): void {
    const authorName = capitalize(this.author.publicAuthorName ?? 'Ismeretlen');
    const title = `${authorName} szerző oldala | ${defaultMetaInfo.title}`;
    const description = `${authorName} szerző oldala - ${defaultMetaInfo.description}`;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage(`szerzo`, this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }
}
