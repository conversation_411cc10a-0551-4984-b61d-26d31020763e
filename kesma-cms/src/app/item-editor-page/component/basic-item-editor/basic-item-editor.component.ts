import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { Observable, of, Subject, throwError } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { Location } from '@angular/common';
import { PageEditorService } from 'src/app/content-page-editor/services/page-editor.service';
import { ApiService } from 'src/app/core/services/api.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { FormControlMapService } from 'src/app/shared/modules/form-generator/services/form-control-map.service';
import { ContentData, FormControlInteraction } from 'src/app/shared/modules/form-generator/form-generator.definitions';
import { FormGeneratorComponent } from 'src/app/shared/modules/form-generator/form-generator.component';
import { FormControlState, FormState } from 'src/app/shared/modules/form-generator/definitions/state.definitions';
import { RoleGroupsApiService } from 'src/app/modules/role-groups/services/role-groups-api.service';
import { ContributorsService } from '../../../modules/contributors/contributors.service';
import { PublicAuthorsService } from '../../../modules/public-authors/components/public-authors.service';
import { ErrorHandlerService } from '@shared/services/error-handler.service';
import { ShortUrlService } from '../../../modules/short-url/short-url.service';
import { MapsService } from 'src/app/modules/maps/api/maps.service';
import { GlossaryService } from 'src/app/core/services/api/glossary/glossary.service';
import { InteractionFactoryService } from '../../interactions/interaction-factory.service';

@Component({
  selector: 'app-basic-item-editor',
  templateUrl: 'basic-item-editor.component.html',
  styleUrls: ['./basic-item-editor.component.scss'],
  standalone: false,
})
export class BasicItemEditorComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});

  @Input() itemId: string;
  @Input() mode: 'list' | 'page' | 'modal';
  @Input() formInfo: ContentData;
  @Input() contentType: string;
  @Input() contentGroup: string;
  /**
   * Show title of the form
   */
  @Input() showTitle = true;
  /**
   * Show save and cancel buttons.
   */
  @Input() showActionButtons = true;
  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() basicItemCreated: EventEmitter<any> = new EventEmitter();
  @ViewChild('createItemTemplate') child: FormGeneratorComponent;

  private destroy: Subject<boolean> = new Subject<boolean>();
  public contentPageData: ContentData;
  public openedComponentIndexes: number[];
  public openedWidgetIndexes: number[];
  public saveLoading: boolean;
  public menuType: string;
  public title: string;
  public editLockDescription: string;
  public operationCMS: string;
  public cancelBtnTitle: string;
  public isFormDisabled: boolean;
  public isFormValid: boolean;
  public disableSave: boolean;

  public interactions: FormControlInteraction;

  constructor(
    public pageEditorService: PageEditorService,
    private route: ActivatedRoute,
    private apiService: ApiService,
    private roleGroupsApiService: RoleGroupsApiService,
    private contributorsService: ContributorsService,
    private publicAuthorsService: PublicAuthorsService,
    private sharedService: SharedService,
    private location: Location,
    private router: Router,
    private formControlMapService: FormControlMapService,
    private cd: ChangeDetectorRef,
    private errorHandler: ErrorHandlerService,
    private shortUrlService: ShortUrlService,
    private mapsService: MapsService,
    private readonly glossaryService: GlossaryService,
    private readonly interactionFactory: InteractionFactoryService
  ) {}

  ngOnInit() {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    this.interactions = this.interactionFactory.getInteractionsByContentGroup(this.contentGroup);
    this.disableSave = false;
    this.forminfoFilterer();
    this.isFormDisabled = this.formInfo.meta?.hasOwnProperty('canEdit') && !this.formInfo.meta?.canEdit;
    this.getTranslations();
    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe(() => {
      if (this.mode && this.mode === 'list') {
        this.child.resetForm(true);
        this.close.emit();
      }
    });
  }

  shouldPositionButtonsToTop(): boolean {
    return ['role-group', 'user-permissions'].includes(this.contentGroup);
  }

  forminfoFilterer() {
    this.formInfo.data = (this.formInfo?.data ?? []).filter(
      (item) => this.formControlMapService.getFormControlComponent(item.inputType, item.inputInfo) !== null
    );
  }

  getTranslations() {
    this.operationCMS = this.itemId ? 'CMS.update' : 'CMS.create';
    this.operationCMS = this.isFormDisabled ? 'CMS.show' : this.operationCMS;
    this.title = 'CMS.' + this.contentType;
    this.cancelBtnTitle = this.itemId ? 'CMS.back' : 'CMS.cancel';
  }

  ngAfterViewInit() {
    this.cd.detectChanges();
  }

  onFormStateChanged(formState: FormState) {}

  ngOnDestroy() {
    this.destroy.next(true);
    this.destroy.complete();
  }

  onCancel() {
    switch (this.mode) {
      case 'list':
        {
          this.child.resetForm(false);
          this.close.emit();
        }
        break;
      case 'page':
        {
          const hasLocation = +(this.location.getState() as { navigationId?: number })?.navigationId || 0;
          if (hasLocation > 1) {
            this.location.back();
            return;
          }
          const listType = this.route.snapshot.root.firstChild.firstChild.routeConfig.path;
          this.router.navigate(['/', 'admin', listType], {}).then();
        }
        break;
      case 'modal':
        {
          this.close.emit();
        }
        break;
    }
  }

  update(id: string, data: any) {
    return this.apiService.updateContentGroup(this.contentType, id, data);
  }

  create(data: any) {
    return this.apiService.createContentGroup(this.contentType, data);
  }

  onCreateOrUpdate() {
    let request: Observable<any>;
    switch (this.contentGroup) {
      case 'publicAuthors':
        {
          if (this.itemId) {
            request = this.publicAuthorsService.updatePublicAuthor(this.formInfo, this.itemId);
          } else {
            request = this.publicAuthorsService.createPublicAuthor(this.formInfo);
          }
        }
        break;
      case 'contributors':
        {
          if (this.itemId) {
            request = this.contributorsService.updateContributor(this.formInfo, this.itemId);
          } else {
            request = this.contributorsService.createContributor(this.formInfo);
          }
        }
        break;
      case 'custom-built-page':
        {
          if (this.itemId) {
            request = this.apiService.updateContentPage(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createContentPageFromBasiItem(this.contentType, this.formInfo);
          }
        }
        break;
      case 'games':
        {
          if (this.itemId) {
            request = this.apiService.updateContentGroup(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createGame(this.contentType, this.formInfo);
          }
        }
        break;
      case 'content-group':
        {
          if (this.itemId) {
            request = this.apiService.updateContentGroup(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createContentGroup(this.contentType, this.formInfo);
          }
        }
        break;
      case 'role-group':
        {
          if (this.itemId) {
            request = this.roleGroupsApiService.updateRoleGroup(this.itemId, this.formInfo);
          } else {
            request = this.roleGroupsApiService.createRoleGroup(this.formInfo);
          }
        }
        break;
      case 'media':
        {
          if (this.itemId) {
            request = this.apiService.updateMedia(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createMedia(this.contentType, this.formInfo);
          }
        }
        break;
      case 'robots-content':
        if (this.itemId) {
          request = this.apiService.seoMeta.robots.update(this.itemId, this.formInfo);
        } else {
          request = this.apiService.seoMeta.robots.create(this.formInfo);
        }
        break;
      case 'seo-meta-tool':
        if (this.itemId) {
          request = this.apiService.seoMeta.urls.update(this.itemId, this.formInfo);
        } else {
          request = this.apiService.seoMeta.urls.create(this.formInfo);
        }
        break;
      case 'menu':
        {
          if (this.itemId) {
            request = this.apiService.saveMenuItemData(this.contentType, this.formInfo, this.itemId);
          } else {
            request = this.apiService.createMenuItem(this.contentType, this.formInfo);
          }
        }
        break;
      case 'user':
        {
          if (this.itemId) {
            request = this.apiService.updateBasicItem(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createBasicItem(this.contentType, this.formInfo);
          }
        }
        break;
      case 'user-permissions':
        {
          request = this.apiService.updateCmsUserPermissions(this.itemId, this.formInfo);
        }
        break;
      case 'portal':
        {
          if (this.itemId) {
            request = this.apiService.updatePortal(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createPortal(this.contentType, this.formInfo);
          }
        }
        break;
      case 'program':
        {
          if (this.itemId) {
            request = this.apiService.updateProgram(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createProgram(this.contentType, this.formInfo);
          }
        }
        break;
      case 'multi-vote':
        if (this.itemId) {
          request = this.apiService.updateVoting(this.contentType, this.itemId, this.formInfo, true);
        } else {
          request = this.apiService.createVoting(this.contentType, this.formInfo, true);
        }
        break;
      case 'extraordinary-notification':
        if (this.itemId) {
          request = this.apiService.extraordinaryNotification.update(this.itemId, this.formInfo);
        } else {
          request = this.apiService.extraordinaryNotification.create(this.formInfo);
        }
        break;
      case 'voting':
        {
          if (this.itemId) {
            request = this.apiService.updateVoting(this.contentType, this.itemId, this.formInfo);
          } else {
            request = this.apiService.createVoting(this.contentType, this.formInfo);
          }
        }
        break;
      case 'marketplace-category':
        {
          if (this.itemId) {
            request = this.apiService.updateMarketplaceCategory(this.itemId, this.formInfo);
          } else {
            request = this.apiService.createMarketplaceCategory(this.formInfo);
          }
        }
        break;
      case 'marketplace-item':
        {
          if (this.itemId) {
            request = this.apiService.updateMarketplaceItem(this.itemId, this.formInfo);
          } else {
            request = this.apiService.createMarketplaceItem(this.formInfo);
          }
        }
        break;
      case 'setting':
        {
          if (this.itemId) {
            request = this.apiService.updateSetting(this.itemId, this.formInfo);
          } else {
            request = this.apiService.createSetting(this.formInfo);
          }
        }
        break;
      case 'config':
        {
          if (this.itemId) {
            request = this.apiService.updateConfig(this.contentType, this.itemId, this.formInfo);
          }
        }
        break;
      case 'quiz':
        {
          request = this.itemId
            ? this.apiService.updateContentGroup(this.contentType, this.itemId, this.formInfo)
            : this.apiService.createContentGroupSuffixed(this.contentType, this.formInfo);
        }
        break;
      case 'journal-issue':
        {
          request = this.itemId
            ? this.apiService.updateContentGroupSuffixed(this.contentType, this.itemId, this.formInfo)
            : this.apiService.createContentGroupSuffixed(this.contentType, this.formInfo);
        }
        break;
      case 'institution-category':
        {
          request = this.itemId
            ? this.apiService.updateInstitutionCategory(this.contentType, this.itemId, this.formInfo)
            : this.apiService.createInstitutionCategory(this.contentType, this.formInfo);
        }
        break;
      case 'article-network-slot':
        {
          request = this.itemId
            ? this.apiService.updateNetworkSlot(this.contentType, this.itemId, this.formInfo)
            : this.apiService.createNetworkSlot(this.contentType, this.formInfo);
        }
        break;
      case 'portal-users':
        {
          request = this.apiService.portalUser.updatePortalUserForm(this.formInfo, this.itemId);
        }
        break;
      case 'subscriptions-subscriptions':
        {
          request = this.apiService.portalUser.updateSubscriptionForm(this.formInfo, this.itemId);
        }
        break;
      case 'banned-word':
        {
          request = this.itemId ? this.apiService.updateBannedWord(this.itemId, this.formInfo) : this.apiService.createBannedWord(this.formInfo);
        }
        break;
      case 'product':
        {
          request = this.apiService.createSubscriptionProduct(this.formInfo, this.itemId);
        }
        break;
      case 'short_url':
        {
          request = this.shortUrlService.createUrl(this.formInfo, this.itemId);
        }
        break;
      case 'campaign':
        {
          request = this.apiService.createMarketingCampaign(this.formInfo, this.itemId);
        }
        break;
      case 'partners':
        {
          request = this.apiService.createPartner(this.formInfo, this.itemId);
        }
        break;
      case 'automails':
        {
          request = this.apiService.createAutomail(this.formInfo, this.itemId);
        }
        break;
      case 'stations':
        {
          request = this.apiService.createStation(this.formInfo, this.itemId);
        }
        break;
      case 'export-addressee':
        {
          request = this.apiService.createExportAddressee(this.formInfo, this.itemId);
        }
        break;
      case 'popup-notifier':
        {
          request = this.apiService.createPopupNotifier(this.formInfo, this.itemId);
        }
        break;
      case 'orvosmet':
        {
          request = this.apiService.createOrvosmet(this.formInfo, this.itemId);
        }
        break;
      case 'map':
        {
          const mapType = this.mapsService.mapType; // if object has id, that will be white event, else has name, that will be temperature map
          if (mapType?.mapId) {
            request = this.apiService.updateWhiteEventsChance(mapType?.mapId, this.formInfo);
          } else if (mapType?.mapName) {
            request = this.apiService.updateTemperatureMap(this.formInfo);
          } else {
            request = this.apiService.updateSnowdepth(this.formInfo);
          }
        }
        break;
      case 'facility':
        {
          request = this.itemId ? this.apiService.facility.update(this.itemId, this.formInfo) : this.apiService.facility.create(this.formInfo);
        }
        break;
      case 'liveSport':
        {
          request = this.itemId ? this.apiService.liveSport.update(this.itemId, this.formInfo) : this.apiService.liveSport.create(this.formInfo);
        }
        break;
      case 'scheduleEvent':
        {
          request = this.itemId ? this.apiService.scheduleEvent.update(this.itemId, this.formInfo) : this.apiService.scheduleEvent.create(this.formInfo);
        }
        break;
      case 'staff':
        {
          request = this.itemId ? this.apiService.staff.update(this.itemId, this.formInfo) : this.apiService.staff.create(this.formInfo);
        }
        break;
      case 'staffPosition':
        {
          request = this.itemId ? this.apiService.staffPosition.update(this.itemId, this.formInfo) : this.apiService.staffPosition.create(this.formInfo);
        }
        break;
      case 'playerPosition':
        {
          request = this.itemId ? this.apiService.playerPosition.update(this.itemId, this.formInfo) : this.apiService.playerPosition.create(this.formInfo);
        }
        break;
      case 'player':
        {
          request = this.itemId ? this.apiService.player.update(this.itemId, this.formInfo) : this.apiService.player.create(this.formInfo);
        }
        break;
      case 'team':
        {
          request = this.itemId ? this.apiService.team.update(this.itemId, this.formInfo) : this.apiService.team.create(this.formInfo);
        }
        break;
      case 'season':
        {
          request = this.itemId ? this.apiService.season.update(this.itemId, this.formInfo) : this.apiService.season.create(this.formInfo);
        }
        break;
      case 'tvStation':
        {
          request = this.itemId ? this.apiService.tvStation.update(this.itemId, this.formInfo) : this.apiService.tvStation.create(this.formInfo);
        }
        break;
      case 'competition':
        {
          request = this.itemId ? this.apiService.competition.update(this.itemId, this.formInfo) : this.apiService.competition.create(this.formInfo);
        }
        break;
      case 'competition-team':
        {
          request = this.itemId ? this.apiService.competitionTeam.update(this.itemId, this.formInfo) : this.apiService.competitionTeam.create(this.formInfo);
        }
        break;
      case 'competition-team-player':
        {
          request = this.itemId
            ? this.apiService.competitionTeamPlayer.update(this.itemId, this.formInfo)
            : this.apiService.competitionTeamPlayer.create(this.formInfo);
        }
        break;
      case 'schedule':
        {
          request = this.itemId ? this.apiService.schedule.update(this.itemId, this.formInfo) : this.apiService.schedule.create(this.formInfo);
        }
        break;
      case 'phase':
        {
          request = this.itemId ? this.apiService.phase.update(this.itemId, this.formInfo) : this.apiService.phase.create(this.formInfo);
        }
        break;
      case 'recipe':
        {
          request = this.itemId ? this.apiService.recipe.update(this.itemId, this.formInfo) : this.apiService.recipe.create(this.formInfo);
        }
        break;
      case 'recipeCategory':
        {
          const parentCategory = this.formInfo?.data?.find((value) => value?.key === 'parent');

          if (parentCategory && parentCategory?.value) {
            const { title } = parentCategory?.value;
            const currentCategoryTitle = this.formInfo?.data?.find((parentCategory) => parentCategory?.key === 'title')?.value;

            if (title === currentCategoryTitle) {
              this.sharedService.showNotification('warning', 'Nem lehet a receptkategóriának ugyan az a szülőkategóriája is!');
              this.disableSave = false;
              return;
            }
          }
          request = this.itemId ? this.apiService.recipeCategory.update(this.itemId, this.formInfo) : this.apiService.recipeCategory.create(this.formInfo);
        }
        break;
      case 'ingredient':
        {
          request = this.itemId ? this.apiService.ingredient.update(this.itemId, this.formInfo) : this.apiService.ingredient.create(this.formInfo);
        }
        break;
      case 'bestPractice':
        {
          request = this.itemId ? this.apiService.bestPractice.update(this.itemId, this.formInfo) : this.apiService.bestPractice.create(this.formInfo);
        }
        break;
      case 'allergen':
        {
          request = this.itemId ? this.apiService.allergen.update(this.itemId, this.formInfo) : this.apiService.allergen.create(this.formInfo);
        }
        break;
      case 'maestro':
        {
          request = this.itemId ? this.apiService.maestro.update(this.itemId, this.formInfo) : this.apiService.maestro.create(this.formInfo);
        }
        break;
      case 'glossary':
        request = this.itemId ? this.glossaryService.updateGlossary(this.itemId, this.formInfo) : this.glossaryService.createGlossary(this.formInfo);
        break;
      case 'gastro-experience-category':
        request = this.itemId
          ? this.apiService.gastro.experienceCategories.update(this.itemId, this.formInfo)
          : this.apiService.gastro.experienceCategories.create(this.formInfo);
        break;
      case 'selection':
        {
          request = this.itemId ? this.apiService.selection.update(this.itemId, this.formInfo) : this.apiService.selection.create(this.formInfo);
        }
        break;
      case 'did-you-know':
        {
          request = this.itemId ? this.apiService.didYouKnow.update(this.itemId, this.formInfo) : this.apiService.didYouKnow.create(this.formInfo);
        }
        break;
      case 'selection-item':
        {
          request = this.itemId ? this.apiService.selection.items.update(this.itemId, this.formInfo) : undefined;
        }
        break;
      case 'portal-user-badge':
        {
          request = this.itemId ? this.apiService.portalUser.badge.update(this.itemId, this.formInfo) : this.apiService.portalUser.badge.create(this.formInfo);
        }
        break;
      case 'athlete':
        {
          request = this.apiService.olympics.editParticipant(this.itemId, this.formInfo);
        }
        break;
      case 'olympics-medal':
        {
          request = this.itemId
            ? this.apiService.olympics.medals.editMedal(this.itemId, this.formInfo)
            : this.apiService.olympics.medals.createMedal(this.formInfo);
        }
        break;
      case 'star-occupation':
        {
          request = this.itemId
            ? this.apiService.starDictionary.occupations.updateOccupation(this.itemId, this.formInfo)
            : this.apiService.starDictionary.occupations.createOccupation(this.formInfo);
        }
        break;
      case 'star-birthplace':
        {
          request = this.itemId
            ? this.apiService.starDictionary.birthPlaces.updateBirthplace(this.itemId, this.formInfo)
            : this.apiService.starDictionary.birthPlaces.createBirthplace(this.formInfo);
        }
        break;
      case 'star-award':
        {
          request = this.itemId
            ? this.apiService.starDictionary.awards.updateAward(this.itemId, this.formInfo)
            : this.apiService.starDictionary.awards.createAward(this.formInfo);
        }
        break;
      case 'word-of-glossary':
        {
          request = this.itemId
            ? this.apiService.topRankingGlossary.wordOfGlossary.update(this.itemId, this.formInfo)
            : this.apiService.topRankingGlossary.wordOfGlossary.create(this.formInfo);
        }
        break;
      case 'quiz-categories':
        request = this.itemId ? this.apiService.quizCategories.update(this.itemId, this.formInfo) : this.apiService.quizCategories.create(this.formInfo);
        break;
      case 'secretDaysCalendar':
        {
          request = this.itemId
            ? this.apiService.secretDaysCalendar.update(this.itemId, this.formInfo)
            : this.apiService.secretDaysCalendar.create(this.formInfo, 'POST');
        }
        break;
      case 'breaking-news':
        {
          request = this.itemId ? this.apiService.breakingNews.update(this.itemId, this.formInfo) : this.apiService.breakingNews.create(this.formInfo);
        }
        break;
    }
    return request.pipe(takeUntil(this.destroy)).subscribe(
      (res) => {
        this.basicItemCreated.emit();
        this.disableSave = false;
        this.onCancel();
      },
      (err) => {
        if (err instanceof HttpErrorResponse) {
          const formErrors = this.errorHandler.handleFormError(err);
          if (formErrors?.length) {
            err.error.data = formErrors;
          }
          this.sharedService.generateErrorMessageFromHttpResponse(err, this.formInfo);
          this.disableSave = false;
        }
        return of(null);
      }
    );
  }

  onSave() {
    this.disableSave = true;
    this.formInfo = this.child.contentData;
    this.onCreateOrUpdate();
  }

  onExport() {
    this.formInfo = this.child.contentData;
    const nameFormControl = this.formInfo.data.find((e) => e.key === 'name');
    const permissionsFormControl = this.formInfo.data.find((e) => e.key === 'permissions');
    const data = {
      name: nameFormControl.value,
      permissions: permissionsFormControl.value,
    };
    this.dataToClipboard(data);
  }

  dataToClipboard(data: any) {
    const tempInput = document.createElement('textarea');
    document.body.appendChild(tempInput);
    tempInput.value = JSON.stringify(data);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);

    this.sharedService.showNotification('success', 'CMS.copied');
  }

  onFormControlStatesChanged(formControlStates: FormControlState[]) {
    this.isFormValid = formControlStates.every((formControlState) => formControlState.data.isValid);
  }

  protected handleErrors(err: any): Observable<any> {
    let errors = [];
    if (err instanceof HttpErrorResponse) {
      const errorsArray = err.error.errors || err.error.data;
      errors = errorsArray.map((error) => error.message);
      errors.forEach((errorMessage) => this.sharedService.showNotification('error', errorMessage));
    }
    return throwError(errors);
  }
}
