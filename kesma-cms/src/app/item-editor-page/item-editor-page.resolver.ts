import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { catchError, delayWhen, tap } from 'rxjs/operators';
import { ContentResponse } from 'src/app/core/api.definitons';
import { ApiService } from 'src/app/core/services/api.service';
import { GlossaryService } from '../core/services/api/glossary/glossary.service';
import { RoleGroupsApiService } from '../modules/role-groups/services/role-groups-api.service';
import { CurrentlyEditedUserService } from '../core/services/model/currenly-edited-user.service';
import { mapIdAndFormInfoToDetailedUser } from './item-editor-page.mappers';
import { ContributorsService } from '../modules/contributors/contributors.service';
import { PublicAuthorsService } from '../modules/public-authors/components/public-authors.service';
import { FormResponseAdapter } from '@shared/adapters/formResponseAdapter';
import { ShortUrlService } from '../modules/short-url/short-url.service';
import { MapsService } from '../modules/maps/api/maps.service';

@Injectable()
export class ItemEditorPageResolver {
  constructor(
    private apiService: ApiService,
    private contributorsService: ContributorsService,
    private publicAuthorsService: PublicAuthorsService,
    private roleGroupsApiService: RoleGroupsApiService,
    private router: Router,
    private currenlyEditedUserService: CurrentlyEditedUserService,
    private shortUrlService: ShortUrlService,
    private readonly mapsService: MapsService,
    private readonly glossaryService: GlossaryService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> | Promise<any> | any {
    const id = route.params.id || '';
    const type = route.queryParams.type || 'column';
    const contentGroup = route.queryParams.contentGroup || 'content-group';
    let request: Observable<any>;
    switch (contentGroup) {
      case 'content-group':
        request = this.apiService.getContentGroupFormInfo(type, id);
        break;
      case 'journal-issue':
        request = this.apiService.getContentGroupFormInfoSuffixed(type, id, 'pageNumberType');
        break;
      case 'games':
        request = this.apiService.getContentGroupFormInfo(type, id);
        break;
      case 'role-group':
        request = this.roleGroupsApiService.getRoleGroupFormInfo(id);
        break;
      case 'media':
        request = this.apiService.getMediaFormInfo(type, id);
        break;
      case 'user':
        request = this.apiService.getBasicItemFormInfo(type, id).pipe(
          tap((formInfo) => {
            this.currenlyEditedUserService.user = mapIdAndFormInfoToDetailedUser(id, formInfo);
          })
        );
        break;
      case 'user-permissions':
        request = this.apiService.getCmsUserPermissions(id);
        break;
      case 'portal':
        request = this.apiService.getPortalFormInfo(type, id);
        break;
      case 'program':
        request = this.apiService.getProgramFormInfo(type, id);
        break;
      case 'voting':
        request = this.apiService.getVotingFormInfo(type, id);
        break;
      case 'multi-vote':
        request = this.apiService.getVotingFormInfo(type, id, true);
        break;
      case 'extraordinary-notification':
        request = this.apiService.extraordinaryNotification.getFormInfo(id);
        break;
      case 'marketplace-category':
        request = this.apiService.getMarketplaceCategoryFormInfo(id);
        break;
      case 'marketplace-item':
        request = this.apiService.getMarketplaceItemFormInfo(id);
        break;
      case 'setting':
        request = this.apiService.getSettingFormInfo(id);
        break;
      case 'config':
        request = this.apiService.getConfigFormInfo(type, id);
        break;
      case 'seo-meta-tool':
        request = this.apiService.seoMeta.urls.get(id);
        break;
      case 'robots-content':
        request = this.apiService.seoMeta.robots.get(id);
        break;
      case 'quiz':
        request = this.apiService.getContentGroupFormInfo(type, id);
        break;
      case 'institution-category':
        request = this.apiService.getContentGroupFormInfo(type, id);
        break;
      case 'article-network-slot':
        request = this.apiService.getNetworkSlotFormInfo(type, id);
        break;
      case 'custom-built-page':
        request = this.apiService.getContentPageFormInfo(type, id);
        break;
      case 'banned-word':
        request = this.apiService.getBannedWord(id);
        break;
      case 'contributors':
        request = this.contributorsService.getContributor(id);
        break;
      case 'publicAuthors':
        request = this.publicAuthorsService.getPublicAuthor(id);
        break;
      case 'product':
        request = this.apiService.getSubscriptionProductFormInfo(id);
        break;
      case 'campaign':
        request = this.apiService.getMarketingCampaignFormInfo(id);
        break;
      case 'export-addressee':
        request = this.apiService.getExportAddresseeFormInfo(id);
        break;
      case 'popup-notifier':
        request = this.apiService.getPopupNotifierFormInfo(id);
        break;
      case 'orvosmet':
        request = this.apiService.getOrvosmetFormInfo(id);
        break;
      case 'map':
        request = this.mapsService.separateMapApiCalls(id);
        break;
      case 'facility':
        request = FormResponseAdapter.facilityAdapter(this.apiService.facility.get(id));
        break;
      case 'liveSport':
        request = FormResponseAdapter.liveSportAdapter(this.apiService.liveSport.get(id));
        break;
      case 'scheduleEvent':
        request = FormResponseAdapter.scheduleEventAdapter(this.apiService.scheduleEvent.get(id));
        break;
      case 'staff':
        request = FormResponseAdapter.staffAdapter(this.apiService.staff.get(id));
        break;
      case 'staffPosition':
        request = FormResponseAdapter.staffPositionAdapter(this.apiService.staffPosition.get(id));
        break;
      case 'playerPosition':
        request = FormResponseAdapter.playerPositionAdapter(this.apiService.playerPosition.get(id));
        break;
      case 'player':
        request = FormResponseAdapter.playerAdapter(this.apiService.player.get(id));
        break;
      case 'team':
        request = FormResponseAdapter.teamAdapter(this.apiService.team.get(id));
        break;
      case 'season':
        request = FormResponseAdapter.seasonAdapter(this.apiService.season.get(id));
        break;
      case 'tvStation':
        request = FormResponseAdapter.tvStationAdapter(this.apiService.tvStation.get(id));
        break;
      case 'competition':
        request = FormResponseAdapter.competitionAdapter(this.apiService.competition.get(id));
        break;
      case 'schedule':
        request = FormResponseAdapter.scheduleAdapter(this.apiService.schedule.get(id));
        break;
      case 'recipe':
        request = FormResponseAdapter.recipeAdapter(this.apiService.recipe.get(id));
        break;
      case 'recipeCategory':
        request = FormResponseAdapter.recipeCategoryAdapter(this.apiService.recipeCategory.get(id));
        break;
      case 'ingredient':
        request = FormResponseAdapter.ingredientAdapter(this.apiService.ingredient.get(id));
        break;
      case 'phase':
        request = FormResponseAdapter.phaseAdapter(this.apiService.phase.get(id));
        break;
      case 'bestPractice':
        request = FormResponseAdapter.bestPracticeAdapter(this.apiService.bestPractice.get(id));
        break;
      case 'allergen':
        request = FormResponseAdapter.allergenAdapter(this.apiService.allergen.get(id));
        break;
      case 'maestro':
        request = FormResponseAdapter.maestroAdapter(this.apiService.maestro.get(id));
        break;
      case 'short_url':
        request = this.shortUrlService.urlFormInfo$(id);
        break;
      case 'portal-users':
        request = this.apiService.portalUser.getUserForm(id === '0' ? null : id);
        break;
      case 'subscriptions-subscriptions':
        request = this.apiService.portalUser.getSubscriptionFormInfo(id);
        break;
      case 'partners':
        request = this.apiService.getPartnerFormInfo(id);
        break;
      case 'stations':
        request = this.apiService.getStationsFormInfo(id);
        break;
      case 'automails':
        request = this.apiService.getAutomailFormInfo(id);
        break;
      case 'glossary':
        request = this.glossaryService.getContentGroupFormInfo(id);
        break;
      case 'selection':
        request = FormResponseAdapter.selectionAdapter(this.apiService.selection.get(id));
        break;
      case 'did-you-know':
        request = this.apiService.didYouKnow.get(id);
        break;
      case 'athlete':
        request = this.apiService.olympics.getParticipantsFormInfo(id);
        break;
      case 'star-occupation':
        request = this.apiService.starDictionary.occupations.getOccupation(id);
        break;
      case 'star-birthplace':
        request = this.apiService.starDictionary.birthPlaces.getBirthplace(id);
        break;
      case 'star-award':
        request = this.apiService.starDictionary.awards.getAward(id);
        break;
      case 'secretDaysCalendar':
        request = this.apiService.secretDaysCalendar.get(id);
        break;
      case 'breaking-news':
        request = this.apiService.breakingNews.getFormInfo(id);
        break;
    }

    return request.pipe(
      catchError((error) => {
        this.router.navigate(['/', 'admin', 'error'], {
          state: { errorResponse: JSON.stringify(error) },
        });
        return throwError(error);
      }),
      delayWhen((contentPage: ContentResponse) => {
        return of(contentPage);
      })
    );
  }
}
