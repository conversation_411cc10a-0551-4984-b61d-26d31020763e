<app-image-uploader #uploader (afterUpload)="imageUploaded.emit($event)"></app-image-uploader>
<app-media-filters *ngIf="options?.showFilters" (filterChange)="onFilterChange($event)"> </app-media-filters>

@if ((explicitSource$ | async) ?? data.values(); as images) {
  <div class="content-row">
    <div class="content-pagination">
      <div
        class="content-list"
        #dropList="dropList"
        cdkDropListGroup
        appDropZone
        appDropList
        (filesDropped)="uploader.nzBeforeUpload($event)"
        [cdkDropListGroupDisabled]="!explicitSource$"
        [data]="images"
      >
        <div cdkDropList (cdkDropListDropped)="dropList.afterDropped(); orderChanged.emit(images)"></div>
        @for (image of images; track image.id) {
          <div cdkDropList (cdkDropListEntered)="dropList.whileDropping($event)" (cdkDropListDropped)="dropList.afterDropped()">
            <div class="content-list-item-box" (click)="handleClick(image, $event)" [class.picked]="selection.isPicked(image)" cdkDrag>
              <img class="content-list-item" draggable="false" [src]="image.selectedVariant?.articleSize || image.url.thumbnail" alt="" />
              <div class="image-details">
                <nz-tag nzColor="var(--media-secondary-color)">
                  {{ image.resolution.join('x') }}
                </nz-tag>
                <nz-button-group>
                  @if (customActionsTemplate; as actionTemplate) {
                    <ng-container *ngTemplateOutlet="actionTemplate; context: { $implicit: image }"></ng-container>
                  }
                  <button nz-button (click)="handlePreviewClick(image, $event)">
                    <span nz-icon nzType="zoom-in" nzTheme="outline"></span>
                  </button>
                </nz-button-group>
              </div>
            </div>
          </div>
        } @empty {
          <nz-empty *ngIf="(isLoading$ | async) === false" class="grid-col-full centered" nzNotFoundContent="Nincs megjelenítendő adat"> </nz-empty>
        }
        <nz-spin
          *ngIf="isLoading$ | async"
          class="grid-col-full"
          [class.centered]="explicitSource$ ? !(explicitSource$ | async)?.length : !data.size"
          nzSize="large"
          nzTip="Képek betöltése..."
        >
        </nz-spin>
      </div>
      <nz-pagination
        [nzDisabled]="isLoading$ | async"
        [nzPageIndex]="pageIndex()"
        (nzPageIndexChange)="handlePaginationChange($event)"
        [nzTotal]="count()"
        [nzPageSize]="mediaConstants.itemsPerPage">
      </nz-pagination>
    </div>
    <app-image-editor class="content-editor" *ngIf="options?.showDetails" [selection]="selection"></app-image-editor>
  </div>
}
@if (modalRef) {
  <button nz-button nzType="primary" class="w-100" [disabled]="!selection.hasValue" (click)="modalRef.triggerOk()">Kiválasztás</button>
}
