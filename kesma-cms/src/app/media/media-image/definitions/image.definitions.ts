import { CropperPosition, ImageCropperComponent } from 'ngx-image-cropper';
import { MediaObject } from '@media/shared/definitions/media.definitions';

export type Image = MediaObject & {
  resolution: [number, number];
  selectedVariant: ImageVariant;
  type: ImageType;
  url: ImageUrl;
  alt?: string; //Two different BE endpoints will receive differently. > altText, alt
  widthMessage?: string;
};

export type ImageVariant = {
  id: number;
  date: Date;
  fileSize: number;
  focusPointX?: number;
  focusPointY?: number;
  resolution: [number, number];
  mimeType: string;
  portalSlug: string;
  publicUrl: string;
  selected: boolean;
  articleSize?: string;
  thumbnail?: string;
  type: string;
  url: string;
};

export type ImageUrl = {
  fullSize?: string;
  articleSize?: string;
  thumbnail?: string;
};

export type UploadFocusedVariantDto = {
  aspectRatio: string;
  imageBase64: string;
  cropper?: ImageCropperComponent;
};

export type FocusedPointsDto = {
  aspectRatio: string;
  variantId: number;
  url: string; // In the media api response under the "url" key for the crop.
  /*
   * It can be anything, BE stores it as json.
   * But the preferred architecture is the ngx-cropper-image CropperPosition interface properties.
   */
  croppingCoordinates: CropperPosition;
};

export type ImageType = 'common' | 'avatar' | 'contentBlockImg' | 'starDictionary';

export type ImageRequestType = 'images' | 'avatars' | 'content-block-images' | 'star-dictionary-images';
