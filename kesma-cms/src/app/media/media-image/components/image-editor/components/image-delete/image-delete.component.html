@if (isImageUsed() !== null) {
  <div class="flex-column">
    <strong>Figyelem!</strong>
    @if (isImageUsed()) {
      A kép használatban van. A törlési szándék megerősítésével a felhasznált helyeken nem fog megjelenni a törölt kép.
      @if (isUsedInDeletedEntity()) {
      <strong>(A kép felhasználási helye ismeretlen, az entitás nem található – lehet, hogy törölve lett.)</strong>
    }
    } @else {
      Biztosan törölni szeretné a képet?
    }
  </div>
  <button nz-button nzDanger nzType="primary" [nzLoading]="isLoading()" (click)="deleteImage()">Megerősítés</button>
} @else {
  <nz-spin class="spinner" nzTip="Képadatok betöltése folyamatban..."></nz-spin>
}
