import { ChangeDetectionStrategy, Component, DestroyRef, inject, Input, OnInit, signal } from '@angular/core';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { SharedService } from '@shared/services/shared.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { catchError, finalize, map, switchMap } from 'rxjs/operators';
import { of, take, throwError } from 'rxjs';
import { ImageService } from '@media/media-image/services/image.service';
import { Image } from '@media/media-image/definitions';
import { ImageEmitter } from '@media/media-image/utils/image.emitter';

@Component({
  selector: 'app-image-delete',
  templateUrl: 'image-delete.component.html',
  styleUrl: 'image-delete.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NzSpinComponent, NzButtonComponent],
})
export class ImageDeleteComponent implements OnInit {
  @Input() image: Image;

  /**
   * Shows whether we are waiting for the image to be deleted or not.
   */
  isLoading = signal<boolean>(false);

  /**
   * Shows whether any variant of the image is used.
   */
  isImageUsed = signal<boolean>(null);

  /**
   * Indicates whether the image is used in the context of an entity that has been deleted from the system.
   */
  isUsedInDeletedEntity = signal<boolean>(false);

  private readonly nzModalRef = inject(NzModalRef);
  private readonly nzModalData = inject(NZ_MODAL_DATA);
  private readonly sharedService = inject(SharedService);
  private readonly imageService = inject(ImageService);
  private readonly imageEmitter = inject(ImageEmitter);
  private readonly destroyRef = inject(DestroyRef);

  ngOnInit(): void {
    const { image } = this.nzModalData;
    this.image = image;

    this.imageService
      .isUsedByVariantIds$(image.variantIds)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        catchError((error) => {
          this.sharedService.showNotification('error', 'A kép nem található, feltehetően már törlésre került!');
          this.nzModalRef.close();
          return throwError(() => error);
        }),
        switchMap(({ isImageUsed }) => {
          if (!isImageUsed) {
            return of({ isImageUsed, places: [] });
          }
          return this.imageService.placeOfUse$(image.selectedVariant.id).pipe(
            map((places) => ({
              isImageUsed,
              places,
            }))
          );
        })
      )
      .subscribe(({ isImageUsed, places }) => {
        this.isUsedInDeletedEntity.set(!places.length && isImageUsed);
        this.isImageUsed.set(isImageUsed);
      });
  }

  deleteImage(): void {
    this.isLoading.set(true);
    this.imageService
      .deleteImageByVariantIds$([this.image.id], this.image.variantIds)
      .pipe(
        finalize(() => this.nzModalRef.close()),
        catchError((error) => {
          this.isLoading.set(false);
          this.sharedService.showNotification('error', 'A kép törlése ismeretlen okokból meghiúsult, kérem később próbálja újra.');
          return throwError(() => error);
        }),
        take(1)
      )
      .subscribe(() => {
        this.sharedService.showNotification('success', 'Kép törlése sikeres.');
        this.imageEmitter.remove(this.image);
      });
  }
}
