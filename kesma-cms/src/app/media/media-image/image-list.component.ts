import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, OnInit, Output, TemplateRef } from '@angular/core';
import { ListComponent } from '@media/shared/components/list.component';
import { forkJoin, Observable, switchMap } from 'rxjs';
import { Image, ImageType } from '@media/media-image/definitions';
import { NzImageService } from 'ng-zorro-antd/image';
import { ImageService } from '@media/media-image/services/image.service';
import { tap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SelectionMode } from '@media/shared/selection';
import { CommonModule } from '@angular/common';
import { NgZorroModule } from '@shared/modules/ng-zorro/ng-zorro.module';
import { CdkDrag, CdkDropList, CdkDropListGroup } from '@angular/cdk/drag-drop';
import { NzEmptyComponent } from 'ng-zorro-antd/empty';
import { ImageEditorComponent, ImageUploaderComponent } from '@media/media-image/components';
import { DropListDirective } from '@media/shared/directives/drop-list.directive';
import { DropZoneDirective } from '@media/shared/directives/drop-zone.directive';
import { ImageEmitter } from '@media/media-image/utils/image.emitter';
import { NzPaginationComponent } from 'ng-zorro-antd/pagination';
import { MediaFiltersComponent } from '@media/shared/components/media-filters/media-filters.component';

@Component({
  selector: 'app-image-list',
  templateUrl: 'image-list.component.html',
  styleUrl: 'image-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [NzImageService],
  imports: [
    CommonModule,
    NgZorroModule,
    NzEmptyComponent,
    CdkDropList,
    CdkDrag,
    ImageUploaderComponent,
    ImageEditorComponent,
    CdkDropListGroup,
    DropListDirective,
    DropZoneDirective,
    NzPaginationComponent,
    MediaFiltersComponent,
  ],
})
export class ImageListComponent extends ListComponent<Image> implements OnInit {
  /**
   * Options for customizing the component's display.
   */
  @Input() options: Record<string, boolean> = {
    showFilters: true,
    showDetails: true,
  };
  /**
   * Shows what type of images to query.
   */
  @Input() imageType: ImageType = 'common';
  /**
   * Shows whether multiple selections are possible.
   */
  @Input() canMultiPick: boolean = true;
  /**
   * We can add additional unique features to the images.
   */
  @Input() customActionsTemplate: TemplateRef<{ $implicit: Image }> | null = null;
  /**
   * It provides a solution for cases where explicit images need to be displayed in the list.
   */
  @Input() explicitSource$: Observable<Image[]> = null;

  /**
   * The currently selected image to be displayed at the top of the list, e.g. a featured image for an article.
   */
  @Input() set selectedImage(image: Image) {
    super.unshift(image);
    this.selection.toggle(image);
  }

  @Output() orderChanged = new EventEmitter<Image[]>();
  @Output() imageUploaded = new EventEmitter<Image>();

  private readonly imageService = inject(ImageService);
  private readonly nzImage = inject(NzImageService);
  private readonly imageEmitter = inject(ImageEmitter);

  ngOnInit(): void {
    Object.keys(this.nzData).forEach((key) => (this[key] = this.nzData[key]));
    this.imageService.imageType.set(this.imageType);

    if (this.explicitSource$) {
      this.canLoadMore = false;
      this.setIsLoading(false);
      // If explicit source is provided, no need to query images.
      return;
    }

    this.loadMore$
      .pipe(
        switchMap((queryParams) => this.imageService.images$(queryParams)),
        tap(() => this.setIsLoading(false)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ count, images }) => {
        this.pushOrUpdate(images);
        this.count.set(+count);
        this.canLoadMore = this.data.size < this.count();
      });

    forkJoin([
      this.imageEmitter.remove$.pipe(tap((image) => this.remove(image))),
      this.imageEmitter.unshift$.pipe(tap((image) => this.unshift(image))),
      this.imageEmitter.update$.pipe(tap((image) => this.pushOrUpdate(image))),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  handleClick(image: Image, mouseEvent: MouseEvent): void {
    const { ctrlKey, shiftKey } = mouseEvent;
    // Set the selection mode based on whether multi-pick is allowed and CTRL or SHIFT key is pressed.
    this.selection.mode = this.canMultiPick && (ctrlKey || shiftKey) ? SelectionMode.MANY : SelectionMode.LATEST_ONLY;
    this.selection.toggle(image);
  }

  handlePreviewClick(image: Image, event: Event): void {
    event.stopPropagation(); // For avoid image selection.
    this.nzImage.preview([{ src: image.selectedVariant?.publicUrl || image.url.fullSize }]);
  }

  override unshift(image: Image): void {
    super.unshift(image);
    this.handlePaginationChange(1);
  }
}
