import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  effect,
  ElementRef,
  inject,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { CANVAS_HEIGHT, CANVAS_WIDTH, MOSAIC_EDITOR_CONFIG } from '@media/mosaic-editor/mosaic-editor.config';
import { SharedModule } from '@shared/shared.module';
import { ImageCroppedEvent, ImageCropperComponent } from 'ngx-image-cropper';
import { ImageListModal } from '@media/media-image/modals/image-list.modal';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { InitModelService } from '@core/services/model/init-model/init-model.service';
import { ImageService } from '@media/media-image/services/image.service';
import { SharedService } from '@shared/services/shared.service';
import { v4 as uuidv4 } from 'uuid';
import { Image } from '@media/media-image/definitions';
import { ZoomWithNumpadDirective } from '@media/mosaic-editor/zoom-with-numpad.directive';

@Component({
  selector: 'app-mosaic-editor',
  templateUrl: './mosaic-editor.component.html',
  styleUrl: './mosaic-editor.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SharedModule, ImageCropperComponent, ZoomWithNumpadDirective],
})
export class MosaicEditorComponent implements AfterViewInit {
  readonly #imageModal = inject(ImageListModal);
  readonly #destroyRef = inject(DestroyRef);
  readonly #changeDetectorRef = inject(ChangeDetectorRef);
  readonly #initModelService = inject(InitModelService);
  readonly #imageService = inject(ImageService);
  readonly #sharedService = inject(SharedService);

  readonly editorConfig = MOSAIC_EDITOR_CONFIG;

  imageBlobs = Array(MOSAIC_EDITOR_CONFIG['template_01'].numberOfCrops);
  croppedImageBlobs = Array(MOSAIC_EDITOR_CONFIG['template_01'].numberOfCrops);

  readonly canvasWidth = CANVAS_WIDTH;
  readonly canvasHeight = CANVAS_HEIGHT;

  readonly currentTemplate = signal(MOSAIC_EDITOR_CONFIG['template_01']);
  readonly canvasRef = viewChild<ElementRef<HTMLCanvasElement>>('canvas');

  readonly isLoading = signal<boolean>(false);

  readonly mosaicCreated = output<Image>();

  canvasRenderingContext2D: CanvasRenderingContext2D;

  constructor() {
    effect(() => {
      this.imageBlobs.length = this.currentTemplate().numberOfCrops;
      this.croppedImageBlobs.length = this.currentTemplate().numberOfCrops;
      this.clearCanvas();
    });
  }

  ngAfterViewInit(): void {
    this.canvasRenderingContext2D = this.canvasRef().nativeElement.getContext('2d');
  }

  imageCropped(event: ImageCroppedEvent, cropperIndex: number): void {
    this.croppedImageBlobs[cropperIndex] = event.blob;

    if (this.croppedImageBlobs.includes(undefined)) {
      // If there is any empty (undefined) image element, skip canvas rendering.
      return;
    }

    this.clearCanvas();

    this.loadImageBitmaps(this.croppedImageBlobs)
      .then((loadedImages) => {
        this.currentTemplate().drawAction(this.canvasRenderingContext2D, loadedImages);
      })
      .catch((error) => {
        console.error(`Nem sikerült betölteni az összes képet: ${error}`);
      });
  }

  openMediaStore(cropperIndex: number): void {
    this.#imageModal
      .open$()
      .pipe(takeUntilDestroyed(this.#destroyRef))
      .subscribe((image) => {
        this.convertUrlToBase64(image.url.fullSize).then((blob) => {
          this.imageBlobs[cropperIndex] = new File([blob], image.fileName, { type: image.selectedVariant.mimeType });
          // Required for ngx-cropper to detect changes
          this.#changeDetectorRef.markForCheck();
        });
      });
  }

  onSave(): void {
    this.isLoading.set(true);

    const canvas = this.canvasRef().nativeElement;
    const filename = `${uuidv4()}-${new Date().toLocaleString()}.webp`;
    const imageType = 'image/webp';

    canvas.toBlob(
      (blob) => {
        const { userId, userName } = this.#initModelService.data;
        const body = new FormData();
        const file = new File([blob], filename, { type: imageType });
        body.append('image', file);
        body.append('userId', userId);
        body.append('userName', userName);
        body.append('mediaType', 'common');
        this.#imageService.upload$(body).subscribe({
          next: (response) => {
            this.isLoading.set(false);
            this.#sharedService.showNotification('success', 'A mozaikkép feltöltése és beállítása sikeresen megtörtént.');
            this.mosaicCreated.emit(response);
            this.clearCanvas();
            this.croppedImageBlobs.fill(undefined);
            this.imageBlobs.fill(undefined);
          },
          error: () => {
            this.isLoading.set(false);
            this.#sharedService.showNotification('error', 'A mozaikkép feltöltése és beállítása nem sikerült. Kérjük, próbálja meg újra később.');
          },
        });
      },
      imageType,
      1
    );
  }

  protected hasEmptyElement(): boolean {
    return this.croppedImageBlobs.includes(undefined);
  }

  /** Converts an image URL to a base64-encoded string. */
  private async convertUrlToBase64(imageUrl: string): Promise<Blob> {
    const response = await fetch(imageUrl);
    return await response.blob();
  }

  /** Waits for all Blobs to be converted into ImageBitmaps. */
  private loadImageBitmaps(blobs: Blob[]): Promise<ImageBitmap[]> {
    return Promise.all(blobs.map((blob) => createImageBitmap(blob)));
  }

  private clearCanvas(): void {
    this.canvasRenderingContext2D && this.canvasRenderingContext2D.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
  }
}
