<nz-page-header [nzGhost]="false">
  <nz-page-header-title class="block-title">Sablonok</nz-page-header-title>
  <nz-page-header-extra>
    <nz-space>
      <button
        *nzSpaceItem
        nz-button
        nzType="primary"
        [disabled]="hasEmptyElement()"
        [nzLoading]="isLoading()"
        (click)="onSave()">
        Mentés és beállítás
      </button>
    </nz-space>
  </nz-page-header-extra>
</nz-page-header>
<div class="template-list">
  @for (config of editorConfig | keyvalue; track $index) {
    <div
      class="template-box"
      [class.current]="currentTemplate().key === config.key"
      (keydown.enter)="currentTemplate.set(config.value)"
      (click)="currentTemplate.set(config.value)">
      <img class="template" [src]="editorConfig[config.key].thumbnail" alt="" />
    </div>
  }
</div>
<nz-page-header [nzGhost]="false">
  <nz-page-header-title class="block-title">Kiválasztás és fókuszálás</nz-page-header-title>
</nz-page-header>
<div class="cropper-list">
  @for (_ of [].constructor(currentTemplate().numberOfCrops); track $index) {
    <div class="cropper-box">
      <button nz-button nzType="dashed" class="select" (click)="openMediaStore($index)" [disabled]="isLoading()">
        <nz-icon nzType="select" nzTheme="outline" />
        {{ $index + 1 }}. kép kiválasztása
      </button>
      @if (imageBlobs[$index]; as imageFile) {
        <image-cropper
          appZoomWithNumpad
          #zoom="appZoomWithNumpad"
          (keydown)="zoom.handleKeydown($event)"
          tabindex="1"
          class="cropper"
          format="webp"
          [resizeToWidth]="canvasWidth"
          [resizeToHeight]="canvasHeight"
          [onlyScaleDown]="true"
          [autoCrop]="true"
          [disabled]="isLoading()"
          [maintainAspectRatio]="true"
          [aspectRatio]="currentTemplate().ratios[$index]"
          (imageCropped)="imageCropped($event, $index)"
          [roundCropper]="currentTemplate()?.roundCropper?.includes($index)"
          [transform]="{
            scale: zoom?.currentScale()
          }"
          backgroundColor="black"
          [imageFile]="imageFile">
        </image-cropper>
      } @else {
        <div class="empty">
          <nz-icon nzType="file-image" nzTheme="outline" />
          {{ $index + 1 }}. kép még nincs kiválasztva!
        </div>
      }
    </div>
  }
</div>
<nz-page-header [nzGhost]="false">
  <nz-page-header-title class="block-title">
    Előnézet
  </nz-page-header-title>
</nz-page-header>
<div class="canvas-box">
  <canvas class="canvas" #canvas [width]="canvasWidth" [height]="canvasHeight"></canvas>
  @if (hasEmptyElement()) {
    <div class="canvas-message">
      Az előnézet megjelenítéséhez kérlek, válaszd ki az összes képet.
    </div>
  }
</div>
