import { Directive, signal } from '@angular/core';

@Directive({
  selector: '[appZoomWithNumpad]',
  exportAs: 'appZoomWithNumpad',
})
export class ZoomWithNumpadDirective {
  /**
   * Represents the current zoom scale.
   * Default is 1 (100%).
   */
  readonly currentScale = signal(1);

  /**
   * Handles keydown events and updates the zoom scale accordingly.
   * - `NumpadAdd`: Zoom in (+)
   * - `NumpadSubtract`: Zoom out (-)
   */
  handleKeydown(event: KeyboardEvent): void {
    switch (event.code) {
      case 'NumpadAdd':
        this.#increaseZoom();
        return;
      case 'NumpadSubtract':
        this.#decreaseZoom();
        return;
    }
  }

  /** Increases the zoom scale by 0.01. */
  #increaseZoom(): void {
    this.currentScale.update((scale) => scale + 0.01);
  }

  /** Decreases the zoom scale by 0.01. */
  #decreaseZoom(): void {
    this.currentScale.update((scale) => scale - 0.01);
  }
}
