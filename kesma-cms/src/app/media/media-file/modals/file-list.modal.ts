import { inject, Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { NzModalService } from 'ng-zorro-antd/modal';
import { MEDIA_CONSTANTS } from '@media/shared/constans';
import { FileListComponent } from '@media/media-file/file-list.component';
import { MediaFile } from '@media/media-file/definitions/file.definitions';

@Injectable({
  providedIn: 'root',
})
export class FileListModal {
  private readonly nzModal = inject(NzModalService);

  /**
   * Opens a modal to display a list of files.
   * Emits the currently selected file when closed, if there is one.
   */
  open$(): Observable<MediaFile> {
    const file$ = new Subject<MediaFile>();

    this.nzModal.create({
      nzTitle: 'Fájl kiválasztása',
      nzClosable: false,
      nzContent: FileListComponent,
      nzFooter: null,
      nzClassName: MEDIA_CONSTANTS.uniqueNzModalClassName,
      nzWidth: MEDIA_CONSTANTS.nzModalWidth,
      nzOnOk: ({ selection }) => file$.next(selection.value),
    });

    return file$;
  }
}
