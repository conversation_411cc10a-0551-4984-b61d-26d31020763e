@use 'shared' as *;
@use 'grid-list' as *;

:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 20px;
  .grid-col-full {
    grid-column: -1/1;
  }
  .centered {
    @include center-vertically;
    width: 200px;
  }
  .empty {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: $white;
    text-align: center;
    height: 100%;
    color: var(--media-primary-color);
    gap: 20px;
    &-icon {
      font-size: 40px;
      color: var(--media-secondary-color);
    }
  }
  .file-name {
    width: 100%;
    display: block;
  }
  .bold {
    font-weight: 700;
  }
  .content-editor .content-list-item {
    height: 200px;
  }
  .dropzone {
    position: relative;
    border: 3px dashed var(--media-secondary-color);
    opacity: 0.5;
  }
  app-file-uploader {
    position: absolute;
    right: 24px;
    top: 10px;
  }
  nz-pagination {
    margin-left: auto;
  }
}
