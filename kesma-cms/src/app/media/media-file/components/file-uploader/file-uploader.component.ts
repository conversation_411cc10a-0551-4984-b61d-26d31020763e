import { ChangeDetectionStrategy, Component, DestroyRef, inject, output, signal } from '@angular/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { FileService } from '@media/media-file/services/file.service';
import { catchError, finalize, map, switchMap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { concat, of } from 'rxjs';
import { SharedService } from '@shared/services/shared.service';
import { InitModelService } from '@core/services/model/init-model/init-model.service';
import { FileMimeType, MediaFile } from '@media/media-file/definitions';

@Component({
  selector: 'app-file-uploader',
  templateUrl: './file-uploader.component.html',
  styleUrl: './file-uploader.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NzButtonComponent, NzIconDirective],
})
export class FileUploaderComponent {
  /**
   * Shows whether we are waiting for the file to upload or not.
   */
  isLoading = signal<boolean>(false);
  /**
   * An array of acceptable MIME types for file uploads.
   */
  readonly acceptableExtensions: string[] = [FileMimeType.PDF, FileMimeType.XLSX] as const;

  private readonly fileService = inject(FileService);
  private readonly sharedService = inject(SharedService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly initModelService = inject(InitModelService);

  afterUpload = output<MediaFile>();

  onChange(event: { target: { files: FileList } }): void {
    const files = event.target.files;
    this.nzBeforeUpload(files);
  }

  nzBeforeUpload(fileList: FileList): boolean {
    const requests$ = [];
    this.isLoading.set(true);
    Array.from(fileList).forEach((file: File) => {
      if (!file) {
        this.sharedService.showNotification('error', 'A fájl nem található.');
        return;
      }
      if (!this.acceptableExtensions.includes(file.type)) {
        this.sharedService.showNotification('error', `A(z) ".${file.type}" kiterjesztésű fájlok nem engedélyezettek.`);
        return;
      }
      const request$ = this.fileService.mediaAPIUpload$(this.createFormDataForRequest(file)).pipe(
        switchMap((backendFile: MediaFile) =>
          this.fileService.getFormInfoForAPIUpload$().pipe(
            switchMap((formInfo) => {
              formInfo.data.forEach((field) => {
                switch (field.key) {
                  case 'variantId':
                    field.value = backendFile.selectedVariant.id;
                    break;
                  case 'fileUrl':
                    field.value = backendFile.url.fullSize;
                    break;
                  case 'fileName':
                    field.value = backendFile.fileName;
                    break;
                  case 'fileSize':
                    field.value = backendFile.fileSize;
                    break;
                  case 'mimeType':
                    field.value = backendFile.selectedVariant.mimeType;
                    break;
                }
              });
              return this.fileService.upload$(formInfo).pipe(map(() => backendFile));
            })
          )
        ),
        finalize(() => this.isLoading.set(false)),
        takeUntilDestroyed(this.destroyRef),
        catchError((error) => {
          const message = error?.error?.message;
          this.sharedService.showNotification('error', message || 'A fájl feltöltése ismeretlen okokból sikertelen.');
          return of(null);
        })
      );
      requests$.push(request$);
    });

    concat(...requests$)
      .pipe(
        finalize(() => this.isLoading.set(false)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe({
        next: (backendFile: MediaFile) => {
          this.afterUpload.emit(backendFile);
          this.sharedService.showNotification('success', 'A fájl feltöltése sikeres.');
        },
      });

    return true; // We return true to avoid the ng-zorro upload feedback.
  }

  private createFormDataForRequest(file: File): FormData {
    const body = new FormData();
    const { userId, userName } = this.initModelService.data;

    body.append('file', file);
    body.append('userId', userId);
    body.append('userName', userName);

    return body;
  }
}
