import { inject, Injectable } from '@angular/core';
import { ReqService } from '@external/http';
import { DomainService } from '@core/modules/admin/services/domain.service';
import { Observable } from 'rxjs';
import { MediaFile } from '@media/media-file/definitions/file.definitions';
import { mapDomainToBackendDomain } from '@media/shared/mappers/domain.mapper';
import { IHttpParams } from '@shared/definitions/shared.definitions';
import { ApiResult } from '@trendency/kesma-ui';
import { BaseContentVariant } from '@core/api.definitons';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  private readonly reqService = inject(ReqService);
  private readonly domainService = inject(DomainService);

  files$(httpParams: object): Observable<{ count: number; files: MediaFile[] }> {
    return this.reqService.get(
      `media/files/${mapDomainToBackendDomain(this.domainService.currentDomain.key)}`,
      {
        params: httpParams as IHttpParams,
      },
      'media'
    );
  }

  /**
   * Uploads the file to the Media API.
   * @param formData
   */
  mediaAPIUpload$(formData: FormData | object): Observable<object> {
    return this.reqService.post(`media/upload-file/${mapDomainToBackendDomain(this.domainService.currentDomain.key)}`, formData, {}, 'media');
  }

  /**
   * Uploading a file to the central API requires a formInfo object.
   * This method retrieves the empty form.
   */
  getFormInfoForAPIUpload$(): Observable<ApiResult<BaseContentVariant[]>> {
    return this.reqService.get(`api/hu/hu/media/file/upload`);
  }

  /**
   * Uploads a file to the central API.
   * Must be used after {@link mediaAPIUpload$}
   */
  upload$(data: ApiResult<BaseContentVariant[]>): Observable<ApiResult<BaseContentVariant[]>> {
    return this.reqService.post(`api/hu/hu/media/file/upload`, data);
  }
}
