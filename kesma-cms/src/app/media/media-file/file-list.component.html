<app-file-uploader #uploader (afterUpload)="unshift($event)" />
<app-media-filters buttonText="Általam feltöltött fájlok" (filterChange)="onFilterChange($event)" />
<div class="content-row">
  <div class="content-pagination">
    <div class="content-list" appDropZone (filesDropped)="uploader.nzBeforeUpload($event)">
      @for (file of data.values(); track file.id) {
        <div class="content-list-item-box"
             (keydown.enter)="selection.toggle(file)"
             (click)="selection.toggle(file)"
             [class.picked]="selection.isPicked(file)">
          <ng-container
            *ngTemplateOutlet="FilePreviewTemplate; context: { mimeType: file.selectedVariant.mimeType }"></ng-container>
          <strong>{{ file.fileName }}</strong>
        </div>
      } @empty {
        <nz-empty
          *ngIf="(isLoading$ | async) === false"
          class="grid-col-full centered"
          nzNotFoundContent="Nincs megjelenítendő adat">
        </nz-empty>
      }
      <nz-spin
        *ngIf="isLoading$ | async"
        class="grid-col-full"
        [class.centered]="!data.size"
        nzSize="large"
        nzTip="Fájlok betöltése..."
      >
      </nz-spin>
    </div>
    <nz-pagination
      [nzDisabled]="isLoading$ | async"
      [nzPageIndex]="pageIndex()"
      [nzTotal]="count()"
      (nzPageIndexChange)="handlePaginationChange($event)"
      [nzPageSize]="mediaConstants.itemsPerPage">
    </nz-pagination>
  </div>
  <div class="content-editor">
    @if (selection.hasValue) {
      <nz-card nzSize="small" [nzActions]="[OpenAction]">
        <ng-container
          *ngTemplateOutlet="FilePreviewTemplate; context: { mimeType: selection.value.selectedVariant.mimeType }">
        </ng-container>
        <nz-list nzSize="small">
          <nz-list-item class="file-name bold">{{ selection.value.fileName }}</nz-list-item>
          <nz-list-item>
            Keletkezési dátum:
            <nz-tag class="bold" nzColor="var(--media-secondary-color)">
              {{ selection.value.date }}
            </nz-tag>
          </nz-list-item>
          <nz-list-item>
            Fájl mérete:
            <nz-tag class="bold" nzColor="var(--media-secondary-color)">
              {{ selection.value.fileSize | storageIECFormat }}
            </nz-tag>
          </nz-list-item>
          <nz-list-item>
            Feltöltő:
            @if (selection.value.userName; as userName) {
              <nz-tag class="bold" nzColor="var(--media-secondary-color)">
                {{ userName }}
              </nz-tag>
            } @else {
              <span>Ismeretlen</span>
            }
          </nz-list-item>
        </nz-list>
      </nz-card>
    } @else {
      <div class="empty">
        <i nz-icon nzType="file" class="empty-icon" nzTheme="outline"></i>
        A fájladatok megtekintéséhez kérem válasszon egy fájlt.
      </div>
    }
  </div>
</div>

<ng-template #FilePreviewTemplate let-mimeType="mimeType">
  @switch (mimeType) {
    @case (FileMimeType.XLSX) {
      <img class="content-list-item" draggable="false" src="assets/images/icons/xlsx-icon.svg" alt="XLSX" />
    }
    @case (FileMimeType.PDF) {
      <img class="content-list-item" draggable="false" src="assets/images/icons/pdf-icon.svg" alt="PDF" />
    }
    @default {
      Ismeretlen MIME típus:
      <mark>{{ mimeType }}</mark>
    }
  }
</ng-template>

<ng-template #OpenAction>
  <a [href]="selection.value.url.fullSize" class="bold" target="_blank">
    {{ selection.value.selectedVariant.mimeType === FileMimeType.PDF ? "Fájl megtekintése új ablakban" : "Fájl letöltése" }}
  </a>
</ng-template>
