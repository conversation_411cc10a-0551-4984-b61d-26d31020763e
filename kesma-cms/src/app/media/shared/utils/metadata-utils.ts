import { Image } from '@media/media-image/definitions';

export const processJsonMetaData = (image: Image): Image => {
  const metaDataStr = image.mediaDetailsMeta as string;
  if (metaDataStr) {
    try {
      const metaData = JSON.parse(metaDataStr);
      return {
        ...image,
        mediaDetailsMeta: Array.isArray(metaData) ? {} : metaData || {},
      };
    } catch (_error) {
      console.error('The mediaDetailsMeta not a valid JSON!');
      return {
        ...image,
        mediaDetailsMeta: {},
      };
    }
  }
  return {
    ...image,
    mediaDetailsMeta: {},
  };
};
