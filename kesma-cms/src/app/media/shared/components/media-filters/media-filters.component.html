<input
  nz-input
  class="input"
  placeholder="Keresés..."
  [(ngModel)]="queryParams.text"
  (ngModelChange)="onSearchChange($event)"
/>

<nz-range-picker nzAllowClear class="input" [(ngModel)]="dates" (ngModelChange)="onDateChange()"></nz-range-picker>

<nz-tag nzMode="checkable" class="input checkable" [(nzChecked)]="queryParams.only_mine" (nzCheckedChange)="search()">
  {{ buttonText() }}
</nz-tag>
