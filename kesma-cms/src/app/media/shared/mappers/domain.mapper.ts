import { DomainKey } from '@core/modules/admin/admin.definitions';
import { MEDIA_CONSTANTS } from '@media/shared/constans';

export const mapDomainToBackendDomain = (domainKey: DomainKey): string => {
  switch (domainKey) {
    case 'vilaggazdasag':
      return 'vghu';
    case 'magyarNemzet':
      return 'magyar_nemzet';
    default:
      return domainKey;
  }
};

export const getAspectRatiosByDomain = (domainKey: DomainKey): Record<string, number> => {
  const supportedRatios = MEDIA_CONSTANTS.supportedRatios;
  if (domainKey === 'origo') {
    return {
      ...supportedRatios,
      '20:10': 20 / 10,
      '9:16': 9 / 16,
    };
  }
  return supportedRatios;
};
