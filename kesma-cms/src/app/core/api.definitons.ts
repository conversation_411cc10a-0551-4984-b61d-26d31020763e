import { MinuteToMinuteBlockResponse } from '../content-page-editor/definitions/minute-to-minute.definitions';
import { Resolution } from '@shared/definitions/shared.definitions';

export type DbBoolean = '0' | '1';
export type ContentInputType =
  | 'component'
  | 'wysiwyg'
  | 'text'
  | 'textarea'
  | 'select'
  | 'selectWithImage'
  | 'checkbox'
  | 'radio'
  | 'number'
  | 'date'
  | 'datePicker'
  | 'datetime'
  | 'datetimeRange'
  | 'hidden'
  | 'searchSelect'
  | 'image'
  | 'radioGroup'
  | 'checkboxGroup'
  | 'radioButton'
  | 'radiobutton'
  | 'colorPicker'
  | 'password'
  | 'collection'
  | 'map'
  | 'contributorsSelect'
  | 'eadvert'
  | 'files'
  | 'permissions'
  | 'userPermissions'
  | 'openingHours'
  | 'openingHoursSeasonal'
  | 'address'
  | 'paywall'
  | 'otherTimes'
  | 'selectHighlightedImage'
  | 'graphdata';

/**
 * Meta property of the API response.
 * NOTE: Marked as partial due to incompatibility with different implementations through the CMS.
 */
export type ContentResponseMeta = Partial<{
  characterCount: number;
  leadCharacterCount: number;
  titleCharacterCount: number;
  bodyCharacterCount: number;
  wordCount: number;
  dataCount: number;
  rowCount: number;
  charCountTitle: number;
  charCountUpTitle: number;
  charCountMainTitle: number;
  charCountPreTitle: number;
  charCountLead: number;
  charCountBody: number;
  charCountSubTitle: number;
  charCountShortLead: number;
  charCountRecommendedTitle: number;
  contributorUsers: string;
  newContributorFeatureEnabled: string;

  id: string;
  requestUrl: string;
  responseType: string;
  publicState?: 'draft' | 'published' | 'publishedAndModified';
  isActive?: boolean;
  blame: {
    createdAt: Date;
    createdBy: string;
    updatedAt: Date;
    updatedBy: string;
  };
  type: 'Article' | 'StaticPage' | 'BrandingBox' | 'Glossary';
  canEdit: boolean;
  canActivate: boolean;
  canCreatePrint: boolean;
  canCreateOnline: boolean;
  needReloadPage?: boolean;
  wasSaved: boolean;
  editLocked?: {
    date: string;
    user: string;
  };
  seoScore: number;
  publicUrl: string | null;
  flekk?: {
    flekk: number;
    flekk_price: number;
  };
  days?: any[];
}>;
export interface ContentResponse {
  data: BaseContentVariant[];
  meta: ContentResponseMeta;
}

export interface MinuteToMinuteBlocksContentResponse {
  data: MinuteToMinuteBlockResponse[];
  meta: {
    dataCount: number;
    filterable: any;
    limitable: any;
    orderable: any;
    requestUrl: string;
    responseType: string;
  };
}

export interface InputInfo {
  isHidden?: boolean;
  treeSelect?: boolean;
  typeSelect?: boolean;
  simpleSelect?: boolean;
  onlyParent?: boolean;
  createUrl?: string;
  translateSource?: boolean;
  isAvatar?: boolean;
  isStarDictionaryStar?: boolean;
  isColor?: boolean;
  isThumbnail?: boolean;
  softLimit?: number;
  addAllButton?: boolean;
  showMap?: boolean;
  formUrl?: string;
  allowClear?: boolean;
  _tempNewSourceUrl: string;
  values?: string[];
  precision?: number; // Decimális számokhoz
}

export interface BaseContentVariant {
  asserts: ComponentFieldDataAsserts;
  inputInfo: InputInfo;
  inputLabel: string;
  inputType: ContentInputType;
  key: string;
  multiple: boolean;
  readOnly?: boolean;
  value?: any;
  referenceSource?: {
    selectDisplayProperty?: string;
  };
}

export interface InputFieldContent extends BaseContentVariant {
  id: string;
  value: any;
  referenceSource?: {
    url: string;
    selectKeyProperty: string;
    selectDisplayProperty: string;
    optionalParameters: string[];
    requiredParameters: string[];
  };
  choices?: any[]; // If this is defined, the option of select is set based on this values
  dependency?: ComponentFieldDataDependency;
  depth?: number; // This is only used in the menu editor to handle different conditions!
  /*type: string;
  id: string;
  key: string;
  subComponents: IComponentData[];
  fields: IComponentFieldData[];
  tempid?: string;
  isNew?: boolean;
  isValid?: boolean;
  multiple?: boolean;
  asserts?: IComponentFieldDataAsserts;
  tabSetIndex?: number;
  tabIndex?: number;*/
}

export interface BodyComponentContent extends BaseContentVariant {
  availableComponents: AvailableComponentContent[];
  value: ComponentContent[];
}

export interface AvailableComponentContent {
  details: InputFieldContent[];
  id: string;
  notInList?: boolean;
  subComponents: any[];
  type: string;
}

export interface ComponentContent {
  details: InputFieldContent[];
  id: string;
  tempId?: string;
  subComponents: any[];
  key?: string;
  type: string;
  config?: AvailableComponentContent;
  deleted?: boolean;
  shouldOpenEditor?: boolean;
}

/*export interface IComponentFieldData {
  inputType: string;
  inputLabel: string;
  key: string;
  id: string;
  value: any;
  type?: string;
  multiple?: boolean;
  readOnly?: boolean;
  asserts?: IComponentFieldDataAsserts;
  referenceSource?: IComponentFieldDataSource;
  inputInfo?: IComponentFieldDataInputInfo[];
  dependency?: IComponentFieldDataDependency;
  properties?: any;
  errors?: IFormErrors;
  validators?: ValidatorFn[];
  valueDetails?: any;
}*/

export interface ComponentFieldDataAsserts {
  NotBlank?: {
    allowNull: boolean;
    message: string;
  };
  Length?: {
    charset: string;
    charsetMessage: string;
    exactMessage: string;
    max: number;
    maxMessage: string;
    min: number;
    minMessage: string;
  };
  Count?: {
    exactMessage: string;
    max: number;
    maxMessage: string;
    min: number;
    minMessage: string;
  };
  GreaterThanOrEqual?: {
    exactMessage: string;
    propertyPath: string;
  };
  Regex?: {
    message: string;
    pattern: string;
  };
  Email?: {
    message: string;
  };
  Url?: {
    message: string;
  };
  Range?: {
    deprecatedMaxMessageSet: boolean;
    deprecatedMinMessageSet: boolean;
    groups: string[];
    invalidDateTimeMessage: string;
    invalidMessage: string;
    max: number;
    maxMessage: string;
    maxPropertyPath: string;
    min: number;
    minMessage: string;
    minPropertyPath: string;
    notInRangeMessage: string;
    payload: string;
  };
}

export interface ComponentFieldDataSource {
  url: string;
  selectKeyProperty: string;
  selectDisplayProperty: string;
  optionalParameters: string[];
  requiredParameters: string[];
}

export interface IComponentFieldDataInputInfo {
  width: number;
  height: number;
  createUrl?: string;
}

export interface IComponentDeleteData {
  id: string;
  type: 'body' | 'widget';
}

export interface IAvailableSectionData {
  isEachRequired: boolean;
  isMaxOne: boolean;
}

export interface ILoginRequestData {
  email: string;
  password: string;
}

export interface IFormInfoDataMeta {
  responseType: string;
  dataCount: number;
  id: string;
}

export interface ComponentFieldDataDependency {
  mode: 'neighbour' | 'root';
  inputPathway: string[];
  queryParamKey: string;
  selectKeyProperty: string;
}

export interface ComponentFieldInputInfo {
  treeSelect: boolean;
  typeSelect: boolean;
  simpleSelect: boolean;
  onlyParent: boolean;
  createUrl: string;
  translateSource: boolean;
  isAvatar: boolean;
  isThumbnail: boolean;
  softLimit: number;
  addAllButton: boolean;
  showMap: boolean;
  required: boolean;
  formUrl: string;
  isColor: boolean;
  useDynamicTagger: boolean;
  isHidden?: boolean;
}

export type FormControlImage = Readonly<{
  altText: string;
  caption: string;
  createdAt: {
    date: string;
    timezone_type: number;
    timezone: string;
  };
  fileSize: number;
  fullSizeUrl: string;
  thumbnailUrl: string;
  articleSizeUrl: string;
  mimeType: string;
  photographer: string;
  source: string;
  title: string;
  variantId: number;
  resolution?: Resolution;
}>;

export type KpiContentType = 'article' | 'gallery' | 'image' | 'contentBlockImg' | 'article-network-slot' | 'voting-network-slot' | 'dossier-network-slot';

export type ColumnWithIcon = Readonly<{
  id: string;
  title: string;
  activeIconUrl?: string;
  inActiveIconUrl?: string;
  slug?: string;
}>;

export type ColumnData = Readonly<{
  id: string;
  title: string;
  slug: string;
  parentId?: string | null;
}>;
