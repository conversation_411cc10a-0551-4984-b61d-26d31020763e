import { DestroyRef, inject, Injectable } from '@angular/core';
import { ApiService } from '@core/services/api.service';
import { Observable, timer } from 'rxjs';
import { ApiListResult } from '@trendency/kesma-ui';
import { StoredUserNotification, UserNotification } from '@core/services/api/notifications/notification.definitions';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map, switchMap } from 'rxjs/operators';
import { StorageService } from '@trendency/kesma-core';
import { environment } from '../../../../../environments/environment';

const USER_NOTIFICATION_STORAGE_KEY = 'user_notifications';
const IS_POLL_SLOW_STORAGE_KEY = 'user_notifications_slow';

@Injectable({ providedIn: 'root' })
export class NotificationService {
  private readonly apiService = inject(ApiService);
  private readonly nzNotificationService = inject(NzNotificationService);
  private readonly storageService = inject(StorageService);
  private readonly destroyRef = inject(DestroyRef);

  readonly isSlowPolling: boolean = environment.type === 'local' || this.storageService.getLocalStorageData(IS_POLL_SLOW_STORAGE_KEY);

  shownNotifications: StoredUserNotification[] = [];

  getDashboardNotifications(): Observable<ApiListResult<UserNotification>> {
    return this.apiService.notifications.actualDashboard();
  }

  markNotificationSeen(id: string, source: 'instant' | 'dashboard'): Observable<void> {
    return this.apiService.notifications.notificationSeen(id, source);
  }

  initInstantNotifications(): void {
    this.shownNotifications = [];
    const storageData: StoredUserNotification[] | undefined = this.storageService.getLocalStorageData(USER_NOTIFICATION_STORAGE_KEY);

    for (const storedUserNotification of storageData ?? []) {
      // Collect not expired notifications from local storage
      if (storedUserNotification.expires > Date.now()) {
        this.shownNotifications.push(storedUserNotification);
      }
    }

    // 5 minutes if disabled:
    timer(0, this.isSlowPolling ? 300_000 : 60_000)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        switchMap(() => this.apiService.notifications.actualInstant()),
        map(({ data }) => data)
      )
      .subscribe((notifications) => {
        for (const notification of notifications) {
          this.showInstant(notification);
        }
        this.storageService.setLocalStorageData(USER_NOTIFICATION_STORAGE_KEY, this.shownNotifications);
      });
  }

  private wasShown(id: string): boolean {
    return this.shownNotifications.some((notification) => notification.data.id === id);
  }

  private showInstant({ id, type, title, message }: UserNotification): void {
    if (this.wasShown(id)) {
      return;
    }

    this.shownNotifications.push({
      data: { id, type, title, message },
      expires: Date.now() + 1_209_600, // 2 weeks
    });

    this.nzNotificationService.create(type, title, message, {
      nzKey: id,
      nzPauseOnHover: true,
      nzDuration: 0,
      nzPlacement: 'bottom',
    });
    this.markNotificationSeen(id, 'instant').subscribe();
  }
}
