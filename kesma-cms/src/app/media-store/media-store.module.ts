import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SharedModule } from 'src/app/shared/shared.module';
import { FileStoreModule } from './file-store/file-store.module';

import { IsRecommendedResolutionSizePipe } from './pipes/is-recommended-resolution-size.pipe';

import { MediaStoreComponent } from './components/media-store/media-store.component';

import {
  MediaStoreGalleryEditorViewComponent,
  MediaStoreGalleryListViewComponent,
  MediaStoreImageListViewComponent,
  MediaStoreVariantEditorViewComponent,
} from './components/views';

import { MediaStoreHeaderComponent } from './components/media-store-header/media-store-header.component';
import { MediaStoreFooterComponent } from './components/media-store-footer/media-store-footer.component';
import { MediaStoreFiltersComponent } from './components/media-store-filters/media-store-filters.component';
import { MediaStoreImageListComponent } from './components/media-store-image-list/media-store-image-list.component';
import { MediaStoreUploaderComponent } from './components/media-store-uploader/media-store-uploader.component';
import { MediaStoreImageDetailsComponent } from './components/media-store-image-details/media-store-image-details.component';
import { MediaStoreGalleryDetailsComponent } from './components/media-store-gallery-details/media-store-gallery-details.component';
import { MediaStoreFocalPointPickerComponent } from './components/media-store-focal-point-picker/media-store-focal-point-picker.component';
import { MediaStoreInputContainerComponent } from './components/media-store-input-container/media-store-input-container.component';

import { KpiStatusGalleryComponent } from './components/kpi-status-gallery/kpi-status-gallery.component';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzCarouselModule } from 'ng-zorro-antd/carousel';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MediaStoreCarouselComponent } from './components/media-store-carousel/media-store-carousel.component';
import { ImageCropperComponent } from 'ngx-image-cropper';
import { ImageLazyLoadDirective } from '@external/loading';

@NgModule({
  declarations: [
    MediaStoreComponent,
    MediaStoreImageListViewComponent,
    MediaStoreVariantEditorViewComponent,
    MediaStoreGalleryListViewComponent,
    MediaStoreGalleryEditorViewComponent,
    MediaStoreHeaderComponent,
    MediaStoreFooterComponent,
    MediaStoreFiltersComponent,
    MediaStoreImageListComponent,
    MediaStoreUploaderComponent,
    MediaStoreImageDetailsComponent,
    MediaStoreGalleryDetailsComponent,
    MediaStoreFocalPointPickerComponent,
    MediaStoreInputContainerComponent,
    IsRecommendedResolutionSizePipe,
    KpiStatusGalleryComponent,
    MediaStoreCarouselComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    NzInputModule,
    NzDatePickerModule,
    NzCheckboxModule,
    NzButtonModule,
    NzSpinModule,
    NzUploadModule,
    NzSelectModule,
    NzStepsModule,
    NzPopconfirmModule,
    NzCarouselModule,
    ReactiveFormsModule,
    NzAutocompleteModule,
    FileStoreModule,
    DragDropModule,
    ImageCropperComponent,
    ImageLazyLoadDirective,
  ],
  exports: [MediaStoreComponent, ImageCropperComponent, MediaStoreUploaderComponent],
})
export class MediaStoreModule {}
