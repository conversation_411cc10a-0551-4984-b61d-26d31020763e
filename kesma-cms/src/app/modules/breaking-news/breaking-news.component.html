<app-data-table
  [loading]="false"
  [enableSorting]="true"
  [tableData]="dataTableData"
  [tableConfig]="dataTableConfig"
  (rowActionTriggered)="handleAction($event, 'rowActions')"
  (globalActionTriggered)="handleAction($event, 'globalActions')"
  (queryParamsChanged)="refreshTableData($event)"
  [createVisible]="createVisible"
  (createVisibleChange)="createVisibleChange($event)">
</app-data-table>

<ng-template #titleTemplate let-data="data">
  {{ data?.title }}
</ng-template>

<ng-template #positionTemplate let-data="data">
  {{ "CMS." + data | translate }}
</ng-template>

<ng-template #colorTemplate let-color="data">
  @if (color) {
    <b>{{ color }}</b> <br />
    <div class="color-box" [style.background-color]="color"></div>
  }
</ng-template>
