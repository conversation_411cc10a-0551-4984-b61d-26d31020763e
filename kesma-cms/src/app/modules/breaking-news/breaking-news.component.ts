import { Component, ElementRef, OnInit, TemplateRef, viewChild } from '@angular/core';
import { BaseContentComponent } from '@modules/base-content.component';
import { IBaseContentConfig } from '@core/core.definitions';
import { DataTableModule } from '@shared/modules/data-table/data-table.module';
import { IHttpOptions } from '@external/http';
import { CustomTemplateType, IDataTableColumnInfo } from '@shared/definitions/data-table.definitions';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { Observable } from 'rxjs';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
  selector: 'app-breaking-news',
  templateUrl: './breaking-news.component.html',
  styleUrl: './breaking-news.component.scss',
  imports: [DataTableModule, TranslatePipe],
})
export class BreakingNewsComponent extends BaseContentComponent implements OnInit {
  readonly titleTemplate = viewChild<TemplateRef<ElementRef>>('titleTemplate');
  readonly positionTemplate = viewChild<TemplateRef<ElementRef>>('positionTemplate');
  readonly colorTemplate = viewChild<TemplateRef<ElementRef>>('colorTemplate');

  ngOnInit(): void {
    this.initTableDataAndConfig(this.route.snapshot.data.list);
  }

  get config(): IBaseContentConfig {
    this.formApiCall = this.getFormInfo.bind(this);
    return {
      contentType: 'breaking-news',
      contentListType: 'breaking-news',
      previewPageRoute: null,
      tableTitle: 'Rendkívüli hírek',
      editorType: 'editor',
      contentGroup: 'breaking-news',
      dataColumns: this.dataColumns,
      formApiCall: this.getFormInfo.bind(this),
    };
  }

  list(params?: IHttpOptions): Observable<object> {
    return this.apiService.breakingNews.list({ params } as IHttpOptions);
  }

  create(data: ContentData): Observable<object> {
    return this.apiService.breakingNews.create(data);
  }

  update(id: string, data: ContentData): Observable<object> {
    return this.apiService.breakingNews.update(id, data);
  }

  delete(id: string): Observable<object> {
    return this.apiService.breakingNews.delete(id);
  }

  getFormInfo(id?: string): Observable<object> {
    return this.apiService.breakingNews.getFormInfo(id);
  }

  private get dataColumns(): IDataTableColumnInfo[] {
    return [
      {
        key: 'article',
        property: 'article',
        title: 'title',
        customTemplate: this.titleTemplate(),
      },
      {
        key: 'position',
        property: 'position',
        title: 'position',
        customTemplate: this.positionTemplate(),
      },
      {
        key: 'text',
        property: 'text',
        title: 'text',
      },
      {
        key: 'backgroundColor',
        property: 'backgroundColor',
        title: 'backgroundColor',
        customTemplate: this.colorTemplate(),
      },
      {
        key: 'foregroundColor',
        property: 'foregroundColor',
        title: 'foregroundColor',
        customTemplate: this.colorTemplate(),
      },
      {
        key: 'currentlyVisible',
        property: 'currentlyVisible',
        title: 'currentlyVisible',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
        orderable: {
          queryKey: 'currentlyVisible_order',
        },
      },
      {
        key: 'visibleFrom',
        property: 'visibleFrom',
        title: 'visibleFrom',
        customTemplateType: CustomTemplateType.DATE_TIME,
      },
      {
        key: 'visibleUntil',
        property: 'visibleUntil',
        title: 'visibleUntil',
        customTemplateType: CustomTemplateType.DATE_TIME,
      },
    ];
  }
}
