import { inject } from '@angular/core';
import { ResolveFn, Router } from '@angular/router';
import { throwError } from 'rxjs';
import { ApiService } from 'src/app/core/services/api.service';
import { catchError } from 'rxjs/operators';

export const breakingNewsResolver: ResolveFn<object> = ({ queryParams }) => {
  const apiService = inject(ApiService);
  const router = inject(Router);

  return apiService.breakingNews.list({ params: queryParams }).pipe(
    catchError((err) => {
      router.navigate(['/', 'admin', 'error'], { state: { errorResponse: JSON.stringify(err) } }).then();
      return throwError(() => err);
    })
  );
};
