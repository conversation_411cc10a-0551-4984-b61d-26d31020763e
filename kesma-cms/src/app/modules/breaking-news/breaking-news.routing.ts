import { Routes } from '@angular/router';
import { breakingNewsResolver } from '@modules/breaking-news/breaking-news.resolver';
import { BreakingNewsComponent } from '@modules/breaking-news/breaking-news.component';

export const breakingNewsRouting: Routes = [
  {
    path: '',
    component: BreakingNewsComponent,
    resolve: { list: breakingNewsResolver },
    pathMatch: 'full',
  },
  {
    path: 'editor',
    loadChildren: () => import('src/app/item-editor-page/item-editor-page.module').then((m) => m.ItemEditorPageModule),
  },
];
