import { Routes } from '@angular/router';
import { CalendarsComponent } from '@modules/calendars/components/calendars/calendars.component';
import { CalendarsResolver } from '@modules/calendars/resolvers/calendars.resolver';
import { CalendarEditComponent } from '@modules/calendars/components/calendar-edit/calendar-edit.component';
import { CalendarFormResolver } from '@modules/calendars/resolvers/calendar-form.resolver';
import { CalendarDayEditComponent } from '@modules/calendars/components/calendar-day-edit/calendar-day-edit.component';
import { CalendarDayResolver } from '@modules/calendars/resolvers/calendar-day.resolver';

export const calendarsRoutes: Routes = [
  {
    path: '',
    component: CalendarsComponent,
    pathMatch: 'full',
    resolve: { list: CalendarsResolver },
    providers: [CalendarsResolver],
  },
  {
    path: 'editor/:id',
    component: CalendarEditComponent,
    pathMatch: 'full',
    resolve: {
      formInfo: CalendarFormResolver,
    },
    runGuardsAndResolvers: 'pathParamsOrQueryParamsChange',
  },
  {
    path: 'day-edit',
    component: CalendarDayEditComponent,
    pathMatch: 'full',
    resolve: {
      formInfo: CalendarDayResolver,
    },
    runGuardsAndResolvers: 'pathParamsOrQueryParamsChange',
  },
  {
    path: 'day-edit/:id',
    component: CalendarDayEditComponent,
    pathMatch: 'full',
    resolve: {
      formInfo: CalendarDayResolver,
    },
    runGuardsAndResolvers: 'pathParamsOrQueryParamsChange',
  },
];
