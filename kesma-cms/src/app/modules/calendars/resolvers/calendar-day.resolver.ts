import { inject, Injectable } from '@angular/core';
import { ApiService } from '@core/services/api.service';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class CalendarDayResolver {
  private readonly apiService = inject(ApiService);
  private readonly router = inject(Router);

  resolve(route: ActivatedRouteSnapshot): Observable<any> | Promise<any> | any {
    const id = route.params?.id ?? null;

    if (id) {
      return this.apiService.secretDaysCalendarDay.get(id).pipe(
        catchError((err) => {
          this.router.navigate(['/', 'admin', 'error'], { state: { errorResponse: JSON.stringify(err) } }).then();
          return throwError(() => err);
        })
      );
    } else {
      return this.apiService.secretDaysCalendarDay.create('GET').pipe(
        catchError((err) => {
          this.router.navigate(['/', 'admin', 'error'], { state: { errorResponse: JSON.stringify(err) } }).then();
          return throwError(() => err);
        })
      );
    }
  }
}
