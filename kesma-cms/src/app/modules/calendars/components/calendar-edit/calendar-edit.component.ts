import { Component, inject, OnInit, signal, ViewChild } from '@angular/core';
import { BasicItemEditorModule } from '../../../../item-editor-page/component/basic-item-editor/basic-item-editor.module';
import { DataTableModule } from '@shared/modules/data-table/data-table.module';
import { FormGeneratorComponent } from '@shared/modules/form-generator/form-generator.component';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { SharedService } from '@shared/services/shared.service';
import { FormGenerator2Module } from '@shared/modules/form-generator2/form-generator2.module';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { ApiService } from '@core/services/api.service';
import { SharedModule } from '@shared/shared.module';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CalendarDaysService } from '@modules/calendars/services/calendar-days.service';
import { CalendarPreviewComponent } from '@modules/calendars/components/calendar-preview/calendar-preview.component';
import { CalendarPreview } from '@modules/calendars/components/calendar-preview/calendar-preview.definitions';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-calendar-edit',
  imports: [BasicItemEditorModule, DataTableModule, FormGenerator2Module, NzButtonComponent, SharedModule, RouterLink, CalendarPreviewComponent, FormsModule],
  templateUrl: './calendar-edit.component.html',
  styleUrl: './calendar-edit.component.scss',
})
export class CalendarEditComponent implements OnInit {
  @ViewChild('formTemplate') fromTemplate: FormGeneratorComponent;

  formInfo = signal<ContentData>(null);
  loading = signal<boolean>(false);
  tableDays = signal<any[]>([]);
  calendarPreview = signal<CalendarPreview | null>(null);
  previewMode = signal<'desktop' | 'mobile'>('desktop');

  protected readonly route = inject(ActivatedRoute);
  protected readonly router = inject(Router);
  protected readonly sharedService = inject(SharedService);
  protected readonly apiService = inject(ApiService);
  protected readonly modal = inject(NzModalService);
  protected readonly calendarDaysService = inject(CalendarDaysService);

  ngOnInit(): void {
    this.formInfo.set(this.route.snapshot.data.formInfo);
    this.createCalendarPreview();
    if (this.calendarDaysService.parentId() === this.formInfo().meta?.id) {
      this.tableDays.set([...this.formInfo().meta.days, ...this.calendarDaysService.calendarDaysMeta()]);
      this.formInfo.set({
        ...this.formInfo(),
        meta: { ...this.formInfo().meta, days: [...this.formInfo().meta.days, ...this.calendarDaysService.calendarDaysMeta()] },
      });
    } else {
      this.calendarDaysService.reset();
      this.tableDays.set(this.formInfo().meta.days);
    }
  }

  onFormControlChanged(formInfo: ContentData): void {
    this.formInfo.set(formInfo);
  }

  saveData(id: string): Observable<any> {
    return this.apiService.secretDaysCalendar.update(id, this.formInfo());
  }

  fetchUpdatedDaysData(id: string): Observable<any> {
    return this.apiService.secretDaysCalendar.get(id);
  }

  onDayEdit(id: string): void {
    this.router.navigate(['/', 'admin', 'calendars', 'day-edit', id], { queryParams: { parent: this.formInfo().meta?.id } });
  }

  onAddDay(): void {
    this.router.navigate(['/', 'admin', 'calendars', 'day-edit'], { queryParams: { parent: this.formInfo().meta?.id } });
  }

  onDayDelete(id: string): void {
    this.tableDays.set(this.tableDays().filter((d) => d.id !== id));
    const filteredFormDays = this.formInfo()
      .data.find((item) => item.key === 'days')
      .value.filter((day) => day.id !== id);
    this.calendarDaysService.calendarDaysForm.set(this.calendarDaysService.calendarDaysForm().filter((d) => d.id !== id));
    this.formInfo.set({
      ...this.formInfo(),
      data: this.formInfo().data.map((item) => (item.key === 'days' ? { ...item, value: filteredFormDays } : item)),
    });
  }

  saveDataAndUpdateDays(): void {
    this.loading.set(true);
    const updatedFormInfo = {
      ...this.formInfo(),
      data: this.formInfo().data.map((item) =>
        item.key === 'days' && this.calendarDaysService.calendarDaysForm().length > 0
          ? { ...item, value: [...item.value, ...this.calendarDaysService.calendarDaysForm()] }
          : item
      ),
    };
    this.formInfo.set(updatedFormInfo);
    const id = this.formInfo()?.meta?.id;
    this.saveData(id)
      .pipe(switchMap(() => this.fetchUpdatedDaysData(id)))
      .subscribe(
        (data) => {
          this.formInfo.set(data);
          this.loading.set(false);
          this.sharedService.showNotification('success', 'Kalendárium módosítása sikeres.');
          this.createCalendarPreview();
          this.calendarDaysService.reset();
        },
        (error) => {
          this.sharedService.showNotification('error', 'Kalendárium módosítása sikertelen.');
          console.error(error);
          this.loading.set(false);
        }
      );
  }

  createCalendarPreview(): void {
    const calendarData = Object.fromEntries(this.formInfo().data.map(({ key, value }) => [key, value]));
    const mappedCalendar = this.calendarDaysService.mapCalendarToPreview(calendarData);
    this.calendarPreview.set(mappedCalendar);
  }
}
