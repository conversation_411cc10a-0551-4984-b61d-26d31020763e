  <div class="header">
    <h3>Nap {{ this.id() ? "szerkesztése" : "hozzáadása" }}</h3>
    <div class="header-buttons">
      <button nz-button nzType="default" (click)="navigateBack()">Vissza</button>
      <button nz-button nzType="primary" (click)="onSave()" [nzLoading]="loading()"><PERSON><PERSON><PERSON></button>
    </div>
  </div>
  <app-form-generator-basic
    #formTemplate
    (autosave)="onFormControlChanged($event)"
    [formControls]="formInfo() | fromContentDataToFormControls | calendarDayFormControlsMap"
    [originalContentData]="formInfo()"
  ></app-form-generator-basic>

  <app-form-generator-body-single
    *ngIf="bodyContentData()?.data?.length"
    [contentData]="bodyContentData()"
    (autosave)="onBodyValueChange($event)"
  >
  </app-form-generator-body-single>
