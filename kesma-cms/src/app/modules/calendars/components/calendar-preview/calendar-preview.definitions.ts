export type CalendarPreview = {
  backgroundImage?: string;
  colorOfDays?: string;
  days?: CalendarPreviewDay[];
  id?: string;
  name?: string;
  defaultDayImageBeforeOpen?: string;
  defaultDayImageAfterOpen?: string;
  sponsorshipForHeader?: any;
  sponsorshipForFooter?: any;
  sponsorshipLeft?: any;
};

export type CalendarPreviewDay = {
  backgroundAfterOpenImage?: string;
  backgroundBeforeOpenImage?: string;
  name?: string;
  backgroundImageUrl?: string;
};
