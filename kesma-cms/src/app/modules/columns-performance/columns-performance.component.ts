import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { NzModalService } from 'ng-zorro-antd/modal';
import { ApiService } from 'src/app/core/services/api.service';
import { BaseContentComponent } from '../base-content.component';
import { IBaseContentConfig } from 'src/app/core/core.definitions';
import { IDataTableColumnInfo } from 'src/app/shared/definitions/data-table.definitions';
import { SharedService } from 'src/app/shared/services/shared.service';
import { ContentPreviewService } from 'src/app/shared/services/content-preview.service';
import { UtilsService } from '@external/utils';
import { IHttpOptions } from '@external/http';
import { ColumnsPerformanceService } from './columns-performance.service';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { AuthorsSearchType } from '../../core/services/performance/base.performance.definitions';
import { tap } from 'rxjs/operators';
import { FormGeneratorComponent } from '@shared/modules/form-generator2/components/form-generator/form-generator.component';
import { validateFormDateRange } from '@shared/utils/performance-statistics-generic-params';

@Component({
  selector: 'app-columns-performance',
  templateUrl: 'columns-performance.component.html',
  styleUrls: ['columns-performance.component.scss'],
  standalone: false,
})
export class ColumnsPerformanceComponent extends BaseContentComponent implements OnInit {
  @ViewChild('formGenerator') private formGenerator: FormGeneratorComponent;
  @ViewChild('pageViewsAvgTemplate') private pageViewsAvgTemplate: TemplateRef<ElementRef>;
  @ViewChild('numberOfArticlesTemplate') private numberOfArticlesTemplate: TemplateRef<ElementRef>;
  @ViewChild('customPageViewsAvgTemplate') private customPageViewsAvgTemplate: TemplateRef<ElementRef>;

  public dataLoading = false;
  public isSearchButtonDisabled = true;
  public formInfo: ContentData;
  public searchType: AuthorsSearchType = AuthorsSearchType.OWN_MATERIAL;

  private isInited = false;

  constructor(
    public apiService: ApiService,
    public route: ActivatedRoute,
    public translate: TranslateService,
    public router: Router,
    public modalService: NzModalService,
    public sharedService: SharedService,
    public contentPreviewService: ContentPreviewService,
    public utilsService: UtilsService,
    public columnsPerformance: ColumnsPerformanceService
  ) {
    super(apiService, route, translate, router, modalService, sharedService, contentPreviewService, utilsService);
  }

  ngOnInit() {
    this.getFormInfo();
  }

  get config(): IBaseContentConfig {
    return {
      contentType: 'columns-performance',
      contentListType: 'columns-performances',
      previewPageRoute: [],
      tableTitle: null,
      contentGroup: 'columns-performance',
      editorType: 'content-page-editor',
      dataColumns: this.getDataColumns(),
    };
  }

  list(options: IHttpOptions) {
    this.dataLoading = true;
    const searchParams = this.columnsPerformance.getFormState(this.formGenerator.formControls);
    return this.columnsPerformance.getStatistics(this.searchType, searchParams, options).pipe(tap(() => (this.dataLoading = false)));
  }

  create(data: any) {
    return this.apiService.createContentGroup(this.config.contentType, data);
  }

  update(id: string, data: any) {
    return this.apiService.updateContentGroup(this.config.contentType, id, data);
  }

  delete(id: string) {
    return this.apiService.deleteContentGroup(this.config.contentType, id);
  }

  restore(id: string) {
    return this.apiService.restoreContentGroup(this.config.contentType, id);
  }

  onSearch(): void {
    const searchParams = this.columnsPerformance.getFormState(this.formGenerator.formControls);

    if (!validateFormDateRange(searchParams.dateFrom, searchParams.dateUntil)) {
      this.modalService.error({
        nzTitle: this.translate.instant('CMS.user-statistics.DATE_RANGE_ERROR_TITLE'),
        nzContent: this.translate.instant('CMS.user-statistics.DATE_RANGE_ERROR_MESSAGE'),
      });
      return;
    }
    this.dataLoading = true;
    const currentData = this.columnsPerformance.getCacheData(this.searchType)?.data;

    if (!this.columnsPerformance.isDateRangeValid(searchParams)) {
      this.dataLoading = false;
      return;
    }

    if (!this.columnsPerformance.checkNeedToUpdate() && !!currentData) {
      this.initTableDataAndConfig(this.columnsPerformance.getCacheData(this.searchType));
      this.forceUpdateTableParams(this.columnsPerformance.cachedMeta[this.searchType]);
    } else {
      this.columnsPerformance.getStatistics(this.searchType, searchParams).subscribe((data) => {
        this.columnsPerformance.setCacheData(this.searchType, data);
        this.initTableDataAndConfig(this.columnsPerformance.getCacheData(this.searchType));
        this.forceUpdateTableParams(this.columnsPerformance.cachedMeta[this.searchType]);
      });
    }
    this.dataLoading = false;
  }

  getFormInfo() {
    this.columnsPerformance.getFormInfo().subscribe((formInfo: ContentData) => {
      this.formInfo = formInfo;
      this.isInited = true;
    });
  }

  onSelectedTabIndexChange(tabIndex: number) {
    switch (tabIndex) {
      case 0:
        this.searchType = AuthorsSearchType.OWN_MATERIAL;
        break;
      case 1:
        this.searchType = AuthorsSearchType.NEWS_AGENCY_MATERIAL;
        break;
      case 2:
        this.searchType = AuthorsSearchType.COLUMN_MATERIAL;
        break;
      case 3:
        this.searchType = AuthorsSearchType.ALL_MATERIALS;
        break;
    }
    this.onSearch();
  }

  onFormControlStatesChanged(): void {
    if (!this.isInited) {
      return;
    }
    const { dateFrom, dateUntil } = this.columnsPerformance.getFormState(this.formGenerator.formControls);
    this.isSearchButtonDisabled = !dateFrom || !dateUntil;
  }

  private getDataColumns(): IDataTableColumnInfo[] {
    return [
      {
        key: 'title',
        property: 'title',
        title: 'title',
      },
      {
        key: 'pageViews',
        property: 'pageViews',
        title: 'pageViews',
      },
      {
        key: 'seoScoreAvg',
        property: 'seoScoreAvg',
        title: 'SeoScore.SeoScore',
      },
      {
        key: 'numberOfArticles',
        property: 'numberOfArticles',
        title: 'numberOfArticles',
        customTemplate: this.numberOfArticlesTemplate,
      },
      {
        key: 'pageViewsAvg',
        property: 'pageViewsAvg',
        title: 'pageViewsAvg',
        customTemplate: this.pageViewsAvgTemplate,
      },
    ];
  }
}
