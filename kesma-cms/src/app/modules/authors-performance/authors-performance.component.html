<nz-page-header nzBackIcon [nzGhost]="false">
  <nz-page-header-title>{{ 'CMS.authors_performance' | translate }}</nz-page-header-title>
  <nz-page-header-extra>
    <nz-space>
      <button *nzSpaceItem (click)="authorsPerformance.resetCacheData(); onSearch()" [disabled]="isSearchButtonDisabled" nz-button nzType="primary">
        {{ 'CMS.search' | translate | uppercase }}
      </button>
    </nz-space>
  </nz-page-header-extra>
</nz-page-header>

<app-form-generator-basic
  class="form"
  #formGenerator
  [formControls]="formInfo | fromContentDataToFormControls"
  (formControlsChanged)="onFormControlStatesChanged()"
  [originalContentData]="formInfo"
>
</app-form-generator-basic>

<ng-container *ngIf="dataTableData && dataTableConfig">
  <nz-tabset [nzTabBarExtraContent]="extraFunctionTemplate" (nzSelectedIndexChange)="onSelectedTabIndexChange($event)">
    <nz-tab [nzTitle]="'CMS.author_performance_own_material' | translate | uppercase" [nzForceRender]="true"> </nz-tab>
    <nz-tab [nzTitle]="'CMS.author_performance_news_agency_material' | translate | uppercase" [nzForceRender]="true"> </nz-tab>
    <nz-tab [nzTitle]="'CMS.materials.column_material' | translate | uppercase" [nzForceRender]="true"> </nz-tab>
    <nz-tab [nzTitle]="'CMS.author_performance_all_materials' | translate | uppercase" [nzForceRender]="true"> </nz-tab>
    <nz-tab [nzTitle]="'tourGuide' | translate | uppercase" [nzForceRender]="true"> </nz-tab>
  </nz-tabset>

  <app-data-table
    [enableSearch]="false"
    [enableActions]="false"
    [firstColumnStick]="true"
    [lastColumnStick]="false"
    [enableManuallyPagination]="true"
    [loading]="dataLoading"
    [tableData]="dataTableData"
    [tableConfig]="dataTableConfig"
    (rowActionTriggered)="handleAction($event, 'rowActions')"
    (globalActionTriggered)="handleAction($event, 'globalActions')"
    (queryParamsChanged)="refreshTableData($event)"
  >
  </app-data-table>
</ng-container>

<ng-template #extraFunctionTemplate>
  <button nz-button nzType="primary" (click)="authorsPerformance.exportToExcel(searchType, formGenerator.formControls, dataTableData)">
    {{ 'CMS.exportXmlsx' | translate }}
  </button>
</ng-template>

<ng-template #pageViewsAvgTemplate let-data="data" let-rowData="rowData">
  {{ rowData?.pageViews }} / {{ rowData?.numberOfArticles?.numberOfArticles }} = {{ data | number: '1.2-2' }}
</ng-template>

<ng-template #numberOfArticlesTemplate let-rowData="rowData">
  {{ rowData?.numberOfArticles?.numberOfArticles }} ({{ rowData?.numberOfArticles?.numberOfAllArticles }})
</ng-template>

<ng-template #customPageViewsAvgTemplate let-data="data" let-rowData="rowData">
  {{ rowData?.customPageViews }} / {{ rowData?.numberOfArticles?.numberOfArticles }} = {{ data | number: '1.2-2' }}
</ng-template>

<ng-template #numberOfHoursWorkedTemplate let-rowData="rowData">
  <nz-input-number [nzMin]="1" [(ngModel)]="rowData.numberOfHoursWorked"></nz-input-number>
</ng-template>

<ng-template #authorPerformanceTemplate let-rowData="rowData">
  <span *ngIf="rowData?.numberOfHoursWorked; else defaultText">
    {{ rowData.numberOfHoursWorked }} / {{ rowData?.numberOfArticles?.numberOfArticles }} =
    {{ (rowData.numberOfHoursWorked / rowData?.numberOfArticles?.numberOfArticles | number: '1.2-2') || 0 }}
  </span>
  <ng-template #defaultText>
    {{ 'CMS.workedHoursAndOnlinePerPrintArticlesDefaultText' | translate }}
  </ng-template>
</ng-template>
