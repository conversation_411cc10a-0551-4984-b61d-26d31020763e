import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { NzModalService } from 'ng-zorro-antd/modal';
import { ApiService } from 'src/app/core/services/api.service';
import { BaseContentComponent } from '../base-content.component';
import { IBaseContentConfig } from 'src/app/core/core.definitions';
import { IDataTableColumnInfo } from 'src/app/shared/definitions/data-table.definitions';
import { SharedService } from 'src/app/shared/services/shared.service';
import { ContentPreviewService } from 'src/app/shared/services/content-preview.service';
import { UtilsService } from '@external/utils';
import { AuthorsPerformanceService } from './authors-performance.service';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { IHttpOptions } from '@external/http';
import { AuthorsSearchType } from 'src/app/core/services/performance/base.performance.definitions';
import { tap } from 'rxjs/operators';
import { FormGeneratorComponent } from '@shared/modules/form-generator2/components/form-generator/form-generator.component';
import { validateFormDateRange } from '@shared/utils/performance-statistics-generic-params';

@Component({
  selector: 'app-authors-performance',
  templateUrl: 'authors-performance.component.html',
  styleUrls: ['./authors-performance.component.scss'],
  standalone: false,
})
export class AuthorsPerformanceComponent extends BaseContentComponent implements OnInit {
  @ViewChild('formGenerator') private formGenerator: FormGeneratorComponent;
  @ViewChild('pageViewsAvgTemplate') private pageViewsAvgTemplate: TemplateRef<ElementRef>;
  @ViewChild('numberOfArticlesTemplate') private numberOfArticlesTemplate: TemplateRef<ElementRef>;
  @ViewChild('customPageViewsAvgTemplate') private customPageViewsAvgTemplate: TemplateRef<ElementRef>;
  @ViewChild('numberOfHoursWorkedTemplate') private numberOfHoursWorkedTemplate: TemplateRef<ElementRef>;
  @ViewChild('authorPerformanceTemplate') private authorPerformanceTemplate: TemplateRef<ElementRef>;

  public dataLoading = false;
  public isSearchButtonDisabled = true;
  public formInfo: ContentData;
  public searchType: AuthorsSearchType = AuthorsSearchType.OWN_MATERIAL;

  private isInited = false;

  constructor(
    public apiService: ApiService,
    public route: ActivatedRoute,
    public translate: TranslateService,
    public router: Router,
    public modalService: NzModalService,
    public sharedService: SharedService,
    public contentPreviewService: ContentPreviewService,
    public utilsService: UtilsService,
    public authorsPerformance: AuthorsPerformanceService
  ) {
    super(apiService, route, translate, router, modalService, sharedService, contentPreviewService, utilsService);
  }

  ngOnInit() {
    this.getFormInfo();
  }

  get config(): IBaseContentConfig {
    this.formApiCall = this.getFormInfo.bind(this);
    return {
      contentType: 'authors-performance',
      contentListType: 'authors-performances',
      previewPageRoute: [],
      tableTitle: '',
      contentGroup: 'authors-performance',
      editorType: 'content-page-editor',
      dataColumns: this.getDataColumns(),
      formApiCall: this.getFormInfo.bind(this),
    };
  }

  list(params: IHttpOptions) {
    this.dataLoading = true;
    const searchParams = this.authorsPerformance.getFormState(this.formGenerator.formControls);
    return this.authorsPerformance.getStatistics(this.searchType, searchParams, params).pipe(tap(() => (this.dataLoading = false)));
  }

  create(data: any) {
    return this.apiService.createContentGroup(this.config.contentType, data);
  }

  update(id: string, data: any) {
    return this.apiService.updateContentGroup(this.config.contentType, id, data);
  }

  delete(id: string) {
    return this.apiService.deleteContentGroup(this.config.contentType, id);
  }

  restore(id: string) {
    return this.apiService.restoreContentGroup(this.config.contentType, id);
  }

  onSearch(): void {
    const searchParams = this.authorsPerformance.getFormState(this.formGenerator.formControls);

    if (!validateFormDateRange(searchParams.dateFrom, searchParams.dateUntil)) {
      this.modalService.error({
        nzTitle: this.translate.instant('CMS.user-statistics.DATE_RANGE_ERROR_TITLE'),
        nzContent: this.translate.instant('CMS.user-statistics.DATE_RANGE_ERROR_MESSAGE'),
      });
      return;
    }

    this.dataLoading = true;
    const currentData = this.authorsPerformance.getCacheData(this.searchType)?.data;

    if (!this.authorsPerformance.isDateRangeValid(searchParams)) {
      this.dataLoading = false;
      return;
    }

    if (!this.authorsPerformance.checkNeedToUpdate() && !!currentData) {
      this.initTableDataAndConfig(this.authorsPerformance.getCacheData(this.searchType));
      this.forceUpdateTableParams(this.authorsPerformance.cachedMeta[this.searchType]);
    } else {
      this.authorsPerformance.getStatistics(this.searchType, searchParams).subscribe((data) => {
        this.authorsPerformance.setCacheData(this.searchType, this.setCustomDataTableConfig(data));
        this.initTableDataAndConfig(this.authorsPerformance.getCacheData(this.searchType));
        this.forceUpdateTableParams(this.authorsPerformance.cachedMeta[this.searchType]);
      });
    }
    this.dataLoading = false;
  }

  onSelectedTabIndexChange(tabIndex: number) {
    switch (tabIndex) {
      case 0:
        this.searchType = AuthorsSearchType.OWN_MATERIAL;
        break;
      case 1:
        this.searchType = AuthorsSearchType.NEWS_AGENCY_MATERIAL;
        break;
      case 2:
        this.searchType = AuthorsSearchType.COLUMN_MATERIAL;
        break;
      case 3:
        this.searchType = AuthorsSearchType.ALL_MATERIALS;
        break;
      case 4:
        this.searchType = AuthorsSearchType.NEWS_AGENCY_BY_TOUR_GUIDE;
        break;
    }
    this.onSearch();
  }

  getFormInfo() {
    this.authorsPerformance.getFormInfo().subscribe((formInfo: ContentData) => {
      this.formInfo = formInfo;
      this.isInited = true;
    });
  }

  onFormControlStatesChanged(): void {
    if (!this.isInited) {
      return;
    }
    const { dateFrom, dateUntil } = this.authorsPerformance.getFormState(this.formGenerator.formControls);
    this.isSearchButtonDisabled = !dateFrom || !dateUntil;
  }

  private setCustomDataTableConfig(tableData: any) {
    // Set custom fields to datatable config.
    return {
      ...{
        ...tableData,
        data: tableData.data?.map((object) => ({
          ...object,
          numberOfHoursWorked: null,
        })),
      },
    };
  }

  private getDataColumns(): IDataTableColumnInfo[] {
    return [
      {
        key: 'contributorFullName',
        property: 'contributorFullName',
        title: 'contributorFullName',
      },
      {
        key: 'pageViews',
        property: 'pageViews',
        title: 'pageViews',
      },
      {
        key: 'numberOfArticles',
        property: 'numberOfArticles',
        title: 'numberOfArticles',
        customTemplate: this.numberOfArticlesTemplate,
      },
      {
        key: 'seoScoreAvg',
        property: 'seoScoreAvg',
        title: 'SeoScore.SeoScore',
      },
      {
        key: 'pageViewsAvg',
        property: 'pageViewsAvg',
        title: 'pageViewsAvg',
        customTemplate: this.pageViewsAvgTemplate,
      },
      {
        key: 'numberOfHoursWorked',
        property: 'numberOfHoursWorked',
        title: 'numberOfHoursWorked',
        customTemplate: this.numberOfHoursWorkedTemplate,
      },
      {
        key: 'authorPerformance',
        property: 'authorPerformance',
        title: 'authorPerformance',
        customTemplate: this.authorPerformanceTemplate,
      },
    ];
  }
}
