@use 'shared' as *;

:host {
  display: block;

  &.article {
    &-LayoutStyle {
      width: 100%;

      .article-header {
        &-title {
          @extend %article-header-title;
        }

        &-block-category {
          @extend .article-header-block-category;
        }

        &-social-container {
          @extend %social-container;
          border-top: 1.5px solid var(--kui-white);
          margin: 32px 0;
        }

        &-author-container {
          @extend %author-container;
        }

        &-author {
          @extend %author;
        }

        &-lead {
          @extend %lead;
          color: var(--kui-white);
          letter-spacing: 1px;
        }
      }
    }

    &-ArticleStyle {
      .article-header {
        &-title {
          @extend %article-header-title;
        }

        &-title-wrapper {
          display: flex;
          flex-direction: row;
          align-items: flex-end;
          min-height: 450px;
          padding: 48px;
          background-size: cover;
          background-blend-mode: multiply;
          background-color: rgba(4, 11, 17, 0.85);
          background-repeat: no-repeat;
          background-position: bottom;

          @include media-breakpoint-down(sm) {
            min-height: 200px;
            padding: 24px 15px;
            margin: 0 -15px;
          }
        }

        &-social-container {
          @extend %social-container;
          padding-bottom: 32px;
        }

        &-author-container {
          @extend %author-container;
        }

        &-author {
          @extend %author;
        }

        &-publish-date {
          margin-top: 6px;
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0.84px;

          @include media-breakpoint-down(sm) {
            display: none;
          }
        }

        &-lead {
          @extend %lead;
          color: var(--kui-black);
        }
      }
    }
  }
}

%article-header-title {
  font-size: 32px;
  font-weight: 600;
  line-height: 140%;
  font-family: var(--kui-font-secondary);
  color: var(--kui-white);

  @include media-breakpoint-down(sm) {
    font-size: 22px;
    font-weight: 500;
  }
}

%social-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
}

%lead {
  font-weight: 700;
  font-family: var(--kui-font-primary);
  font-size: 14px;
  line-height: 170%;
  letter-spacing: 0.84px;

  @include media-breakpoint-down(sm) {
    font-weight: 400;
    font-size: 16px;
    line-height: 150%;
    letter-spacing: 0.96px;
  }
}

%author {
  color: var(--kui-orange-600);
  font-weight: 700;
  font-family: var(--kui-font-primary);
  font-size: 14px;
  letter-spacing: 0.84px;

  @include media-breakpoint-down(sm) {
    font-size: 12px;
  }
}

%author-container {
  display: flex;
  flex-direction: column;
}
