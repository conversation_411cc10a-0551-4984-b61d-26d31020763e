@use 'shared' as *;

:host {
  width: 100%;
  background-color: var(--kui-blue-800);
  display: flex;
  flex-direction: column;
  padding: 32px 16px;
  color: var(--kui-white);

  @include media-breakpoint-up(md) {
    width: 100vw;
    margin-left: calc((100vw - min(1272px, 100vw - 32px)) / -2);
  }

  .header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-wrap: nowrap;
    margin: 0 auto 32px;
    width: min(100%, 503px);
    max-width: 1056px;

    @include media-breakpoint-up(md) {
      width: calc(100% - 32px);
      flex-direction: row;
      gap: 24px;
    }

    &-title {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: nowrap;

      &-text {
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
        letter-spacing: 0.24px;
      }

      &-line {
        height: 1px;
        width: 100%;
        background-color: var(--kui-white);
      }
    }

    &-source {
      white-space: nowrap;

      a {
        color: var(--kui-white);
        border-bottom: 1px solid var(--kui-white);
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 1056px;
    margin: auto;
    width: min(100%, 503px);
    gap: 56px;

    @include media-breakpoint-up(md) {
      width: calc(100% - 32px);
    }

    @include media-breakpoint-up(xl) {
      gap: 24px;
      width: calc(100% - 32px);
      flex-direction: row;
    }

    &-panel {
      &-input {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 24px;

        &-label {
          color: var(--kui-blue-200);
        }

        ng-select {
          &.ng-select-opened::ng-deep .ng-arrow-wrapper {
            transform: rotate(180deg);
            margin-top: -5px;
          }

          &::ng-deep {
            .ng-value-container {
              padding: 10px 16px;
            }

            .ng-arrow-wrapper {
              background-image: url('/assets/images/icons/chevron-down-white.svg');
              background-repeat: no-repeat;
              background-size: 20px;
              margin: 5px 8px 0 0;
              transition: transform 0.2s ease-in-out;

              .ng-arrow {
                border-style: none;
                border-width: 0;
              }
            }

            .ng-select-container {
              background-color: var(--kui-blue-800);
              border: 1px solid var(--kui-blue-600);
              border-radius: 2px;
            }

            .ng-value-label {
              color: var(--kui-white);
            }
          }
        }
      }

      &-data {
        border-radius: 4px;
        border: 1px solid var(--kui-blue-600);

        & > div {
          display: flex;
          padding: 16px 32px;
          gap: 16px;
          column-gap: 24px;
        }

        &-skyview {
          flex-direction: column;
          @include media-breakpoint-up(md) {
            flex-direction: row;
            align-items: center;
          }

          &-text {
            color: var(--kui-white);
            font-size: 24px;
            font-weight: 700;
            line-height: 32px;
            letter-spacing: 0.24px;
            width: 100%;
          }

          &-sun {
            display: flex;
            flex-wrap: nowrap;
            gap: 16px;
            width: 100%;

            & > kesma-icon {
              width: 32px;
              color: #ff9902;
            }

            &-time {
              font-size: 20px;
              font-weight: 500;
              line-height: 32px;
              white-space: nowrap;

              kesma-icon {
                display: inline-block;
                margin-left: 5px;
                width: 20px;
                height: 20px;
              }
            }
          }
        }

        &-temp {
          flex-direction: column;
          background-color: var(--kui-blue-600);
          justify-content: space-between;

          @include media-breakpoint-up(md) {
            flex-direction: row;
            align-items: center;
          }

          &-current {
            display: flex;
            align-items: center;
            gap: 16px;

            kesma-icon {
              width: 88px;
            }

            &-text {
              font-size: 64px;
              font-weight: 500;
              letter-spacing: 0.64px;
            }
          }

          &-minmax {
            display: flex;
            gap: 32px;

            span {
              font-size: 20px;
              font-weight: 500;
              white-space: nowrap;

              kesma-icon {
                display: inline-block;
                margin-left: 5px;
                width: 20px;
                height: 20px;
              }
            }

            & > kesma-icon {
              color: #ff9902;
              width: 32px;
            }

            &-max {
              color: #ff9902;
            }
          }
        }

        &-misc {
          flex-wrap: wrap;

          &-item {
            display: flex;
            flex-direction: column;
            gap: 2px;

            &-label {
              font-size: 14px;
              line-height: 32px;
              letter-spacing: 0.14px;
              color: var(--kui-blue-200);
            }

            &-value {
              font-size: 16px;
              font-weight: 500;
              line-height: 32px;
              letter-spacing: 0.16px;

              &.uv {
                text-transform: capitalize;
              }
            }
          }
        }
      }
    }

    &-map {
      &-container {
        position: relative;
        width: 100%;
        min-width: 325px;
        max-width: 516px;
        height: fit-content;

        & > img {
          color: var(--kui-blue-600);
          width: 100%;
          object-fit: contain;
        }
      }

      &-layer {
        position: absolute;

        &-box {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 6px;
          border-radius: 8px;
          border: 1px solid var(--kui-blue-700);
          background: rgba(20, 61, 93, 0.95);
          padding: 6px 8px;
          width: 76px;
          height: 78px;

          kesma-icon {
            color: var(--kui-white);
          }
        }

        &-divider {
          width: 100%;
          height: 1px;
          background-color: var(--kui-blue-200);
        }

        &-text {
          display: flex;
          gap: 8px;
        }

        &-max {
          color: #ff9902;
        }

        &-min {
          color: var(--kui-blue-200);
        }

        &.Győr {
          top: 1%;
          left: 13%;

          @include media-breakpoint-up(sm) {
            top: 4%;
            left: 12%;
          }
        }
        &.Miskolc {
          right: 8%;
          top: -16%;

          @include media-breakpoint-up(sm) {
            right: 17%;
            top: -2%;
          }
        }
        &.Debrecen {
          right: -1%;
          top: 36%;

          @include media-breakpoint-up(sm) {
            right: 13%;
            top: 33%;
          }
        }
        &.Pécs {
          left: 25%;
          bottom: -10%;
          @include media-breakpoint-up(sm) {
            left: 27%;
            bottom: 10%;
          }
        }
        &.Szeged {
          right: 26%;
          bottom: -3%;

          @include media-breakpoint-up(sm) {
            right: 30%;
            bottom: 13%;
          }
        }
        &.Zalaegerszeg {
          left: -3px;
          top: 46%;

          @include media-breakpoint-up(sm) {
            left: 8%;
            top: 40%;
          }
        }
        &.Budapest {
          left: 40%;
          top: 5%;

          @include media-breakpoint-up(sm) {
            top: 15%;
          }
        }
      }

      &-city {
        font-size: 14px;
        line-height: 18px;
        text-shadow: 0 0 16px rgba(15, 23, 42, 0.45);
        color: var(--kui-white);
        text-align: center;
      }
    }
  }
}
