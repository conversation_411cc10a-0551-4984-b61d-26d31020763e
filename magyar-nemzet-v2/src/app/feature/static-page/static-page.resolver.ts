import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { ApiService, StaticPageService } from '../../shared';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult } from '@trendency/kesma-ui';

@Injectable({ providedIn: 'root' })
export class StaticPageResolver {
  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> | Promise<any> | any {
    const previewHash = route.queryParams['previewHash'];
    const param = route.params['slug'];
    const request$ = previewHash
      ? this.staticPageService.getStaticPagePreview(param, previewHash)
      : (param ? this.apiService.getArticlesByFoundationTag(param) : of({})).pipe(
          switchMap((searchResult: ApiResult<ArticleSearchResult[], ApiResponseMetaList> | any) => {
            if (param && (searchResult?.data ?? []).length === 0) {
              return this.staticPageService.getStaticPage(param).pipe(
                catchError((error) => {
                  this.router
                    .navigate(['/', '404'], {
                      state: { errorResponse: JSON.stringify(error) },
                      skipLocationChange: true,
                    })
                    .then();
                  return throwError(() => error);
                })
              );
            }
            return of(searchResult);
          })
        );
    return request$.pipe(
      catchError((error) => {
        this.router
          .navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(error) },
            skipLocationChange: true,
          })
          .then();
        return throwError(() => error);
      })
    );
  }
}
