@use 'shared' as *;

:host {
  app-breadcrumb {
    margin-top: 16px;
  }
}
.wrapper {
  @include media-breakpoint-down(sm) {
    width: calc(100% - #{$mobile-side-padding * 2}) !important;
  }
}
.static-page {
  margin-top: 25px;
  padding-bottom: 45px;

  @include media-breakpoint-down(md) {
    margin: 25px 16px 0px;
  }

  &-title {
    font-size: 60px;
    font-weight: 700;
    line-height: 74px;
    font-family: var(--kui-font-secondary);
    margin-bottom: 40px;
    word-wrap: break-word;

    @include media-breakpoint-down(xs) {
      font-size: 30px;
      font-weight: 700;
      line-height: 40px;
    }
  }
}
