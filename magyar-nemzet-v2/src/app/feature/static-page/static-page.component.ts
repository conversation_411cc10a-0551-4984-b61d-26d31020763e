import { AfterViewInit, Component, Host<PERSON>inding, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EmbeddingService, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  ApiResponseStaticPageMeta,
  ApiResult,
  CustomStaticPageType,
  LayoutApiData,
  PortalConfigSetting,
  StaticPage,
  WysiwygMain,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { BreadcrumbComponent, defaultMetaInfo, PortalConfigService } from '../../shared';
import { OlimpiaService } from 'src/app/shared/services/olimpia.service';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgFor, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';
import { ArticleTextComponent } from 'src/app/shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { CpacComponent } from 'src/app/shared/components/cpac/cpac.component';
import { DEFAULT_BREADCRUMB_ITEM, makeBreadcrumbSchema } from 'src/app/shared/utils/breadcrumb.utils';

const OLIMPIA_SLUG = 'olimpia-2024';

@Component({
  selector: 'app-static-page',
  templateUrl: './static-page.component.html',
  styleUrls: ['./static-page.component.scss'],
  imports: [LayoutComponent, NgIf, NgFor, ArticleTextComponent, SidebarComponent, CpacComponent, NgSwitch, NgSwitchCase, BreadcrumbComponent],
})
export class StaticPageComponent implements OnInit, AfterViewInit, OnDestroy {
  public title: string;
  public slug: string;
  public staticPageResponse: StaticPage;
  public body: WysiwygMain[];
  public customStaticPageType: CustomStaticPageType;
  public layoutApiData: LayoutApiData;
  public adPageType = 'all_articles_and_sub_pages';
  isOlimpiaCustomPage = false;

  readonly CustomStaticPageType = CustomStaticPageType;

  @HostBinding('class.eb-2024')
  get isEb2024(): boolean {
    return this.slug === 'labdarugo-eb-2024';
  }

  @HostBinding('class.olympics-2024')
  get isOlympics2024(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS) && this.slug === 'olimpia-2024';
  }

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly embedding: EmbeddingService,
    private readonly analyticsService: AnalyticsService,
    private readonly portalConfigService: PortalConfigService,
    private readonly olimpiaService: OlimpiaService,
    private readonly schemaService: SchemaOrgService
  ) {}

  ngOnInit(): void {
    this.route.data.subscribe((res) => {
      const { data, meta } = res['staticPageData'] as ApiResult<StaticPage | LayoutApiData, ApiResponseStaticPageMeta>;
      this.customStaticPageType = meta.customStaticPageType;
      if (!this.customStaticPageType || this.customStaticPageType === CustomStaticPageType.StaticPage) {
        this.staticPageResponse = data as StaticPage;
        this.title = this.staticPageResponse.title;
        this.slug = this.staticPageResponse?.slug;
        this.body = this.staticPageResponse.body as unknown as WysiwygMain[];
        this.analyticsService.sendPageView(undefined, 'Statikus oldal');
      }

      if (this.customStaticPageType === CustomStaticPageType.CustomPage) {
        const { customBuiltPageTitle, customBuiltPageSlug } = meta;
        this.title = customBuiltPageTitle;
        this.slug = customBuiltPageSlug;
        this.layoutApiData = data as LayoutApiData;
        this.isOlimpiaCustomPage = meta?.['customBuiltPageSlug'] === OLIMPIA_SLUG;
        this.olimpiaService.setIsOlimpiaMainOrArticlePage(this.isOlimpiaCustomPage);

        this.analyticsService.sendPageView(undefined, 'Egyedi oldal');
      }

      const canonical = createCanonicalUrlForPageablePage(this.slug);
      if (canonical) {
        this.seo.updateCanonicalUrl(canonical);
      }
      const ogImageEB = this.slug === 'labdarugo-eb-2024' ? `${this.seo.hostUrl}/assets/images/eb/MNO-EB-OG-image-1200x630.jpg` : undefined;
      const title = `${this.title} | Magyar Nemzet`;
      const ebMetaDescription =
        'Labdarúgó EB 2024: A legfrissebb hírek a Magyar Nemzet oldalán. ' + 'A Foci EB menetrendje, részletes bemutató a csoportokról éskiesőkről.';

      this.seo.setMetaData({
        ...defaultMetaInfo,
        title: title,
        description: this.isEb2024 ? ebMetaDescription : defaultMetaInfo.description,
        ogTitle: this.title,
        ogImage: ogImageEB,
        ogDescription: this.isEb2024 ? ebMetaDescription : defaultMetaInfo.ogDescription,
        twitterDescription: this.isEb2024 ? ebMetaDescription : defaultMetaInfo.ogDescription,
      });
    });
    this.makeBreadCrumbSchema();
  }

  private makeBreadCrumbSchema(): void {
    const breadcrumbSchema = makeBreadcrumbSchema([DEFAULT_BREADCRUMB_ITEM, { label: this.title }]);
    this.schemaService.removeStructuredData();
    this.schemaService.insertSchema(breadcrumbSchema);
  }

  ngOnDestroy(): void {
    this.olimpiaService.setIsOlimpiaMainOrArticlePage(false);
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }
}
