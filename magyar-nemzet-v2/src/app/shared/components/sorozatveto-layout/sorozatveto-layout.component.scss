@use 'shared' as *;

:host {
  display: block;
  background-size: cover;

  &:hover {
    background-size: cover;
  }

  &.style-DEFAULT {
    width: 100%;
    height: unset;

    .card {
      &-background {
        &-overlay {
          gap: 36px;

          @include media-breakpoint-down(sm) {
            gap: 27px;
          }
        }
      }
    }
  }

  &.style-WIDE {
    @include media-breakpoint-up(md) {
      width: 100%;

      .card {
        &-remaining-authors {
          display: none;
        }

        &-title {
          max-width: 100%;
          min-width: auto;
        }

        &-bottom {
          flex-grow: 0;
          flex-shrink: 0;
          min-width: 184px;
          gap: 8px;
        }
      }
    }
  }

  @include media-breakpoint-down(sm) {
    .card-title {
      height: auto;
      max-height: unset;
    }
  }
}

.card {
  &-title {
    display: -webkit-box;
    line-clamp: 3;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow-y: hidden;
    vertical-align: text-bottom;
    margin-bottom: 0;
  }

  &-top,
  &-bottom {
    flex: 1;
  }

  &-top {
    max-height: 236px;
  }

  &-remaining-authors {
    font-size: 12px;
    line-height: 16px;
    text-align: right;
    color: var(--kui-blue-50);
    max-width: 76px;
    align-self: flex-end;
    margin-left: auto;
  }

  &-bottom {
    gap: 2px;
    margin-bottom: 4px;
    flex-wrap: wrap;

    mno-opinion-author {
      margin-bottom: 0;
      min-width: 38px;
    }

    .d-flex {
      gap: 8px;
      flex: 1;
    }
  }
}
