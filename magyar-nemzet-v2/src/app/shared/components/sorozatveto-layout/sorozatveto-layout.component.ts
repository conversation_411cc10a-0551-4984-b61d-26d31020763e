import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BlogRecommendationComponent } from '../blog-recommendation/blog-recommendation.component';
import { ArticleCard, ArticleReviewBody, Author, SorozatvetoArticleCard, SorozatvetoOpinionCard } from '@trendency/kesma-ui';
import { uniqBy } from 'lodash-es';
import { Observable } from 'rxjs';
import { IconComponent } from '../icon/icon.component';
import { RouterLink } from '@angular/router';

const MAX_DISPLAYED_OPINIONERS = 3;

@Component({
  selector: 'mno-sorozatveto-layout',
  templateUrl: './sorozatveto-layout.component.html',
  styleUrls: ['../blog-recommendation/blog-recommendation.component.scss', './sorozatveto-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class SorozatvetoLayoutComponent extends BlogRecommendationComponent<SorozatvetoArticleCard> {
  @Input() set opinionCards(cards: SorozatvetoOpinionCard<ArticleReviewBody[]>[]) {
    this.#opinionCards = cards;
    this.setProperties();
  }

  get opinionCards(): SorozatvetoOpinionCard<ArticleReviewBody[]>[] {
    return this.#opinionCards;
  }

  @Input() set updateObserver(value: Observable<SorozatvetoOpinionCard<ArticleReviewBody[]>[]>) {
    this.#updateObserver = value;
    this.#updateObserver.subscribe((reviews) => (this.opinionCards = reviews));
  }

  authors: Author[] = [];
  opinioners: Author[] = [];

  remainingAuthors = 0;

  #opinionCards: SorozatvetoOpinionCard<ArticleReviewBody[]>[] = [];
  #updateObserver?: Observable<SorozatvetoOpinionCard<ArticleReviewBody[]>[]>;

  protected override setProperties(): void {
    super.setProperties();
    this.opinioners = uniqBy(this.opinionCards.map(({ author }) => author) || [], 'name');
    this.authors = (this.opinioners ?? []).slice(0, MAX_DISPLAYED_OPINIONERS);
    this.remainingAuthors = (this.opinioners?.length ?? 0) - this.authors.length;
    this.bg = `
      linear-gradient(0deg, rgba(15, 23, 42, 0.80) 0%, rgba(15, 23, 42, 0.80) 100%),
      url(${(this.data as unknown as ArticleCard)?.thumbnail?.url})
    `;
  }
}
