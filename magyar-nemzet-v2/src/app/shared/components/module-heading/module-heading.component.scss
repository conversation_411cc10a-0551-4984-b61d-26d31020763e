@use 'shared' as *;

$maxWidth: 1056px;

:host {
  display: flex;
  flex-direction: column;
  background: var(--kui-group-bg-color);
  margin: 24px auto;
  padding: 24px;
  @include media-breakpoint-down(sm) {
    width: calc(100% + $mobile-side-padding);
    // margin-inline: -$mobile-side-padding;
    padding-top: 0;
    margin-top: 0;
    .result-bar,
    .top-container {
      border-left: 0;
      border-right: 0;
      padding-inline: 0;
      width: calc(100% + $mobile-side-padding);
      margin-inline: -$mobile-side-padding;
    }
  }

  &.white {
    --kui-group-border-color: var(--kui-slate-950);
    --kui-group-fg-color: var(--kui-slate-950);
    --kui-group-bg-color: transparent;
    --kui-group-hover-color: var(--kui-blue-700);
    --kui-group-label-color: var(--kui-blue-900);
  }

  &.black {
    --kui-group-border-color: var(--kui-slate-50);
    --kui-group-fg-color: var(--kui-slate-50);
    --kui-group-bg-color: var(--kui-slate-900);
    --kui-group-label-color: var(--kui-white);

    [mno-article-card] {
      border-bottom: 1px dotted var(--kui-slate-50);
    }
  }

  &.gray {
    --kui-group-border-color: var(--kui-slate-50);
    --kui-group-fg-color: var(--kui-slate-50);
    --kui-group-bg-color: var(--kui-slate-700);
    --kui-group-label-color: var(--kui-white);

    [mno-article-card] {
      border-bottom: 1px solid var(--kui-slate-50);
    }
  }

  &.is-opinion {
    .result-bar {
      h1 {
        color: var(--kui-blue-900);
      }

      .icon {
        margin-left: 16px;
      }
    }
  }

  &.blue {
    --kui-group-border-color: var(--kui-slate-50);
    --kui-group-fg-color: var(--kui-slate-50);
    --kui-group-bg-color: var(--kui-slate-800);
    --kui-group-hover-color: var(--kui-slate-300);
    --kui-group-label-color: var(--kui-white);
  }

  [mno-article-card]::ng-deep {
    .article {
      &-label {
        color: var(--kui-group-label-color);
      }

      &-title,
      &-lead {
        color: var(--kui-group-fg-color);
      }

      &-link-wrapper {
        &:hover {
          .article {
            &-title,
            &-lead {
              color: var(--kui-group-hover-color);
            }
          }
        }
      }
    }
  }
}

.result-bar {
  height: 28px;
  width: 100%;
  max-width: $maxWidth;

  margin: 0 auto;
  border-left: 1px solid var(--kui-group-border-color);
  border-right: 1px solid var(--kui-group-border-color);
  display: flex;
  align-items: center;
  justify-content: stretch;

  h1 {
    flex-shrink: 1;
    flex-grow: 0;
    margin: 0 16px;
    color: var(--kui-group-fg-color);
  }

  hr {
    flex: 1;
    border-bottom: 0;
    border-color: var(--kui-group-border-color);
  }

  &.full {
    width: 100%;
  }

  &.with-sidebar {
    max-width: 873px;
  }

  @include media-breakpoint-down(xs) {
    width: 100%;
    min-width: auto;
  }
}

.top-container {
  width: 100%;
  max-width: $maxWidth;
  margin: 0 auto;

  gap: 24px;
  border-left: 1px solid var(--kui-group-border-color);
  border-right: 1px solid var(--kui-group-border-color);
  padding: 24px;

  [mno-article-card] {
    flex: 1;
  }
}
