<ng-select
  [ngModel]="selectedItem$ | async"
  notFoundText="Nincs találat"
  loadingText="Kérlek várj..."
  clearAllText="Törlés"
  typeToSearchText="Keresés..."
  [placeholder]="placeholder"
  [ariaLabel]="placeholder"
  [searchable]="true"
  [clearable]="true"
  [loading]="(isLoading$ | async) || false"
  [bindLabel]="bindLabel"
  [virtualScroll]="true"
  [multiple]="false"
  [bindValue]="bindValue"
  [items]="items$ | async"
  [typeahead]="$any(isBackendSearch ? typeahead$ : undefined)"
  (scrollToEnd)="loadMore()"
  (click)="handleClick()"
  (ngModelChange)="selectionChanged($event)"
>
</ng-select>
