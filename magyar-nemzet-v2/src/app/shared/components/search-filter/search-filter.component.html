<div class="search-by-keyword">
  <div class="search-by-keyword-input-wrapper">
    <input (keydown.enter)="onSearch()" [(ngModel)]="searchFilter['global_filter']" class="search-by-keyword-input" placeholder="Ezt keresem" type="text" />
    <i (keydown)="onClearSearch()" (click)="onClearSearch()" [showLabel]="true" [size]="24" class="close-icon" label="Törlés" mno-icon="dismiss-circle"></i>
  </div>
  <button (click)="onSearch()" class="search-submit btn btn-ghost btn-ghost-dark-transparent" type="submit">
    <span class="desktop">Keresés</span>
    <img [size]="24" alt="Keresés ikon" class="mobile" mno-icon="search" />
  </button>
</div>

<div class="search-filters">
  <div *ngIf="showPublishDateFilter" class="search-filters-item">
    <ng-select
      (change)="searchFilter['from_date'] = $event.value; onSearch()"
      [(ngModel)]="selectedPublishDate"
      [clearable]="false"
      [items]="publishFilters"
      [searchable]="false"
      [ariaLabel]="'Dátum tartomány'"
    >
    </ng-select>
  </div>

  <div *ngIf="showContentTypeFilter" class="search-filters-item">
    <ng-select
      (change)="searchFilter['content_types[]'] = $event.value; onSearch()"
      [(ngModel)]="selectedArticleType"
      [clearable]="false"
      [items]="articleTypeFilters"
      [searchable]="false"
    >
    </ng-select>
  </div>

  <div *ngIf="showColumnFilter" class="search-filters-item">
    <app-search-filter-api-select
      (ngModelChange)="searchFilter['columnSlugs[]'] = $event; onSearch()"
      [(ngModel)]="selectedColumnSlug"
      [isBackendSearch]="true"
      [singleItemRequest]="columnSingleItemRequest"
      [sourceRequest]="columnSourceRequest"
      placeholder="Rovat"
    ></app-search-filter-api-select>
  </div>

  <div *ngIf="showAuthorFilter" class="search-filters-item">
    <app-search-filter-api-select
      (ngModelChange)="searchFilter['author'] = $event; onSearch()"
      [(ngModel)]="selectedAuthorSlug"
      [bindLabel]="'public_author_name'"
      [singleItemRequest]="authorSingleItemRequest"
      [sourceRequest]="authorsSourceRequest"
      placeholder="Szerző"
    ></app-search-filter-api-select>
  </div>

  <ng-container *ngIf="showOnlyInTagsFilter">
    <button (click)="onSearchOnlyInTags()" [class.active]="searchOnlyInTags" class="search-filters-item tag-filter">Csak címkében</button>
  </ng-container>

  <div *ngIf="showPublishDateSort" class="search-filters-item">
    <ng-select
      (change)="searchFilter['publishDate_order[]'] = $event.value; onSearch()"
      [(ngModel)]="selectedSortOption"
      [clearable]="false"
      [items]="sortOptions"
      [searchable]="false"
    >
    </ng-select>
  </div>
</div>
