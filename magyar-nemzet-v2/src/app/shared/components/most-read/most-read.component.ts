import { ChangeDetectionStrategy, Component, HostBinding, inject, OnInit, signal } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, IconComponent } from '@trendency/kesma-ui';
import { NgFor, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { parseISO, getHours, getMinutes } from 'date-fns';
import { ApiService } from '../../services';
import { take } from 'rxjs';
import { UtilService } from '@trendency/kesma-core';

@Component({
  selector: 'mno-most-read',
  templateUrl: './most-read.component.html',
  styleUrls: ['./most-read.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, NgFor, RouterLink, IconComponent],
})
export class MostReadComponent extends BaseComponent<ArticleCard[]> implements OnInit {
  private readonly api = inject(ApiService);
  private readonly utils = inject(UtilService);
  @HostBinding('class') hostClass = '';
  protected readonly activeTabIndex = signal(0);
  latestArticles = signal<ArticleCard[]>([]);

  protected readonly buildArticleUrl = buildArticleUrl;
  protected readonly getPublishTime = (dateString: string): string => {
    const date = parseISO(dateString.replace(' ', 'T') + 'Z');

    const hours = getHours(date);
    const minutes = getMinutes(date);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  protected changeTab(index: number): void {
    this.activeTabIndex.set(index);
  }

  override ngOnInit(): void {
    super.ngOnInit();
    if (!this.utils.isBrowser()) {
      return;
    }
    this.api
      .getLatestArticles(this.data?.length || 5)
      .pipe(take(1))
      .subscribe(({ data }) => {
        this.latestArticles.set(data);
      });
  }
}
