@use 'shared' as *;

:host {
  display: block;
  max-width: 100%;

  .tabs {
    display: flex;
    button {
      font-family: var(--kui-font-roboto-flex);
      padding: 0 12px;
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      letter-spacing: 0.3px;
      color: var(--kui-blue-900);
      background-color: #f1f8fe;
      width: 100%;
      min-height: 60px;
      border: 1px solid transparent;
      border-bottom: 1px solid var(--kui-blue-900);

      &.active {
        background-color: var(--kui-white);
        border-top: 1px solid var(--kui-blue-900);
        border-right: 1px solid var(--kui-blue-900);
        border-left: 1px solid var(--kui-blue-900);
        border-bottom: none;
      }
    }
  }
  .content {
  }
  .most-read,
  .fresh-news {
    border-left: 1px solid var(--kui-blue-900);
    padding-top: 24px;
    &-item {
      color: var(--kui-blue-900);
      display: flex;
      gap: 12px;
      padding-right: 6px;
      padding-bottom: 12px;
      font-size: 17px;
      font-weight: 700;
      line-height: 21px; /* 123.529% */
      border-radius: 2px;
      border-right: 1px solid var(--kui-blue-900);
      &:not(:last-child) {
        border-bottom: 1px solid var(--kui-blue-900);
      }
      @include transition;

      &:hover {
        background-color: var(--kui-blue-900);
        color: var(--kui-white);
        .most-read-item-serial {
          border-color: var(--kui-white);
        }
      }
    }
  }
  .most-read {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-left: 16px;
    &-item {
      &-serial {
        width: 35px;
        min-width: 35px;
        max-width: 35px;
        height: 35px;
        min-height: 35px;
        max-height: 35px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        border: 1px solid var(--kui-blue-900);
        font-family: var(--kui-font-libre);
        font-size: 28px;
      }
      &:hover {
        .most-read-item-serial {
          color: #f1f8fe;
        }
      }
    }
  }
  .fresh-news {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-left: 16px;
    border-left: 1px solid var(--kui-blue-900);
    min-height: 300px;

    &-item {
      &-timestamp {
        color: #738196;
      }
      &:hover {
        .fresh-news-item-timestamp {
          color: #cbd5e1;
        }
      }
    }
  }
  .read-more {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px;
    border-radius: 2px;
    border: 1px solid var(--kui-primary);
    font-family: var(--kui-font-roboto-flex);
    color: var(--kui-primary);
  }
}
