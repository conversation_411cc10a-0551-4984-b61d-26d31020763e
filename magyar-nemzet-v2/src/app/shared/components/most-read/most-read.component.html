<div class="tabs">
  <button [class.active]="activeTabIndex() === 0" (click)="changeTab(0)"><PERSON>iss hírek</button>
  <button [class.active]="activeTabIndex() === 1" (click)="changeTab(1)">Legolvasottabb</button>
</div>
<div class="content">
  @if (activeTabIndex() === 0) {
    <div class="fresh-news">
      <ng-container *ngFor="let item of latestArticles(); let i = index">
        <ng-container *ngTemplateOutlet="freshNewsItem; context: { item, i }"></ng-container>
      </ng-container>
    </div>
    <a routerLink="/friss-hirek" class="read-more">
      Összes legfrissebb hír
      <kesma-icon name="chevron-right-dark" [size]="20" />
    </a>
  }
  @if (activeTabIndex() === 1) {
    <div class="most-read">
      <ng-container *ngFor="let item of data; let i = index">
        <ng-container *ngTemplateOutlet="mostViewedItem; context: { item, i }"></ng-container>
      </ng-container>
    </div>
    <a routerLink="/legolvasottabb-hirek" class="read-more">
      Összes legolvasottabb hír
      <kesma-icon name="chevron-right-dark" [size]="20" />
    </a>
  }
</div>

<ng-template #mostViewedItem let-item="item" let-i="i">
  @let url = buildArticleUrl(item);

  <a [routerLink]="url" class="most-read-item">
    <div class="most-read-item-serial">{{ i + 1 }}</div>
    {{ item.title }}
  </a>
</ng-template>

<ng-template #freshNewsItem let-item="item" let-i="i">
  @let url = buildArticleUrl(item);
  @let publishTime = getPublishTime(item.publishDate);

  <a [routerLink]="url" class="fresh-news-item">
    <div class="fresh-news-item-timestamp">{{ publishTime }}</div>
    {{ item.title }}
  </a>
</ng-template>
