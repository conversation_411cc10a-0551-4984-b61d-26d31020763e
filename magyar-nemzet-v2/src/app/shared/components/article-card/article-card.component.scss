@use 'shared' as *;

:host {
  display: block;

  &.in-layout {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px dotted var(--kui-slate-300);

    @include media-breakpoint-down(sm) {
      padding-bottom: 10px;
      margin-bottom: 0 !important;
    }

    &:hover {
      border-bottom-color: var(--kui-blue-700);
    }
  }

  &:hover {
    .article-content-counter {
      .icon {
        @include transition;
        border-color: var(--kui-white);
      }
    }

    .article-thumbnail {
      transform: scale(110%);
      @include transition;
    }
  }
}

div.article-link-wrapper {
  .article-title,
  p {
    color: var(--kui-slate-950);
  }
}

.article {
  &-link-wrapper {
    display: block;
    font-weight: 500;
  }

  &-thumbnail-figure {
    margin-bottom: 4px;
    background-image: url('/assets/images/magyar-nemzet.png');
    position: relative;
    overflow: hidden;

    .article-content-counter {
      @include transition;
      position: absolute;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background: var(--kui-slate-950-o80);
      color: var(--kui-white);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      padding: 4px 8px 4px 4px;

      .icon {
        @include transition;
        border: 1px solid transparent;
        border-radius: 50%;
        padding: 2px;
      }
    }
  }

  &-sponsor {
    margin-top: -8px;
    padding: 8px 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    background-color: var(--kui-blue-500);
    position: relative;

    &,
    a &,
    a:hover &,
    a:active &,
    a:focus & {
      color: var(--kui-white);
    }

    span {
      display: inline-block;
      padding: 0 16px;
    }

    span:first-child {
      border-right: 1px solid var(--kui-blue-50);
    }
  }

  &-thumbnail {
    object-fit: cover;
    width: 100%;
    border-radius: 2px;
    transform: scale(100%);
    @include transition;
  }

  &-labels {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    flex-wrap: wrap;

    @include media-breakpoint-down(sm) {
      margin-bottom: 6px;
    }

    .icon {
      margin-right: 8px;
    }
  }

  &-label {
    @include transition;
    color: var(--kui-blue-900);
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
  }

  &-title {
    margin-bottom: 12px;
    @include transition;

    @include media-breakpoint-down(xs) {
      font-size: 20px !important;
      font-style: normal;
      font-weight: 700;
      line-height: 26px;
      margin-bottom: 6px;
    }
  }

  &-lead {
    margin-bottom: 8px;
    @include transition;

    @include media-breakpoint-down(sm) {
      margin-bottom: 6px;
    }
  }

  &-badge-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 4px;

    &[data-icon-size='16'] {
      gap: 2px;
    }
  }
}

:host {
  &.is-live {
    .article-content-counter {
      width: 100%;
      padding: 8px 16px;
      font-size: 16px;
      font-weight: 700;
      line-height: 21px; /* 131.25% */
      text-align: left;
      justify-content: flex-start;
      border-radius: 2px;
      border: 1px solid var(--kui-slate-500);
      background-color: var(--kui-slate-900-o75);
    }
  }

  &.is-minute-by-minute {
    .article-link-wrapper,
    .article-link-wrapper:hover,
    .article-link-wrapper:focus,
    .article-link-wrapper:active {
      .article-content-counter {
        border: 1px solid var(--kui-blue-500);
        color: 1px solid var(--kui-blue-500);
        background-color: var(--kui-slate-900-o80);
      }
    }
  }
}

mno-opinion-author {
  margin-top: 8px;
}

:host {
  &.as-gallery,
  &.as-podcast,
  &.as-video {
    margin-bottom: 0;

    .article {
      &-title,
      &-lead,
      &-label {
        color: var(--kui-slate-50);
      }

      &-link-wrapper:hover {
        .article-title {
          color: var(--kui-blue-500);
        }
      }
    }
  }

  &.style-ImgRightTagsTitleBadgeSmall.as-video {
    .article {
      &-thumbnail {
        &-figure {
          max-width: 25%;
        }
      }

      &-title {
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.015em;
        color: var(--kui-white);
      }

      &-label {
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        margin-bottom: 12px;
      }
    }
  }

  &.style-Gallery {
    .article {
      &-link-wrapper,
      &-thumbnail,
      &-thumbnail-figure {
        height: 100%;
      }
    }

    .article-content-container {
      background-color: var(--kui-slate-900-o50);
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 24px 8px 8px;

      .article-title {
        font-family: var(--kui-font-secondary);
        font-size: 24px;
        font-weight: 700;
        line-height: 30px;
        color: var(--kui-slate-50);
      }

      .article-lead {
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        color: var(--kui-slate-50);
      }
    }

    .article-thumbnail {
      &,
      &-figure {
        padding-bottom: 0;
        margin-bottom: 0;
      }
    }

    .article-content-counter {
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
    }
  }

  &.style-Img4TopTitleLeadBadgeLarge,
  &.style-Img1TopTagsTitleLeadBadge,
  &.style-Img16TopTagsTitleLeadLarge {
    .article {
      &-title {
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0.01em;
      }
    }
  }

  &.style-Img16TopTagsTitleLeadLargeBorder,
  &.style-Img4TopTagsTitleLeadSmallBorder {
    padding: 0 16px 24px;

    @include media-breakpoint-down(sm) {
      padding: 0 16px 10px 24px;
    }

    &,
    &.in-layout {
      border-right: 1px solid var(--kui-blue-900);
      border-bottom: 1px solid var(--kui-blue-900);
      border-left: 1px solid var(--kui-blue-900);

      @include media-breakpoint-down(xs) {
        border-width: 0 0 1px 0;
        border-bottom: 1px solid var(--kui-blue-900);
        padding-inline: 0;
        padding-bottom: 10px;
      }
    }

    @include transition;

    &:hover {
      @include transition;
      border-color: var(--kui-blue-700);
      border-width: 0 2px 2px;
      padding: 0 15px 23px;
      @include media-breakpoint-down(sm) {
        border-width: 0 0 1px 0;
        border-bottom: 1px solid var(--kui-blue-900);
        padding-inline: 0;
        padding-bottom: 10px;
      }
    }

    .article {
      &-title {
        font-size: 36px;
        line-height: 44px;
        letter-spacing: normal;
      }
    }
  }

  &.style-Img16TopTagsTitleLeadLargeBorder {
    @include media-breakpoint-down(sm) {
      .article {
        &-title {
          font-size: 24px;
          line-height: 30px; /* 125% */
        }

        &-lead {
          font-size: 20px;
          font-weight: 400;
          line-height: 24px; /* 120% */
        }
      }
    }
  }

  &.style-Img4TopTagsTitleLeadSmallBorder {
    border-left: 0 !important;
    padding-left: 0 !important;

    &:hover {
      padding-left: 0 !important;
      border-left: 0 !important;
    }

    .article {
      &-title {
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
        letter-spacing: 0;
      }

      &-lead {
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
      }

      &-label {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
      }
    }
  }

  &.style-Img4TopTitleLeadBadgeLarge,
  &.style-Img1TopTagsTitleLeadBadge,
  &.style-ImgRightTagsTitleLeadWide,
  &.style-ImgRightTagsTitleLeadWideBorder,
  &.style-DateImgRightTagsTitleLeadWide {
    .article {
      &-badge-container {
        display: inline-flex;
        vertical-align: text-bottom;
      }
    }
  }

  &.style-ImgRightTagsTitleLeadWideBorder {
    border-right: 1px solid var(--kui-blue-900);
    border-bottom: 1px solid var(--kui-blue-900);
    padding-right: 8px;

    .article {
      &-title {
        margin-bottom: 4px;
      }

      &-thumbnail-figure {
        margin-bottom: 8px;
      }
    }
  }

  &.style-Img1TopTagsTitleLeadBadge,
  &.style-Img4TopTitleLeadBadgeLarge {
    .article {
      &-thumbnail-figure {
        margin-bottom: 24px;
      }

      &-content-counter {
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        padding: 4px 16px 4px 8px;
        gap: 8px;
      }

      &-label:first-child {
        padding-left: 0;
      }
    }

    &.as-branding-box-ex {
      .article {
        &-title {
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
          line-height: 21px;
        }

        &-lead {
          display: none;
        }

        &-thumbnail-figure {
          margin-bottom: 8px;
        }
      }
    }
  }

  &.style-Podcast {
    .article {
      &-link-wrapper {
        color: var(--kui-white);
        display: flex;
        justify-content: space-between;
        gap: 8px;
      }

      &-label {
        color: var(--kui-white);
      }

      &-title {
        font-size: 14px !important;
        line-height: 18px !important;
        margin-bottom: 0;
      }

      &-thumbnail-figure {
        width: 110px;
        height: 62px;
        flex-shrink: 0;
        background-image: none;
      }

      &-thumbnail {
        height: 100%;
      }

      &-content-counter {
        font-weight: 400;
        padding: 0 4px 0 0;

        i {
          width: 20px;
          height: 20px;
          min-width: 20px;
        }
      }
    }
  }

  &.style-ImgRightTagsTitleLeadBadge,
  &.style-NoImgBorderLeftTagsTitleLeadBadge,
  &.style-NoImgBorderAllTagsTitleLeadBadge,
  &.style-NoImgBorderBottomRightDateTitleBadge,
  &.style-RelatedArticle,
  &.style-ImgRightTagsTitleBadgeSmall,
  &.style-ImgRightTagsTitleLeadWide,
  &.style-ImgRightTagsTitleLeadWideBorder,
  &.style-DateImgRightTagsTitleLeadWide,
  &.style-ImgRightTagsTitleLead50,
  &.style-NoImgNoBorderAllTagsTitleLeadBadge {
    .article-link-wrapper > .d-flex {
      gap: 8px;
    }

    .article {
      &-title {
        font-size: 20px;
        line-height: 26px;
        letter-spacing: 0.015em;
      }

      &-thumbnail-figure {
        max-width: 36%;
      }

      &-labels {
        margin-bottom: 4px;
      }
    }
  }

  &.style-ImgRightTagsTitleLeadWide,
  &.style-ImgRightTagsTitleLeadWideBorder,
  &.style-DateImgRightTagsTitleLeadWide {
    .article {
      &-thumbnail-figure {
        max-width: 148px;
      }
    }

    &.as-program {
      padding: 8px 8px 8px 24px;
    }
  }

  &.style-ImgRightTagsTitleBadgeSmall {
    .article-link-wrapper > div {
      gap: 16px;
    }

    .article {
      &-thumbnail-figure {
        max-width: 84px;
      }

      &-label {
        font-size: 12px;
        line-height: 16px;
      }

      &-title {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
      }
    }
  }

  &.style-ImgRightTagsTitleLead50 {
    .article-container {
      justify-content: space-between;
    }

    .article {
      &-thumbnail-figure {
        flex-basis: 50%;
        max-width: 50%;
        align-self: flex-start;
        flex-shrink: 0;
      }
    }

    .article-content-counter {
      @include transition;

      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 4px;
      background: var(--kui-slate-950-o80);
      color: var(--kui-slate-50);
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      padding: 8px;
      width: 100%;
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
    }

    &.as-program {
      border-radius: 2px;
      margin-bottom: 0;

      .article {
        &-container {
          padding-top: 8px;
          justify-content: space-between;

          & > div {
            padding: 0 0 0 8px;
          }
        }

        &-title {
          color: var(--kui-slate-950);
          font-size: 16px;
          font-weight: 700;
          line-height: 21px;
        }

        &-lead {
          color: var(--kui-slate-950);
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
        }

        &-thumbnail {
          &-figure {
            width: calc(50% - 16px);
            min-width: calc(50% - 16px);
            max-width: calc(50% - 16px);
          }
        }

        &-label {
          font-size: 14px;
          font-weight: 500;
          line-height: 18px;
        }
      }
    }
  }

  &.style-ExternalRecommendation {
    min-height: 260px;
    border-bottom: 1px solid var(--kui-slate-950) !important;

    .article {
      &-label {
        color: var(--kui-blue-800);
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        text-transform: capitalize;
      }

      &-lead {
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
      }
    }
  }

  &.style-NoImgBorderLeftTagsTitleLeadBadge,
  &.style-NoImgBorderAllTagsTitleLeadBadge,
  &.style-NoImgBorderBottomRightDateTitleBadge,
  &.style-RelatedArticle,
  &.style-NoImgNoBorderAllTagsTitleLeadBadge {
    padding: 15px;

    @include media-breakpoint-down(sm) {
      padding: 10px;
    }

    &:hover {
      background-color: var(--kui-blue-950);

      .article {
        &-label {
          @include transition;
          color: var(--kui-blue-500);
        }

        &-lead,
        &-title {
          @include transition;
          color: var(--kui-white);
        }
      }
    }
  }

  &.style-NoImgBorderLeftTagsTitleLeadBadge {
    border-left: 8px solid var(--kui-blue-900);
  }

  &.style-NoImgBorderAllTagsTitleLeadBadge {
    border: 1px solid var(--kui-blue-900) !important;
    border-top: 0 !important;
    border-radius: 2px;
  }

  &.style-NoImgBorderBottomRightDateTitleBadge,
  &.style-RelatedArticle {
    border: 1px solid var(--kui-blue-900) !important;
    border-top: 0 !important;
    border-left: 0 !important;
    border-radius: 2px;
    padding: 7px 16px 16px 7px;

    @include media-breakpoint-down(sm) {
      padding: 7px 16px 10px 7px;
    }

    .article {
      &-date {
        padding: 4px 7px;
        border-radius: 2px;
        border: 1px solid var(--kui-blue-100);
        font-size: 16px;
        font-weight: 800;
        line-height: 22px;
        letter-spacing: 0em;
        text-align: left;
        color: var(--kui-blue-900);
        align-self: flex-start;
        margin-bottom: 8px;
      }

      &-title {
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        letter-spacing: 0;
        color: var(--kui-blue-900);
      }
    }

    &:hover {
      .article {
        &-date {
          @include transition;
          color: var(--kui-blue-50);
        }
      }
    }
  }

  &.style-RelatedArticle {
    border: 0 !important;
  }

  &.style-DateImgRightTagsTitleLeadWide,
  &.style-DateImgRightTagsTitleLeadWideLarge {
    .article {
      &-title,
      &-labels,
      &-lead {
        margin-bottom: 0;
      }

      &-lead {
        line-height: 24px;
      }

      &-thumbnail-figure {
        max-width: 192px;
      }

      &-date {
        flex-basis: 108px;
        border-right: 1px solid var(--kui-slate-900);
        padding: 26px 16px 0 0;
        color: var(--kui-slate-900);
        font-size: 12px;
        font-weight: 700;
        line-height: 16px;
        align-self: stretch;
      }

      &-container {
        gap: 16px;
      }

      &-content {
        gap: 8px;
      }

      &-link-wrapper:hover {
        .article-label {
          color: var(--kui-blue-500);
        }
      }
    }

    mno-opinion-author {
      margin-top: 0;
    }
  }

  &.style-DateImgRightTagsTitleLeadWide {
    .article {
      &-date {
        max-width: 108px;
        min-width: 108px;
      }
    }
  }

  &.style-DateImgRightTagsTitleLeadWideLarge {
    .article {
      &-content {
        flex: 1;
      }

      &-thumbnail-figure {
        flex: 1;
        max-width: 50%;
      }

      &-title {
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.015em;
      }
    }
  }

  &.style-NoImgTitleBadge {
    .article {
      &-title {
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
        margin-bottom: 2px;
      }
    }
  }

  &.style-Img16TopTagsTitleLeadBadge {
    .article-labels {
      @include media-breakpoint-down(sm) {
        margin-bottom: 6px;
      }
    }
  }

  &.is-opinion,
  &.is-interview {
    .article-title {
      font-family: var(--kui-font-secondary);

      &.style-Img16TopTagsTitleLeadBadge,
      &.style-Img16TopTagsTitleLeadLarge,
      &.style-Img1TopTagsTitleLeadBadge,
      &.style-Img4TopTitleLeadBadgeLarge {
        font-size: 32px;
        line-height: 36px;
      }
    }
  }
}

.article {
  &-program-location,
  &-program-date {
    color: var(--kui-slate-950);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 128.571% */
  }

  &-program-container {
    .icon {
      margin-right: 8px;
    }
  }
}

:host {
  &.as-podcast {
    .article-link-wrapper {
      &:hover {
        .article-content-counter {
          .icon {
            @include transition;
            border-color: transparent;
            @include icon('icons/sound-wave-circle-white.svg', true);
          }
        }

        .article-title,
        .article-label {
          color: var(--kui-white);
        }
      }

      .article {
        &-thumbnail-figure {
          margin-bottom: 8px;
        }

        &-title {
          font-size: 20px;
          font-weight: 700;
          line-height: 26px;
          letter-spacing: 0.015em;
        }

        &-label {
          font-size: 14px;
          font-weight: 500;
          line-height: 18px;
        }

        &-title,
        &-label {
          color: var(--kui-slate-50);
          padding: 0 8px;
        }
      }
    }
  }

  &.as-gallery {
    .article-link-wrapper {
      &:hover {
        .article-content-counter {
          .icon {
            @include transition;
            border-color: transparent;
            @include icon('icons/image-circle-white.svg', true);
          }
        }

        .article-content-container {
          @include transition;
          background-color: var(--kui-slate-50-o65);

          .article-title,
          .article-lead {
            color: var(--kui-slate-950);
          }
        }
      }
    }

    .article-title {
      font-family: var(--kui-font-secondary);
      font-size: 24px;
      font-weight: 700;
      line-height: 30px;
      padding: 0 16px;
    }

    .article-lead {
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      padding: 0 16px;
    }
  }

  &.as-program {
    border: 1px solid var(--kui-slate-300) !important;
    border-radius: 2px;
    @include transition;

    .article {
      &-content-counter {
        width: 100%;
        justify-content: flex-start;
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        padding: 4px 8px;
        height: 48px;
      }

      &-lead {
        font-size: 16px;
        line-height: 24px;
      }

      &-title {
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.015em;
      }

      &-label {
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        color: var(--kui-blue-900);
      }
    }

    &:hover {
      border-color: var(--kui-slate-400) !important;
      @include transition;

      .article {
        &-title,
        &-lead {
          color: var(--kui-slate-950);
        }

        &-label {
          color: var(--kui-blue-500);
        }
      }
    }

    &.style-Img1TopTagsTitleLeadBadge,
    &.style-Img4TopTitleLeadBadgeLarge {
      .article {
        &-title,
        &-lead,
        &-labels,
        &-program-container {
          padding-left: 8px;
          padding-right: 8px;
        }

        &-thumbnail-figure {
          margin-bottom: 16px;
        }
      }

      padding-bottom: 8px;
    }

    &.style-DateImgRightTagsTitleLeadWide {
      .article {
        &-date {
          border-color: var(--kui-slate-900);
          padding-top: 24px;
        }
      }
    }
  }

  &.as-result {
    .article {
      &-title {
        font-size: 16px;
        font-weight: 700;
        line-height: 21px; /* 131.25% */
      }

      &-lead {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px; /* 150% */
      }
    }
  }
}

.result-date {
  display: flex;
  padding: 8px 8px 8px 0;
  align-items: center;
  border-bottom: 0.5px solid var(--kui-slate-900);
  color: var(--kui-slate-900);
  font-size: 12px;
  font-weight: 700;
  line-height: 16px; /* 133.333% */
  margin-bottom: 8px;

  :host.style-Img16TopTagsTitleLeadBadge & {
    margin-bottom: 0;
  }
}
