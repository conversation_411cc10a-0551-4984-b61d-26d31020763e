<div
  *ngIf="
    articleLink &&
      styleID !== OpinionCardType.OpinionHeader &&
      styleID !== OpinionCardType.InterviewHeader &&
      styleID !== OpinionCardType.SorozatvetoHeader &&
      styleID !== OpinionCardType.MinuteToMinuteHeader &&
      styleID !== OpinionCardType.WhereTheBallWillBe;
    else simpleWrapper
  "
  (click)="onLinkClick(articleLink)"
  (keydown)="onLinkClick(articleLink)"
  class="opinion-link-wrapper"
>
  <ng-container [ngTemplateOutlet]="articleContent"></ng-container>
</div>

<ng-template #simpleWrapper>
  <div class="opinion-wrapper">
    <ng-container [ngTemplateOutlet]="articleContent"></ng-container>
  </div>
</ng-template>

<ng-template #articleContent>
  <ng-container *ngIf="styleID === OpinionCardType.Narrow || styleID === OpinionCardType.Large">
    <ng-container *ngTemplateOutlet="thumbnailContainer"></ng-container>
  </ng-container>
  <div *ngIf="withContainer; else noContainer" class="opinion-container">
    <div
      *ngIf="
        styleID !== OpinionCardType.InterviewHeader &&
        styleID !== OpinionCardType.SorozatvetoHeader &&
        styleID !== OpinionCardType.MinuteToMinuteHeader &&
        styleID !== OpinionCardType.WhereTheBallWillBe
      "
      class="opinion-ornament"
    >
      <hr />
      <img
        *ngIf="styleID !== OpinionCardType.AuthorOpinionHeader"
        [color]="styleID !== OpinionCardType.OpinionWaiter ? 'white' : 'lightblue'"
        [showLabel]="false"
        [size]="iconSize"
        [width]="iconSize"
        alt="Nagy idézőjel ikon"
        loading="lazy"
        mno-icon="idezojelek"
      />
      <span *ngIf="styleID === OpinionCardType.OpinionWaiter" class="opinion-wrapper-label"> Véleményváró </span>
      <div *ngIf="styleID === OpinionCardType.AuthorOpinionHeader" class="opinion-wrapper-label">
        <mno-opinion-author *ngIf="author" [data]="author" [isWhite]="false" [showName]="false" [size]="authorSize"></mno-opinion-author>

        {{ data?.columnTitle }}
      </div>
      <hr />
    </div>
    <ng-container [ngSwitch]="styleID">
      <ng-container *ngSwitchCase="OpinionCardType.Large">
        <ng-container *ngTemplateOutlet="columnBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="authorBlock"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.OpinionHeader">
        <ng-container *ngTemplateOutlet="columnBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="authorBlock"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.InterviewHeader">
        <ng-container *ngTemplateOutlet="columnBlock"></ng-container>
        <ng-container *ngIf="isMobile">
          <ng-container *ngTemplateOutlet="thumbnailContainer"></ng-container>
        </ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="authorBlockNameOnly"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.MinuteToMinuteHeader">
        <ng-container *ngTemplateOutlet="liveBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="authorBlockNameOnly"></ng-container>
        <ng-container *ngTemplateOutlet="photoInfoBlock"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.SorozatvetoHeader">
        <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.Narrow">
        <ng-container *ngTemplateOutlet="columnBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="authorBlock"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.WhereTheBallWillBe">
        <ng-container *ngTemplateOutlet="columnBlock"></ng-container>
        <div class="opinion-divider"></div>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="authorBlockNameOnly"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.OpinionWaiter">
        <ng-container *ngIf="!isMobile; else useArticleCard">
          <div class="opinion-layout-wrapper d-flex align-items-stretch justify-content-evenly">
            <div class="opinion-content-container d-flex flex-column justify-content-start align-items-center">
              <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
              <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
              <ng-container *ngTemplateOutlet="lead"></ng-container>
              <ng-container *ngTemplateOutlet="authorBlock"></ng-container>
            </div>
            <ng-container *ngTemplateOutlet="thumbnailContainer"></ng-container>
          </div>
        </ng-container>
        <ng-template #useArticleCard>
          <article [data]="data" [isOpinion]="true" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
        </ng-template>
      </ng-container>

      <ng-container *ngSwitchCase="OpinionCardType.AuthorOpinionHeader">
        <ng-container *ngIf="!isMobile; else useArticleCard">
          <div class="opinion-layout-wrapper d-flex align-items-stretch justify-content-evenly">
            <div class="opinion-content-container d-flex flex-column justify-content-center align-items-center">
              <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
              <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
              <ng-container *ngTemplateOutlet="lead"></ng-container>
            </div>
            <ng-container *ngTemplateOutlet="thumbnailContainer"></ng-container>
          </div>
        </ng-container>
        <ng-template #useArticleCard>
          <article [data]="data" [isOpinion]="true" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
        </ng-template>
      </ng-container>
    </ng-container>
  </div>
  <ng-template #noContainer>
    <ng-container [ngSwitch]="styleID">
      <ng-container *ngSwitchCase="OpinionCardType.SmallAuthorRight">
        <ng-container *ngTemplateOutlet="articleStyle"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.SmallImgRightBorderBlack">
        <ng-container *ngTemplateOutlet="articleStyle"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.SmallImgRightSplitBorderBlack">
        <div class="opinion-container-top">
          <div class="opinion-container-left">
            <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
            <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
            <ng-container *ngTemplateOutlet="lead"></ng-container>
            <ng-container *ngTemplateOutlet="badges"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="thumbnailContainer"></ng-container>
        </div>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.SmallAuthorRightBorderBlue">
        <ng-container *ngTemplateOutlet="articleStyle"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.LabelTitleBadge">
        <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="OpinionCardType.AuthorLabelTitleBadge">
        <ng-container *ngTemplateOutlet="authorBlock"></ng-container>
        <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
        <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
      </ng-container>
      <ng-template #articleStyle>
        <div class="opinion-container-top">
          <div class="opinion-container-left">
            <ng-container *ngTemplateOutlet="authorNameBlock"></ng-container>
            <ng-container *ngTemplateOutlet="labelsBlock"></ng-container>
            <ng-container *ngTemplateOutlet="titleBlock"></ng-container>
          </div>
          <ng-container *ngIf="styleID === OpinionCardType.SmallAuthorRight || styleID === OpinionCardType.SmallAuthorRightBorderBlue; else thumbnailContainer">
            <ng-container *ngTemplateOutlet="authorBlockNoName"></ng-container>
          </ng-container>
        </div>
        <ng-container *ngTemplateOutlet="lead"></ng-container>
        <ng-container *ngTemplateOutlet="badges"></ng-container>
      </ng-template>
    </ng-container>
  </ng-template>
  <ng-container
    *ngIf="
      styleID === OpinionCardType.OpinionHeader ||
      (styleID === OpinionCardType.InterviewHeader && !isMobile) ||
      styleID === OpinionCardType.MinuteToMinuteHeader
    "
  >
    <ng-container *ngTemplateOutlet="thumbnailContainer"></ng-container>
  </ng-container>

  <ng-container *ngIf="styleID === OpinionCardType.WhereTheBallWillBe">
    <ng-container *ngTemplateOutlet="authorImg"></ng-container>
  </ng-container>
</ng-template>

<ng-container
  *ngIf="
    styleID === OpinionCardType.OpinionHeader ||
    styleID === OpinionCardType.InterviewHeader ||
    styleID === OpinionCardType.SorozatvetoHeader ||
    styleID === OpinionCardType.MinuteToMinuteHeader ||
    styleID === OpinionCardType.WhereTheBallWillBe
  "
>
  <ng-container *ngTemplateOutlet="infoBlock"></ng-container>
</ng-container>
<ng-container *ngIf="styleID === OpinionCardType.MinuteToMinuteHeader">
  <div class="opinion-card-minute-to-minute-info">
    <div class="section-left">
      <h5 class="minute-summary">A nap eddigi összefoglalója</h5>
    </div>
    <div class="section-right">
      <ul class="minute-blocks">
        <li *ngFor="let minuteBlock of article.minuteToMinuteBlocks | slice: 0 : 5; let i = index">
          <button (click)="onBlockClick(i, $event)" class="cursor-pointer">{{ minuteBlock.title }}</button>
        </li>
      </ul>
    </div>
  </div>
</ng-container>

<ng-template #thumbnailContainer let-displayedAspectRatio="displayedAspectRatio">
  <img
    [alt]="'Cikk kép: ' + articleCard?.thumbnail?.alt"
    [data]="data?.thumbnailFocusedImages"
    [displayedAspectRatio]="{
      desktop: styleID === OpinionCardType.SmallImgRightBorderBlack || styleID === OpinionCardType.SmallImgRightSplitBorderBlack ? '1:1' : '16:9',
    }"
    [displayedUrl]="
      styleID === OpinionCardType.SmallImgRightBorderBlack || styleID === OpinionCardType.SmallImgRightSplitBorderBlack
        ? displayed43ThumbnailUrl
        : displayedThumbnailUrl
    "
    class="opinion-thumbnail"
    loading="lazy"
    withFocusPoint
  />
</ng-template>
<ng-template #authorImg>
  <img [alt]="data?.author?.name" [src]="data?.avatarLarge" loading="lazy" class="opinion-author-image" />
</ng-template>
<ng-template #labelsBlock let-showPublishDate="showPublishDate">
  <div class="opinion-labels">
    <img
      *ngIf="
        styleID !== OpinionCardType.Large &&
        styleID !== OpinionCardType.Narrow &&
        styleID !== OpinionCardType.OpinionHeader &&
        styleID !== OpinionCardType.InterviewHeader &&
        styleID !== OpinionCardType.SorozatvetoHeader &&
        styleID !== OpinionCardType.MinuteToMinuteHeader
      "
      [size]="16"
      alt="Idézőjel ikon"
      loading="lazy"
      mnoBadge
      mno-icon
      type="opinion"
    />
    <ng-container *ngIf="showOpinionLabels">
      <ng-container *ngFor="let label of labels | slice: 0 : tagLimit">
        <span *ngIf="!label.link; else link" class="opinion-label">{{ label.title }}</span>
        <ng-template #link>
          <a [routerLink]="label.link" class="opinion-label">{{ label.title }}</a>
        </ng-template>
      </ng-container>
    </ng-container>
    @if (showPublishDate) {
      <span
        *ngIf="publishDate && styleID !== OpinionCardType.InterviewHeader && styleID !== OpinionCardType.MinuteToMinuteHeader"
        class="opinion-publish-date with-label"
      >
        {{ publishDate | dfnsFormat: 'Pp' }}
      </span>
    } @else {
      <span *ngIf="publishDate && styleID === OpinionCardType.SorozatvetoHeader" class="opinion-label opinion-label-date">{{
        publishDate | dfnsFormat: 'Pp'
      }}</span>
    }
  </div>
</ng-template>

<ng-template #columnBlock>
  <div class="opinion-labels">
    <a *ngIf="isInterview; else columnLabel" [routerLink]="['/', 'rovat', 'interview']" class="opinion-label opinion-column">Interjú</a>
    <ng-template #columnLabel>
      <a [routerLink]="['/', 'rovat', data?.columnSlug]" class="opinion-label opinion-column">{{ data?.columnTitle }}</a>
    </ng-template>
    <span *ngIf="publishDate && styleID === OpinionCardType.InterviewHeader" class="opinion-label-date">{{ publishDate | dfnsFormat: 'PPPp' }}</span>
  </div>
</ng-template>

<ng-template #authorNameBlock>
  <ng-container *ngIf="author?.name && author?.slug; else noLink">
    <a [routerLink]="['/', 'szerzo', author?.slug]" class="opinion-author-name">
      {{ author?.name }}
    </a>
  </ng-container>
  <ng-template #noLink>
    <span *ngIf="author" class="opinion-author-name">{{ author?.name }}</span>
  </ng-template>
</ng-template>
<ng-template #titleBlock>
  @if (styleID !== OpinionCardType.WhereTheBallWillBe) {
    <h3 class="opinion-title">{{ articleCard?.title }}</h3>
  } @else {
    <h3 class="opinion-title">
      <img
        class="opinion-title-quote-left"
        [color]="'lightgray'"
        [showLabel]="false"
        [size]="36"
        [width]="36"
        alt="Nagy idézőjel ikon"
        loading="lazy"
        mno-icon="idezojelek"
      />
      {{ articleCard?.title }}
      <img
        class="opinion-title-quote-right"
        [color]="'lightgray'"
        [showLabel]="false"
        [size]="36"
        [width]="36"
        alt="Nagy idézőjel ikon"
        loading="lazy"
        mno-icon="idezojelek"
      />
    </h3>
  }
</ng-template>
<ng-template #lead>
  <p class="opinion-lead">{{ articleCard?.lead ?? articleCard?.excerpt }}</p>
</ng-template>
<ng-template #authorBlock>
  <mno-opinion-author *ngIf="author" [data]="author" [isWhite]="styleID !== OpinionCardType.OpinionWaiter" [size]="authorSize"></mno-opinion-author>
</ng-template>
<ng-template #authorBlockNoName>
  <mno-opinion-author
    *ngIf="author"
    [data]="author"
    [isSquare]="styleID === OpinionCardType.SmallImgRightBorderBlack || styleID === OpinionCardType.SmallImgRightSplitBorderBlack"
    [showName]="false"
    [size]="authorSize"
  ></mno-opinion-author>
</ng-template>

<ng-template #authorBlockNameOnly>
  <div class="opinion-author">
    <ng-container *ngIf="author?.name && author?.slug; else noLink">
      <span *ngIf="isMobile && styleID === OpinionCardType.InterviewHeader"> Szerző: </span>
      <a [routerLink]="['/', 'szerzo', author?.slug]" class="opinion-author-name">
        {{ author?.name }}
      </a>
    </ng-container>
    <ng-template #noLink>
      <span *ngIf="author?.name" class="opinion-author-name">{{ author?.name }}</span>
    </ng-template>

    <span
      *ngIf="articleCard.articleSource && styleID !== OpinionCardType.MinuteToMinuteHeader && styleID !== OpinionCardType.WhereTheBallWillBe"
      class="opinion-author-source"
      >Forrás: {{ articleCard.articleSource }}</span
    >
  </div>
</ng-template>

<ng-template #badges>
  <div [attr.data-icon-size]="20" class="opinion-badge-container">
    <img *ngIf="isAdultsOnly" [size]="20" alt="18+ ikon" mnoBadge mno-icon type="adult" />
    <i *ngIf="hasVideo" [size]="20" mnoBadge mno-icon type="video"></i>
    <i *ngIf="hasGallery" [size]="20" mnoBadge mno-icon type="gallery"></i>
  </div>
</ng-template>

<ng-template #infoBlock>
  <div class="opinion-info-block">
    <div *ngIf="styleID !== OpinionCardType.SorozatvetoHeader" class="section-left">
      <ng-template *ngTemplateOutlet="labelsBlock; context: { showPublishDate: true }"></ng-template>

      <div
        *ngIf="
          isMobile &&
          (articleCard?.thumbnailInfo?.photographer || articleCard?.thumbnailInfo?.caption || articleCard?.thumbnailInfo?.title) &&
          styleID !== OpinionCardType.MinuteToMinuteHeader
        "
        [class.mobile-padding]="publishDate"
        class="opinion-photographer"
      >
        Fotó: {{ articleCard?.thumbnailInfo?.photographer }}
        {{ articleCard?.thumbnailInfo?.caption ?? articleCard?.thumbnailInfo?.title }}
      </div>
    </div>
    <div class="section-right">
      <div
        *ngIf="!isMobile && articleCard?.thumbnailInfo?.photographer && styleID !== OpinionCardType.MinuteToMinuteHeader; else placeholder"
        class="opinion-photographer"
      >
        Fotó: {{ articleCard?.thumbnailInfo?.photographer }}
      </div>
      <ng-template #placeholder>
        <div></div>
      </ng-template>
      <mno-social-share [isMobile]="isMobile" [link]="shareLink" [title]="articleCard.title"></mno-social-share>
    </div>
  </div>
</ng-template>

<ng-template #liveBlock>
  <div class="opinion-card-live-info">
    Élő közvetítés
    <span *ngIf="publishDate" class="opinion-publish-date">
      {{ publishDate | dfnsFormat: 'PPp' }}
    </span>
  </div>
</ng-template>

<ng-template #multiAuthorBlockNameOnly></ng-template>

<ng-template #photoInfoBlock>
  <div *ngIf="articleCard?.thumbnailInfo?.photographer" class="opinion-photographer">Fotó: {{ articleCard?.thumbnailInfo?.photographer }}</div>
</ng-template>
