import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { Author, BaseComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf, NgTemplateOutlet } from '@angular/common';

export type AuthorAvatarSize = 'small' | 'medium' | 'larger' | 'large';

@Component({
  selector: 'mno-opinion-author',
  templateUrl: './opinion-author.component.html',
  styleUrls: ['./opinion-author.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgTemplateOutlet],
})
export class OpinionAuthorComponent extends BaseComponent<Author> {
  DEFAULT_AVATAR = '/assets/images/logo-mno-small-padded-dark.svg';

  @Input() hideAvatar: boolean = false;

  @Input() set isSquare(value: boolean) {
    this.#isSquare = value;
    this.setProperties();
  }

  get isSquare(): boolean {
    return this.#isSquare;
  }

  @Input() set isWhite(value: boolean) {
    this.#isWhite = value;
    this.setProperties();
  }

  get isWhite(): boolean {
    return this.#isWhite;
  }

  @Input() set size(value: AuthorAvatarSize) {
    this.#size = value;
    this.setProperties();
  }

  get size(): AuthorAvatarSize {
    return this.#size;
  }

  @Input() set showName(value: boolean) {
    this.#showName = value;
  }

  get showName(): boolean {
    return this.#showName;
  }

  @HostBinding('class') hostClass = 'opinion-card';

  avatarUrl = this.DEFAULT_AVATAR;

  #showName = true;
  #size: AuthorAvatarSize = 'small';
  #isWhite = false;
  #isSquare = false;

  override setProperties(): void {
    super.setProperties();

    this.avatarUrl = this.data?.avatarUrl || this.DEFAULT_AVATAR;
    this.hostClass = [
      this.#isSquare ? 'is-square' : null,
      this.#isWhite ? 'color-white' : null,
      this.#size === 'large' ? 'is-large' : null,
      this.#size === 'larger' ? 'is-larger' : null,
      this.#size === 'medium' ? 'is-medium' : null,
    ]
      .filter((c) => !!c)
      .join(' ');
  }
}
