<ng-container *ngIf="data?.slug; else noLink">
  <a [routerLink]="['/', 'szerzo', data?.slug]" class="author-link">
    <ng-container *ngTemplateOutlet="img"></ng-container>
  </a>
</ng-container>

<ng-template #noLink>
  <div class="author-link">
    <ng-container *ngTemplateOutlet="img"></ng-container>
  </div>
</ng-template>

<ng-template #img>
  <div class="avatar-wrapper">
    <img
      *ngIf="!hideAvatar && (data?.name || data?.avatarUrl)"
      [alt]="data?.name + ' avatarja'"
      [class.author-avatar-default]="!data?.avatarUrl"
      [src]="avatarUrl"
      [title]="data?.name"
      class="author-avatar"
      loading="lazy"
    />
  </div>
  <span *ngIf="showName && data?.name" class="author-name">{{ data?.name }}</span>
</ng-template>
