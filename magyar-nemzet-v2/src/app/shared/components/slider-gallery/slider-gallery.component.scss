@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-primary);

  .adult-btn {
    font-family: inherit;
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    width: 200px;
    background-color: var(--kui-slate-900);
    color: var(--kui-white);
    padding: 8px;

    &:hover {
      color: var(--kui-slate-300);
    }

    @include media-breakpoint-down(sm) {
      font-size: 12px;
      width: 150px;
      padding: 6px;
    }
  }

  .desktop-only {
    @include media-breakpoint-down(md) {
      display: none !important;
    }
  }

  .icon {
    height: 20px;
    width: 20px;
  }

  .wrapper {
    position: relative;
  }

  .main {
    width: 100%;
  }

  .swiper-prev,
  .swiper-next {
    height: 44px;
    min-width: 44px;
    @include transition;
    background-color: var(--kui-slate-900-o65);

    &:hover {
      @include transition;
      background-color: var(--kui-slate-900-o85);
    }

    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: calc(50% - 22px);
    z-index: 2;
    @include media-breakpoint-down(sm) {
      top: calc(50% - 16px);
      height: 32px;
      min-width: 32px;
    }

    &.left {
      left: 0;
      @include media-breakpoint-up(lg) {
        left: 0;
      }
    }

    &.right {
      right: 0;

      @include media-breakpoint-up(lg) {
        right: 0;
      }
    }
  }

  .hidden {
    display: none;
  }

  ::ng-deep {
    swiper-container.main {
      swiper-slide {
        width: 70%;
        @include media-breakpoint-down(md) {
          width: 90%;
        }
      }

      .swiper-slide {
        &-prev,
        &-next {
          position: relative;

          &:after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 0.7;
            background:
              linear-gradient(0deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 100%),
              lightgray 50% / cover no-repeat;
          }
        }

        &-prev {
          justify-content: flex-end;

          .gallery-details {
            opacity: 0;
          }
        }

        &-next {
          justify-content: flex-start;

          .gallery-details {
            opacity: 0;
          }
        }

        .gallery-slide {
          width: 100%;
        }
      }
    }
  }

  mno-tag-list::ng-deep {
    margin-bottom: 0;

    .label.first {
      font-weight: 700;
    }
  }

  &.fullsize {
    background-color: var(--kui-slate-800);
    color: var(--kui-white);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 24px 0;
    @include media-breakpoint-down(sm) {
      padding: 16px 0 24px 0;
    }

    .swiper {
      position: relative;
    }

    .wrapper {
      max-width: calc(100% - 32px);
      width: 1272px;
      margin: 0 auto;
      position: relative;
      gap: 16px;
      @include media-breakpoint-down(sm) {
        width: 100%;
        max-width: 100%;
      }
    }

    .gallery {
      border-bottom: 0;

      &-details {
        border-bottom: 1px solid var(--kui-white);
      }

      &-details-caption {
        color: var(--kui-white);
      }

      &-title {
        color: var(--kui-white);

        font-family: var(--kui-font-secondary);
        font-size: 32px;
        font-weight: 700;
        line-height: 36px; /* 112.5% */
      }

      &-layer-info {
        gap: 16px;
        flex: 1;
      }

      &-layer-meta {
        font-size: 14px;
        font-weight: 400;
        line-height: 18px; /* 128.571% */
        gap: 16px;
        @include media-breakpoint-down(sm) {
          align-items: flex-start !important;
        }

        .left {
          gap: 16px;
          @include media-breakpoint-down(sm) {
            gap: 4px !important;
            align-items: flex-start !important;
            justify-content: flex-start !important;
          }

          .gallery-layer-meta-title {
            font-weight: bold;
          }

          .gallery-layer-meta-photographer,
          .gallery-layer-meta-date {
            padding-left: 16px;
            border-left: 1px solid var(--kui-slate-300);

            &:first-child {
              padding-left: 0;
              border-left: 0;
            }

            @include media-breakpoint-down(sm) {
              padding-left: 0;
              border-left: 0;
            }
          }

          @include media-breakpoint-down(sm) {
            mno-tag-list {
              order: 0;
            }
            .gallery-layer-meta-photographer {
              order: 2;
              margin-top: 4px;
            }
            .gallery-layer-meta-date {
              order: 1;
            }
          }
        }
      }

      &-layer-header {
        gap: 64px;
        margin-bottom: 16px;

        .gallery-layer-info {
          flex-basis: initial;
        }

        @include media-breakpoint-down(md) {
          margin-bottom: 0;

          flex-wrap: wrap;
          gap: 16px;

          .logo,
          .gallery-layer-top-icons {
            width: 50%;
          }
          .gallery-layer-info {
            order: 3;
            width: 100%;
          }

          .gallery-title {
            display: flex;
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 30px; /* 125% */
          }
        }

        .left {
          flex-wrap: wrap;
          border: 0;
          padding: 0;
          gap: 16px;
          @include media-breakpoint-down(sm) {
            flex-direction: column;
            display: flex;
          }
        }

        .logo,
        .gallery-layer-top-icons {
          max-width: 153px;
          gap: 24px;
        }

        .gallery-layer-top-icons {
          &-item {
            transform: scale(100%);
            @include transition;
            border: 1px solid transparent;
            border-radius: 30px;
            width: 32px;
            height: 32px;
            padding: 1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            object-fit: cover;

            &:hover {
              transform: scale(90%);
              border: 1px solid var(--kui-white);
            }
          }
        }
      }

      &-details {
        @include media-breakpoint-down(sm) {
          width: calc(100% + 32px);
          margin: -8px -16px 0;
          padding: 0 16px 8px;

          .gallery-details-caption {
            display: inline-block;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px; /* 128.571% */
          }
          .gallery-details-image-count {
            font-size: 14px;
            line-height: 18px; /* 128.571% */
          }
        }
      }
    }

    .swiper-bottom-wrapper {
      padding-top: 16px;
      margin-top: -24px;
      @include media-breakpoint-down(sm) {
        margin: -12px 0;
        max-width: 100%;
      }
    }

    @include media-breakpoint-down(md) {
      .gallery-slide-image {
        aspect-ratio: 16/9;
      }
    }

    .gallery-details {
      width: 100%;
      justify-content: center;
    }

    @include media-breakpoint-down(sm) {
      .swiper-wrapper {
        margin: 0 -16px;
        width: calc(100% + 32px);
      }
    }
  }
}

.gallery {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 24px;
  position: relative;
  border-bottom: 1px solid var(--kui-slate-200);
  @include media-breakpoint-down(xs) {
    display: block;
    padding: 0 16px 8px;
    gap: 0;
  }

  &-header {
    padding: 16px 0px;
    display: flex;
    flex-direction: column;

    @include media-breakpoint-down(xs) {
      padding: 12px 0px;
      gap: 8px;
    }
  }

  &-header-inner {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  &-title {
    @include media-breakpoint-down(xs) {
      display: none;
    }
  }

  &-title-mobile {
    display: none;

    @include media-breakpoint-down(xs) {
      display: block;
    }
  }

  &-button {
    height: 32px;
    width: 32px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 0;
    background-color: var(--kui-slate-900-o65);
  }

  &-buttons {
    display: flex;
    gap: 8px;
  }

  &-slide {
    //width: fit-content;

    &-image {
      aspect-ratio: 4/3;
      object-fit: contain;
      width: 100%;
      min-height: 200px;
      cursor: pointer;
    }
  }

  &-details {
    max-width: 1272px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;

    @include media-breakpoint-down(sm) {
      padding: 12px 0 0;
    }

    &-inner {
      display: flex;
      gap: 10px;
    }

    &-image-count,
    &-caption,
    &-source {
      color: var(--kui-slate-950);
      font-size: 20px;
      font-style: normal;
      line-height: 24px;
      font-weight: 400;

      &.mobile {
        display: none;
        @include media-breakpoint-down(xs) {
          margin-top: 5px;
          display: block;
        }
      }
    }

    &-image-count {
      color: var(--kui-blue-500);
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      letter-spacing: 0.015em;
    }

    &-caption {
      @include media-breakpoint-down(xs) {
        display: none;
      }
    }

    &-source {
      @include media-breakpoint-down(xs) {
        display: none;
      }
    }
  }
}

.swiper-bottom-wrapper {
  margin: 0 84px;
  max-width: 1044px;
}
