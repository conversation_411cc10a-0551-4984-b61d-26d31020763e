<div class="fullwidth-ad-zone" *ngIf="(isOlimpiaMainOrArticlePage$ | async) === false">
  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.leaderboard_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
</div>
<kesma-advertisement-adocean *ngIf="adverts?.desktop?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.desktop?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<div>
  <header
    #header
    (breakingClose)="onBreakingCloseClick()"
    (cookieSettingsClick)="onCookieSettingsClick()"
    (subscriptionClick)="onSubscriptionClick('header')"
    *ngIf="!isMobileApp"
    [breakingAllowed]="breakingAllowed"
    [breakingColor]="config.header.breakingColor"
    [breaking]="breakingNews"
    [data]="mainMenu"
    [facebookLink]="facebookLink"
    [instagramLink]="instagramLink"
    [isDark]="config.header.isDark"
    [isStuck]="headerStuck()"
    [trendingTags]="tags"
    [twitterLink]="twitterLink"
    [videaLink]="videaLink"
    [youtubeLink]="youtubeLink"
    mno-header
  >
    <kesma-eb-matches
      (navigateTo)="navigateToEbPage()"
      *ngIf="ebService.isEnableFootballEbElements() && (ebLiveHeaderData$ | async) as data"
      [data]="data"
      [styleID]="EBPortalEnum.MNO"
      class="style-{{ EBPortalEnum[EBPortalEnum.MNO] }}"
      eb-header
    ></kesma-eb-matches>

    <kesma-olimpia-header
      (navigateTo)="navigateToOlimpiaPage()"
      *ngIf="olimpiaService.isEnableOlympicsElements() && (olimpiaHeaderData$ | async) as data"
      [data]="data"
      [styleID]="OlimpicPortalEnum.OlimpicMNO"
      class="style-{{ OlimpicPortalEnum[OlimpicPortalEnum.OlimpicMNO] }}"
      olimpia-header
    ></kesma-olimpia-header>
  </header>
  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['szponzorcsik'] as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['szponzorcsik'] as ad" [ad]="ad"></kesma-advertisement-adocean>

  <app-cpac-2025-banner *ngIf="isCpac2025$ | async" />
  <div
    *ngIf="sponsorship$ | async as sponsorship"
    [style.background-color]="sponsorship.sponsor.highlightedColor || ''"
    [style.color]="sponsorship.sponsor.fontColor || ''"
    class="content-wrap content-wrap-full-width header-sponsorship"
  >
    <a [href]="sponsorship.sponsor.url" class="header-sponsorship-link">
      <h4>{{ sponsorship.title }}:</h4>
      <img
        *ngIf="sponsorship.sponsor.thumbnailUrl; else titleOnly"
        [alt]="'Szponzor logo: ' + sponsorship.sponsor.title"
        [src]="sponsorship.sponsor.thumbnailUrl"
        class="header-sponsorship-logo"
        loading="lazy"
      />
      <ng-template #titleOnly>
        <span class="header-sponsorship-title">{{ sponsorship.sponsor.title }}</span>
      </ng-template>
    </a>
  </div>

  <ng-container *ngIf="electionsService.isElections2024Enabled() && (isHome$ | async) === false">
    <section>
      <div class="wrapper elections-box-wrapper">
        <kesma-elections-box [link]="electionsService.getElections2024Link()" [styleID]="ElectionsBoxStyle.HEADER"></kesma-elections-box>
      </div>
    </section>
  </ng-container>

  <div [class.content-wrap-full-width]="isFullWidth$ | async" class="content-wrap">
    <router-outlet></router-outlet>
  </div>

  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.leaderboard_footer as ad" [ad]="ad" class="fullwidth-ad-zone ad-with-margin">
  </kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_footer as ad" [ad]="ad" class="fullwidth-ad-zone ad-with-margin">
  </kesma-advertisement-adocean>
</div>

<footer
  (cookieSettingsClick)="onCookieSettingsClick()"
  (subscriptionClick)="onSubscriptionClick('footer')"
  *ngIf="!isMobileApp"
  [facebookLink]="facebookLink"
  [instagramLink]="instagramLink"
  [twitterLink]="twitterLink"
  [videaLink]="videaLink"
  [youtubeLink]="youtubeLink"
  mno-footer
></footer>

<kesma-mediaworks-footer-compact [data]="mediaworksFooter"></kesma-mediaworks-footer-compact>
