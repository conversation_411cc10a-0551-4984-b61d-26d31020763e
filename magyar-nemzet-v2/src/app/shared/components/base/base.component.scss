@use 'shared' as *;

:host {
  z-index: 1;

  > .fullwidth-ad-zone:first-child {
    display: block;

    kesma-advertisement-adocean::ng-deep {
      padding: 0;
    }
  }
}

header {
  margin-inline: auto;
}

.content-wrap {
  width: calc(100% - 32px);
  max-width: 1272px;
  margin: auto;

  &.content-wrap-full-width {
    width: 100%;
    max-width: 100%;
  }

  @include media-breakpoint-down(sm) {
    width: 100%;
  }
}

.header-sponsorship {
  &.content-wrap-full-width {
    display: flex;
    width: calc(100% - 32px);
    margin: 0 84px;
    padding: 24px 0;
    align-items: center;
    justify-content: space-between;
    background: #083377;
    @include media-breakpoint-down(sm) {
      width: 100%;
      margin: 0;
      padding-left: 24px;
      padding-right: 24px;
    }
  }

  &-link {
    margin: 0 auto;
    width: 100%;
    max-width: $article-max-width;
    color: var(--kui-white);
    font-size: 20px;
    line-height: 24px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;

    h4 {
      font-weight: 400;
    }
  }

  &-logo {
    max-height: 50px;
  }

  &-title {
    font-size: 32px;
    line-height: 36px;
    font-weight: 500;
  }
}

kesma-mediaworks-footer-compact::ng-deep {
  p {
    color: var(--kui-white);
    opacity: 0.5;
  }
}

kesma-elections-box {
  margin: 30px 0;
}

.elections-box-wrapper {
  max-width: $layout-max-width;
  width: calc(100% - 32px);

  @include media-breakpoint-down(sm) {
    width: calc(100% - 32px);
  }
}
