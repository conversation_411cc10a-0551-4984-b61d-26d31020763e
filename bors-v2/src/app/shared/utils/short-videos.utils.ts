import { Injectable, inject } from '@angular/core';
import { BackendVideo } from '@trendency/kesma-ui';
import { ConvertEmbededLinkService } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class ShortVideosUtilsService {
  private readonly convertEmbededLinkService = inject(ConvertEmbededLinkService);

  convertVideoUrls(videos: BackendVideo[]): BackendVideo[] {
    return videos.map((item: BackendVideo) => ({
      ...item,
      videaUrl: this.convertEmbededLinkService.getEmdededLink(item.videaUrl),
    }));
  }
}
