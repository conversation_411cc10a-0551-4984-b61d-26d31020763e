<a routerLink="/">
  <img class="footer-logo hover-scale" src="/assets/images/bors-logo.svg" width="88" height="120" alt="Borsonline" loading="lazy" />
</a>
<footer class="footer">
  <div class="socials">
    <a class="hover-scale" href="https://www.tiktok.com/@borsonline" target="_blank" aria-label="Tiktok">
      <kesma-icon name="tiktok" size="30"></kesma-icon>
    </a>
    <a class="hover-scale" href="https://www.instagram.com/borsonline/" target="_blank" aria-label="Instagram">
      <kesma-icon name="instagram" size="30"></kesma-icon>
    </a>
    <a class="hover-scale" href="https://www.facebook.com/Borsonline.Bors.Szorakoztato.Napilap" target="_blank" aria-label="Facebook">
      <kesma-icon name="facebook" size="30"></kesma-icon>
    </a>
  </div>
  @if (data?.length) {
    <div class="footer-links">
      @for (menu of data; track menu.id) {
        @if (menu.relatedType === RelatedType.DROPDOWN) {
          <div class="footer-link bordered">{{ menu.title }}</div>
        } @else if (menu.isCustomUrl) {
          <a class="footer-link linked bordered" [href]="menu.link" [target]="menu.target">{{ menu.title }}</a>
        } @else {
          <a class="footer-link linked bordered" [routerLink]="menu.link" [target]="menu.target">{{ menu.title }}</a>
        }
      }
      <button class="footer-link linked" (click)="openCookieSettings()">Süti beállítások</button>
    </div>
  }
  <strong class="copyright">© Bors 2007–{{ currentYear }} Minden jog fenntartva.</strong>
</footer>
