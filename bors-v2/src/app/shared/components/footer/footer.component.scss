@use 'shared' as *;

:host {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  font-size: 16px;
  color: var(--kui-white);
  line-height: 18.4px;
  background-color: var(--kui-black-950);
  padding: 32px 28px;
  @include media-breakpoint-down(md) {
    flex-direction: column;
    align-items: center;
    gap: 32px;
    .footer-logo {
      width: 59px;
      height: 80px;
    }
  }
  .footer {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: center;
      gap: 32px;
    }
  }
  .socials {
    display: flex;
    padding: 5px;
    gap: 20px;
  }
  .footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }

    button {
      font-size: 16px;
    }
  }
  .footer-link {
    color: var(--kui-white);
    display: inline-block;
    @include media-breakpoint-up(lg) {
      height: 33px;
      padding: 7px 16px;
      &.bordered {
        border-right: 1px solid var(--kui-gray-600);
      }
    }
  }
  .copyright {
    height: 19px;
  }
  .linked {
    cursor: pointer;
    text-decoration-color: transparent;
    transition: all 0.3s;
    &:hover {
      text-decoration-line: underline;
      text-decoration-color: var(--kui-white);
    }
  }
  .hover-scale {
    transition: scale 300ms;
    &:hover {
      scale: 1.1;
    }
  }
}
