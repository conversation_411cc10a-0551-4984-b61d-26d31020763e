<div class="top-header-wrapper">
  <div [class.homepage]="isHomePage()" class="top-header">
    <div class="menu-items left">
      @if (topMenu(); as topMenu) {
        @for (menuItem of topMenu; track menuItem.id) {
          @if (menuItem.isCustomUrl) {
            <a [href]="menuItem.link" class="menu-item" [target]="menuItem.target">{{ menuItem.title }}</a>
          } @else {
            <a [routerLink]="menuItem.link" class="menu-item">{{ menuItem.title }}</a>
          }
        }
      }
    </div>
    <div class="menu-items right">
      @for (menuItem of topMenuFixItems; track menuItem.id) {
        <a [routerLink]="menuItem.link" class="menu-item">{{ menuItem.title }}</a>
      }
      <a [routerLink]="'/orvos-valaszol'" class="menu-item orvos-valaszol">Orvos válaszol</a>
      <a [routerLink]="'/lexikon'" class="menu-item bors-lexikon">Bors Lexikon</a>
      <app-retro-radio-player-button></app-retro-radio-player-button>

      @if (weather(); as weatherData) {
        <a [routerLink]="'/idojaras'" class="weather">
          <kesma-icon [size]="16" [name]="'weather/' + weatherData.icon2"></kesma-icon>
          <span>{{ weatherData.temperature }}°C</span>
          {{ weatherData.city }}
        </a>
      }

      @if (namedays()?.length) {
        <p class="nameday">{{ namedays() }} névnapja</p>
      }
    </div>
  </div>
</div>

<header (mouseleave)="hoveredParentMenuItemIndex.set(null)">
  <div
    class="main-header model-1"
    [class.opened]="isHamburgerOpen()"
    [style.background-color]="backgroundColor()"
    [ngClass]="{ open: isHamburgerOpen(), homepage: isHomePage() }"
  >
    <div class="hamburger-button-wrapper">
      <button class="menu-icon hamburger" type="button" [class.active]="isHamburgerOpen()" (click)="toggleHamburgerMenu()" aria-label="Hamburger menü">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>
    </div>

    @if (!isHamburgerOpen()) {
      <a [routerLink]="'/'" class="logo">
        <img src="assets/images/bors-logo.svg" alt="BORS" />
      </a>
    } @else {
      <div [routerLink]="'/'" class="logo wide">
        <img src="assets/images/bors_logo_wide.svg" alt="BORS" />
      </div>
    }

    <div [ngClass]="{ hidden: isHamburgerOpen() }" class="menu-items">
      @if (mainMenu(); as mainMenu) {
        @for (menuItem of mainMenu; track menuItem.id) {
          @if (menuItem.isCustomUrl) {
            <a
              (click)="closeHamburger()"
              (mouseover)="hoveredParentMenuItemIndex.set($index)"
              [href]="menuItem.link"
              [target]="menuItem.target"
              class="menu-item"
            >
              {{ menuItem.title }}
              @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
                <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
              }
            </a>
          } @else {
            <a (click)="closeHamburger()" (mouseover)="hoveredParentMenuItemIndex.set($index)" [routerLink]="menuItem.link" class="menu-item">
              {{ menuItem.title }}
              @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
                <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
              }
            </a>
          }
        }
      }
    </div>
    <div class="icons">
      <button (click)="toggleSearch()" aria-label="Keresés">
        <kesma-icon name="magnifier-colored" [size]="16" class="icon" />
      </button>
      <a [routerLink]="'/profil'" aria-label="Profil">
        <kesma-icon name="profile-colored" [size]="16" class="icon" />
      </a>
    </div>
  </div>

  @if (hoveredParentMenuItemSubItems()?.length) {
    <div class="hover-menu" [class.not-home-page]="!isHomePage()" [style.background-color]="backgroundColor()">
      <div class="hover-menu-list">
        @for (menuItem of hoveredParentMenuItemSubItems(); track menuItem.id) {
          @if (menuItem.isCustomUrl) {
            <a (click)="closeHamburger()" [href]="menuItem.link" [target]="menuItem.target" class="hover-menu-item">{{ menuItem.title }}</a>
          } @else {
            <a (click)="closeHamburger()" [routerLink]="menuItem.link" class="hover-menu-item">{{ menuItem.title }}</a>
          }
        }
      </div>
    </div>
  }

  @if (isHamburgerOpen()) {
    <div [class.homepage]="isHomePage()" class="hamburger-menu" (clickOutside)="closeHamburger()">
      <div class="top-menu-list">
        @for (menuItem of topMenu(); track menuItem.id) {
          @if (menuItem.isCustomUrl) {
            <a (click)="closeHamburger()" [href]="menuItem.link" [target]="menuItem.target" class="top-menu-list-item">{{ menuItem.title }}</a>
          } @else {
            <a (click)="closeHamburger()" [routerLink]="menuItem.link" class="top-menu-list-item">{{ menuItem.title }}</a>
          }
        }
      </div>
      <div class="menu-list">
        @for (menuItem of parentMenuItems(); track menuItem.id) {
          <div class="menu-item">
            @if (menuItem.isCustomUrl) {
              <a (click)="closeHamburger()" [href]="menuItem.link" [target]="menuItem.target" class="menu-list-item column">
                {{ menuItem.title }}
                @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
                  <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                }
              </a>
            } @else {
              <a (click)="closeHamburger()" [routerLink]="menuItem.link" class="menu-list-item">
                {{ menuItem.title }}
                @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
                  <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                }
              </a>
            }
            @if (menuItem?.hasSubItems) {
              <div class="child-list">
                @for (childItem of menuItem.children; track childItem.id) {
                  @if (childItem.isCustomUrl) {
                    <a (click)="closeHamburger()" [href]="childItem.link" [target]="menuItem.target" class="menu-list-item-child">
                      {{ childItem.title }}
                      @if (childItem.relatedType === RelatedType.COLUMN && childItem?.related?.sponsorship; as sponsorship) {
                        <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                      }
                    </a>
                  } @else {
                    <a (click)="closeHamburger()" [routerLink]="childItem.link" class="menu-list-item-child">
                      {{ childItem.title }}
                      @if (childItem.relatedType === RelatedType.COLUMN && childItem?.related?.sponsorship; as sponsorship) {
                        <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                      }
                    </a>
                  }
                }
              </div>
            }
          </div>
        }
        <div class="menu-item-standalone">
          @for (menuItem of standaloneMenuItems(); track menuItem.id) {
            <div class="menu-item">
              @if (menuItem.isCustomUrl) {
                <a (click)="closeHamburger()" [href]="menuItem.link" [target]="menuItem.target" class="menu-list-item">
                  {{ menuItem.title }}
                  @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
                    <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                  }
                </a>
              } @else {
                <a (click)="closeHamburger()" [routerLink]="menuItem.link" class="menu-list-item">
                  {{ menuItem.title }}
                  @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship; as sponsorship) {
                    <img [src]="sponsorship?.logo" alt="Szponzor logo" loading="lazy" />
                  }
                </a>
              }
            </div>
          }
        </div>
      </div>
      <app-mobile-menu-list [menuItems]="mainMenu()" (itemClicked)="closeHamburger()"></app-mobile-menu-list>
      <div class="links-and-socials">
        <div class="links">
          <a (click)="closeHamburger()" [routerLink]="'/szerzo'">Szerzők</a>
          <a (click)="closeHamburger()" [routerLink]="'/impresszum21'">Impresszum</a>
          <a (click)="closeHamburger()" [routerLink]="'/bors-napilap-elofizetes'">Előfizetés</a>
          <a (click)="closeHamburger()" [routerLink]="'/adatvedelem'">Adatvédelmi tájékoztató</a>
          <a (click)="closeHamburger()" [routerLink]="'/felhasznalasi-feltetelek'">Felhasználási feltételek</a>
          <a (click)="openCookieSettings()">Süti beállítások</a>
          @for (fixMenuItem of topMenuFixItems; track fixMenuItem.id) {
            <a (click)="closeHamburger()" [routerLink]="fixMenuItem.link" class="mobile-only">{{ fixMenuItem.title }}</a>
          }
        </div>

        <div class="mobile-buttons">
          <a (click)="closeHamburger()" [routerLink]="'/orvos-valaszol'" class="bors-lexikon">Orvos válaszol</a>
          <a (click)="closeHamburger()" [routerLink]="'/lexikon'" class="bors-lexikon">Bors Lexikon</a>
          <app-retro-radio-player-button></app-retro-radio-player-button>
        </div>
        <div class="socials">
          <a href="https://www.tiktok.com/@borsonline" target="_blank">
            <kesma-icon name="tiktok-white" size="30"></kesma-icon>
          </a>
          <a href="https://www.instagram.com/borsonline/" target="_blank">
            <kesma-icon name="instagram-white" size="30"></kesma-icon>
          </a>
          <a href="https://www.facebook.com/Borsonline.Bors.Szorakoztato.Napilap" target="_blank">
            <kesma-icon name="facebook-white" size="30"></kesma-icon>
          </a>
        </div>
      </div>
    </div>
  }
</header>

@if (isSearchOpen()) {
  <app-popup>
    <div class="search-overlay">
      @if (hasSearchError()) {
        <div class="search-error-text"><kesma-icon [name]="'info-circle'" [size]="16"></kesma-icon> Kérjük, adjon meg legalább 3 karaktert a kereséshez.</div>
      }
      <div (clickOutside)="isSearchOpen.set(false)" class="search-bar" [ngClass]="{ error: hasSearchError() }">
        <input type="text" [(ngModel)]="searchInput" placeholder="Keresés..." />
        <button (click)="onSearch()" aria-label="Keresés">Keresés</button>
      </div>
    </div>
  </app-popup>
}
