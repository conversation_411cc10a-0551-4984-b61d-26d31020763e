@use 'shared' as *;

.top-header-wrapper {
  position: relative;
}

.top-header {
  position: absolute;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  font-size: 14px;
  font-weight: 500;
  line-height: 14px;
  max-width: $global-wrapper-width-with-bg;
  color: var(--kui-black-950);
  z-index: 1500;
  padding: 10px 32px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% + 64px);

  @include media-breakpoint-down(md) {
    display: none;
  }

  &.homepage {
    background-color: var(--kui-dark-blue-800);
    color: var(--kui-white);
    max-width: $global-wrapper-width-with-bg;

    .menu-item {
      color: var(--kui-white);
    }

    .menu-items {
      .weather,
      .nameday {
        color: var(--kui-white);

        kesma-icon {
          color: var(--kui-hwite);
        }
      }
    }
  }

  .menu-items {
    display: flex;
    width: 100%;
    overflow: hidden;

    &.left {
      flex: 1 1 auto;
      min-width: 0;
      overflow-x: auto;
      white-space: nowrap;
    }

    &.right {
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      width: fit-content;
    }

    .weather,
    .nameday {
      padding: 2px 8px;
      display: flex;
      gap: 5px;
      align-items: center;
      line-height: 20px;
      color: var(--kui-black-950);

      kesma-icon {
        margin-left: 10px;
        color: var(--kui-black-950);
      }
    }
  }

  .menu-item {
    display: flex;
    padding: 2px 8px;
    line-height: 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: center;
    color: var(--kui-black-950);
    transition: $button-hover-transition;

    &:hover {
      cursor: pointer;
      color: var(--kui-white);
      background-color: var(--kui-red-500);
    }

    &.bors-lexikon {
      border-radius: 6px;
      padding: 8px 15px;
      color: var(--kui-white);
      background-color: var(--kui-red-500);
      font-weight: 700;
      text-transform: uppercase;
      margin-right: 10px;
      height: 40px;
      transition: $button-hover-transition;
      margin-left: 10px;

      &:hover {
        background-color: var(--kui-red-550);
      }
    }
    &.orvos-valaszol {
      border-radius: 6px;
      padding: 8px 15px;
      color: var(--kui-white);
      background-color: var(--kui-red-500);
      font-weight: 700;
      text-transform: uppercase;
      height: 40px;
      transition: $button-hover-transition;
      margin-left: 10px;

      &:hover {
        background-color: var(--kui-red-550);
      }
    }
  }
}

header {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  color: var(--kui-black-950);
  margin: 0 auto;
  position: sticky;
  top: -72px;
  z-index: 1499;

  @include media-breakpoint-down(md) {
    padding: 0 15px;
    top: 16px;
  }

  .hover-menu {
    position: absolute;
    top: calc(100% + 12px);
    max-width: $global-wrapper-width;
    width: 100%;
    padding-inline: 176px 15px;
    font-size: 14px;
    display: flex;
    align-items: center;
    height: 40px;
    font-weight: 500;
    line-height: 20px;

    &.not-home-page {
      max-width: $global-wrapper-width-with-bg;
    }
    @include media-breakpoint-down(md) {
      display: none;
    }
    &-list {
      display: flex;
      align-items: center;
      overflow: auto hidden;
    }
    &-item {
      color: var(--kui-white);
      padding-inline: 15px;
      white-space: nowrap;
      &:hover {
        color: var(--kui-white-o90);
      }
    }
  }

  .main-header {
    max-width: $global-wrapper-width-with-bg;
    display: flex;
    width: 100%;
    align-items: center;
    height: 60px;
    margin: 107px auto -12px auto;
    position: relative;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
    padding: 0 30px;
    z-index: 10;

    &.homepage {
      max-width: $global-wrapper-width;
    }

    &:not(.homepage) {
      @include media-breakpoint-up(lg) {
        margin-top: 90px;
      }

      &::before {
        content: ' ';
        background: white;
        width: 100%;
        height: 18px;
        position: absolute;
        top: -18px;
        left: 0;
        z-index: -1;
      }
    }

    @include media-breakpoint-down(md) {
      height: 50px;
      padding: 0 20px;
      margin: 0;
      width: 100%;
    }

    &.open {
      .logo {
        flex: 1;
        text-align: center;

        img {
          max-height: 40px;
        }
      }
    }

    .hamburger-button-wrapper {
      display: flex;
      align-items: center;
      @include media-breakpoint-down(md) {
        flex: 1;
      }
    }

    .hamburger-icon {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: scale 300ms;
      flex-shrink: 0;

      kesma-icon {
        fill: var(--kui-white);
      }

      @include media-breakpoint-down(md) {
        flex: 1;
      }

      &:hover {
        kesma-icon {
          color: var(--kui-white-o90);
          fill: var(--kui-white-o90);
        }
      }
    }

    .logo {
      margin: 0 25px 0 30px;
      height: 100px;
      width: 70px;
      cursor: pointer;
      display: inline-flex;
      flex-shrink: 0;

      @include media-breakpoint-down(md) {
        display: flex;
        justify-content: center;
        margin: 0;
        width: 59px;
        height: 80px;
        flex: 1;
        position: absolute;
        left: calc(50% - 59px / 2);
      }

      &.wide {
        height: unset;
        justify-content: center;
      }
    }

    .menu-items {
      display: flex;
      width: fit-content;
      overflow-y: hidden;
      overflow-x: auto;
      white-space: nowrap;
      height: 100%;

      @include media-breakpoint-down(md) {
        display: none;
      }

      .menu-item {
        display: flex;
        color: var(--kui-white);
        padding: 0px 10px;
        height: 100%;
        align-self: center;
        font-size: 14px;
        font-weight: 700;
        line-height: 14px;
        letter-spacing: 1px;
        text-transform: uppercase;
        cursor: pointer;
        align-items: center;
        gap: 5px;
        text-decoration-color: transparent;
        transition: all 0.3s;

        img {
          width: 24px;
        }
        &:hover {
          text-decoration-line: underline;
          text-decoration-style: solid;
          text-decoration-color: white;
          text-decoration-thickness: 3px;
          text-underline-offset: 25px;
        }
      }

      &.hidden {
        display: none;
      }
    }

    .icons {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 20px;
      padding-left: 10px;
      flex-shrink: 0;

      kesma-icon.icon {
        cursor: pointer;
        transition: scale 300ms;
        color: var(--kui-white);
        fill: var(--kui-white);

        &:hover {
          color: var(--kui-white-o90);
          fill: var(--kui-white-o90);
        }
      }

      @include media-breakpoint-down(md) {
        justify-content: flex-end;
        flex: 1;
      }
    }
  }

  .opened {
    box-shadow: none;
  }

  .hamburger-menu {
    width: 100%;
    background-color: var(--kui-red-500);
    max-width: $global-wrapper-width-with-bg;
    padding: 53px 32px 32px 32px;
    color: var(--kui-white);
    position: absolute;
    top: 100%;
    max-height: calc(100vh - 155px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    display: flex;
    flex-direction: column;

    &.homepage {
      max-width: $global-wrapper-width;
    }

    &::-webkit-scrollbar {
      display: none;
    }

    @include media-breakpoint-down(md) {
      padding: 16px;
      width: calc(100% - 30px);
      max-height: 90vh;
      overflow-y: scroll;
    }

    .top-menu-list {
      display: none;

      @include media-breakpoint-down(md) {
        display: flex;
        width: 100%;
        gap: 8px;
        flex-wrap: wrap;
        margin-bottom: 16px;
      }

      &-item {
        color: var(--kui-white);
        background-color: var(--kui-black-950);
        white-space: nowrap;
        line-height: 14px;
        font-size: 14px;
        padding: 8px;
      }
    }

    app-mobile-menu-list {
      display: none;

      @include media-breakpoint-down(md) {
        display: block;
      }
    }

    .menu-list {
      display: flex;
      gap: 20px;
      margin-bottom: 100px;
      flex-wrap: wrap;

      @include media-breakpoint-down(md) {
        display: none;
      }

      .menu-item {
        display: flex;
        flex-direction: column;
        color: var(--kui-white);
        width: 100%;
        max-width: 161px;

        .child-list {
          display: flex;
          flex-direction: column;
        }

        &-standalone {
          min-width: 161px;
          display: flex;
          flex-direction: column;
        }
      }

      &-item {
        font-size: 16px;
        font-weight: 700;
        letter-spacing: 1px;
        text-transform: uppercase;
        padding-bottom: 16px;
        margin-bottom: 16px;
        border-bottom: 1px solid var(--kui-white);
        color: var(--kui-white);
        transition: $button-hover-transition;
        display: flex;
        align-items: center;
        gap: 5px;

        img {
          width: 24px;
        }

        &:hover {
          color: var(--kui-white-o90);
        }

        &-child {
          padding: 10px 30px 10px 0;
          font-size: 16px;
          font-weight: 500;
          color: var(--kui-white);
          transition: $button-hover-transition;
          display: flex;
          align-items: center;
          gap: 5px;

          img {
            width: 24px;
          }

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .links-and-socials {
      display: flex;
      justify-content: space-between;
      align-items: center;

      @include media-breakpoint-down(md) {
        flex-direction: column;
      }

      .mobile-only {
        display: none;

        @include media-breakpoint-down(md) {
          display: block;
        }
      }

      .links {
        display: flex;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          width: 100%;
          gap: 16px;
        }

        :last-child {
          border-right: unset;
        }

        a {
          color: var(--kui-white);
          padding: 7px 16px;
          border-right: 1px solid rgba(255, 255, 255, 0.3);
          transition: $button-hover-transition;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }

          @include media-breakpoint-down(md) {
            width: 100%;
            border-right: none;
            padding: 7px 16px 7px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
          }
        }
      }

      .mobile-buttons {
        display: none;

        @include media-breakpoint-down(md) {
          display: flex;
          flex-direction: column;
          padding: 32px 0;
          width: 100%;
          gap: 16px;
        }

        .bors-lexikon {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 8px 15px;
          height: 40px;
          width: 100%;
          background-color: var(--kui-black-950);
          color: var(--kui-white);
          font-size: 12px;
          line-height: 14px;
          font-weight: 700;
          text-transform: uppercase;
          border-radius: 6px;
        }

        app-retro-radio-player-button {
          display: none;

          @include media-breakpoint-down(md) {
            background-color: #0e0814;
            width: 100%;
            height: 40px;
            display: flex !important;
            justify-content: center;
            align-items: center;
          }
        }
      }

      .socials {
        padding-left: 10px;
        display: flex;
        gap: 20px;
        flex-shrink: 0;

        @include media-breakpoint-down(md) {
          padding-left: 0;
          gap: 16px;
        }

        kesma-icon {
          cursor: pointer;
          transition: scale 300ms;

          &:hover {
            scale: 1.1;
          }
        }
      }
    }
  }
}

.search-overlay {
  width: 100%;
  max-width: 691px;

  @include media-breakpoint-down(md) {
    margin: 0 10px;
  }

  .search-error-text {
    background-color: var(--kui-red-500);
    color: var(--kui-white);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    display: flex;
    width: 100%;
    padding: 10px;
    align-items: center;
    margin-bottom: 8px;
    gap: 5px;

    kesma-icon {
      padding: 4px;
    }

    @include media-breakpoint-down(md) {
      margin-bottom: 3px;
    }
  }

  .search-bar {
    display: flex;
    width: 100%;
    height: 50px;
    padding-left: 20px;
    gap: 20px;
    color: var(--kui-black-950);
    background-color: var(--kui-white);
    align-items: center;

    @include media-breakpoint-down(md) {
      height: 44px;
    }

    &.error {
      border: 2px solid var(--kui-red-500);

      input {
        color: var(--kui-red-500);
      }
    }

    input {
      color: var(--kui-black-950);
      font-size: 16px;
      font-weight: 400;
      line-height: 44px;
      height: 100%;
      width: 100%;

      &::placeholder {
        color: #334e5d;
      }
    }

    button {
      display: flex;
      background-color: var(--kui-red-500);
      color: var(--kui-white);
      padding: 0 20px;
      height: 100%;
      align-items: center;
      justify-content: center;
      width: 100%;
      max-width: 90px;
      font-weight: 700;
      line-height: 28px;
      font-size: 16px;

      &:hover {
        background-color: var(--kui-red-600);
      }
    }
  }
}

.hover-menu-list,
.menu-items {
  padding-bottom: 2px;
  &::-webkit-scrollbar {
    height: 4px;
    &-thumb {
      border-radius: 2px;
      background: var(--kui-gray-200);
      padding-top: 10px;
      cursor: pointer;
    }
  }
}

/*=============  Hamburger icon animation   ================*/

.menu-icon {
  width: 32px;
  height: 32px;
  margin-right: -10px;
  position: relative;
  border: 5px solid transparent;
  transition: 0.3s;
  cursor: pointer;
  flex-shrink: 0;

  &:not(.hamburger) {
    @include media-breakpoint-down(md) {
      flex: 1;
    }
  }

  &:hover .bar {
    background-color: var(--kui-white-o90);
  }
}
.hamburger {
  padding: 15px 15px;
  display: inline-block;
  cursor: pointer;
  transition-property: opacity, filter;
  transition-duration: 0.15s;
  transition-timing-function: linear;
  font: inherit;
  color: inherit;
  text-transform: none;
  background-color: transparent;
  border: 0;
  margin: 0;
  overflow: visible;

  &:hover {
    opacity: 0.7;
  }

  &.active {
    &:hover {
      opacity: 0.7;
    }

    .hamburger-inner,
    .hamburger-inner::before,
    .hamburger-inner::after {
      background-color: var(--kui-white);
    }

    .hamburger-inner {
      transform: rotate(45deg);
      transition-delay: 0.12s;
      transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
      &,
      &::before,
      &::after {
        height: 2px;
      }

      &::before {
        top: 0;
        opacity: 0;
        transition:
          top 0.075s ease,
          opacity 0.075s 0.12s ease;
      }

      &::after {
        bottom: 0;
        transform: rotate(-90deg);
        transition:
          bottom 0.075s ease,
          transform 0.075s 0.12s cubic-bezier(0.215, 0.61, 0.355, 1);
      }
    }
  }

  &-box {
    width: 21px;
    height: 21px;
    display: inline-block;
    position: relative;
  }

  &-inner {
    display: block;
    top: 50%;
    margin-top: -10px;
    transition-duration: 0.075s;
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);

    &,
    &::before,
    &::after {
      width: 21px;
      height: 1px;
      background-color: var(--kui-white);
      border-radius: 4px;
      position: absolute;
      transition-property: transform;
      transition-duration: 0.15s;
      transition-timing-function: ease;
    }

    &::before,
    &::after {
      content: '';
      display: block;
    }

    &::before {
      top: -7px;
      transition:
        top 0.075s 0.12s ease,
        opacity 0.075s ease;
    }

    &::after {
      bottom: -7px;
      transition:
        bottom 0.075s 0.12s ease,
        transform 0.075s cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }
  }
}

.bar {
  height: 1px;
  width: 21px;
  display: block;
  margin: 10px auto;
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  transition: 0.4s;
}
