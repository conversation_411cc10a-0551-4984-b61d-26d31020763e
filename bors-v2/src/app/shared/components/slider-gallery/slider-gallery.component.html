<div class="slider-wrapper-main">
  <div class="slider-wrapper">
    @if (!(data?.isAdult && !isAcceptedAdultContent && !isInsideAdultArticleBody)) {
      <div (click)="onOpenSliderLayer()" class="hover-overlay">
        <div class="hover-overlay-icon-wrapper">
          <kesma-icon [size]="40" name="full-size"></kesma-icon>
        </div>
      </div>
    }

    <div
      kesma-swipe
      [itemTemplate]="itemTemplate"
      [data]="data?.images"
      [startIndex]="highlightedImageIndex()"
      [breakpoints]="{
        default: {
          itemCount: 1,
          itemAlign: 'center',
          gap: '8px',
        },
      }"
    ></div>

    <ng-template #itemTemplate let-image="data">
      <article class="slider-slide-container">
        @if (data?.isAdult && !isAcceptedAdultContent && !isInsideAdultArticleBody) {
          <kesma-adult-overlay>
            <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
            <button class="adult-btn" (click)="acceptAdultContent()" clickStopPropagation custom-overlay-content>Megnézem</button>
          </kesma-adult-overlay>
        } @else {
          <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
        }
        <ng-template #galleryImage>
          <img class="gallery-image" loading="lazy" alt="{{ image?.altText ?? image?.title }}" src="{{ image?.url?.fullSize }}" />
        </ng-template>
      </article>
    </ng-template>
  </div>

  <div class="gallery-details">
    @if (data?.title) {
      <div class="title">Galéria: {{ data?.title }}</div>
    }
    @if (data?.photographer) {
      <div class="photographer">Fotó: {{ data?.photographer }}</div>
    }

    <div class="flex-wrapper">
      <div class="left">
        <div class="page">{{ currentIndex() + 1 }}/{{ data?.images?.length }}</div>
        <div class="description">{{ data?.images?.[currentIndex()]?.caption || data?.title }}</div>
      </div>

      <div class="pager">
        <button type="button" class="slider-prev" (click)="swipePrev()" aria-label="Előző">
          <kesma-icon class="chevron" [size]="40" name="chevron-left"></kesma-icon>
        </button>
        <button type="button" class="slider-next" (click)="swipeNext()" aria-label="Következő">
          <kesma-icon class="chevron" [size]="40" name="chevron-right"></kesma-icon>
        </button>
      </div>
    </div>
  </div>
</div>
