@if (!withImage()) {
  @for (article of articles(); track article.id) {
    <app-article-card [styleId]="ArticleCardType.ArticleRecommendationNoImg" [data]="article" />
  }
} @else {
  <div class="articles-container">
    @if (canSlidePrev) {
      <div class="navigation prev">
        <button (click)="slidePrev()" class="nav-prev" aria-label="Előző"><kesma-icon [name]="'arrow-right'" [size]="24"></kesma-icon></button>
      </div>
    }
    <div class="articles-with-image" kesma-swipe [data]="articles()" [breakpoints]="breakpoints" [itemTemplate]="articleWithImage" [useNavigation]="true"></div>
    @if (canSlideNext) {
      <div class="navigation next">
        <button (click)="slideNext()" class="nav-next" aria-label="Következő"><kesma-icon [name]="'arrow-right'" [size]="24"></kesma-icon></button>
      </div>
    }
  </div>
}

<ng-template #articleWithImage let-article="data">
  <app-article-card [styleId]="ArticleCardType.ArticleRecommendationTopImg" [data]="article" />
</ng-template>
