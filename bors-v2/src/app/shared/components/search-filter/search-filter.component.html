<div class="search-filter-bar">
  <input type="text" [placeholder]="'keresés'" [(ngModel)]="globalFilter" (keydown.enter)="onSubmit()" class="search-filter-bar-input" />
  <button class="search-filter-bar-clear" (click)="onClear()" aria-label="Keresés törlése">
    @if (globalFilter() && globalFilter().length > 0) {
      <kesma-icon [name]="'clear'"></kesma-icon>
    }
  </button>
  <button class="search-filter-bar-submit" (click)="onSubmit()">Keresés</button>
</div>
<div class="search-filter-select-wrapper">
  <app-search-select
    [items]="publishDateFilters"
    [value]="selectedPublishDate()"
    [bindLabel]="'label'"
    [bindValue]="'value'"
    (valueChanged)="searchFilter['from_date'] = $event"
  ></app-search-select>
  <app-search-select
    [items]="contentTypeFilters"
    [value]="selectedContentType()"
    [bindLabel]="'label'"
    [bindValue]="'value'"
    (valueChanged)="searchFilter['content_types[]'] = $event"
  ></app-search-select>
  <app-search-select [items]="categorySource()" [value]="selectedColumnSlug()" (valueChanged)="searchFilter['columnSlugs[]'] = $event"></app-search-select>
  <app-search-select
    [items]="authorsSource()"
    [value]="selectedAuthorSlug()"
    [bindLabel]="'public_author_name'"
    [bindValue]="'slug'"
    (valueChanged)="searchFilter['author'] = $event"
  ></app-search-select>
  <button class="tag-filter-toggle" [ngClass]="{ active: isTagFilter() }" (click)="toggleTagFilter()">Csak címkében</button>
</div>
