@use 'shared' as *;

:host {
  ::ng-deep kesma-adult-overlay {
    position: absolute;
    top: 0;
  }
  .gallery-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: var(--kui-black-950);
    padding: 20px 32px 32px 32px;
    clip-path: polygon(0 20px, 101% 0, 100% 100%, 0 100%);

    &.small {
      clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
      padding: 10px 16px 16px 16px;
    }

    @include media-breakpoint-down(md) {
      clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
      padding: 16px;
      margin-inline: -15px;
      width: calc(100% + 30px);
    }

    &-title {
      color: var(--kui-white);
      font-size: 48px;
      font-weight: 800;
      line-height: 42px;
      margin-bottom: 32px;
      padding-top: 32px;

      &.small {
        font-size: 28px;
        line-height: 28px;
        margin-bottom: 16px;
        padding-top: 16px;
      }

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 28px;
        margin-bottom: 16px;
        padding-top: 16px;
      }
    }
  }
  .gallery-card {
    &-badge {
      position: absolute;
      bottom: 20px;
      left: 0;
      background-color: var(--kui-black-950);
      color: var(--kui-white);
      display: flex;
      gap: 4px;
      padding: 4px 8px;
      align-items: center;
      font-size: 12px;
      font-weight: 600;
    }
    &-image {
      object-fit: cover;
      width: 100%;
      height: 100%;
      position: relative;
      margin-bottom: 32px;
      display: block;

      &.adult {
        img {
          filter: blur(50px);
        }
      }

      &.small {
        margin-bottom: 16px;
      }

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
      }

      img {
        width: 100%;
        cursor: pointer;
      }

      &-wrapper {
        position: relative;
      }
    }
    &-title {
      color: var(--kui-white);
      font-size: 30px;
      font-weight: 800;
      line-height: 42px;

      &.small {
        font-size: 20px;
        line-height: 28px;
      }

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 28px;
      }
    }
    &-tag {
      color: var(--kui-white);
      margin-top: 8px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      text-transform: uppercase;

      &:hover {
        color: var(--kui-yellow-500);
      }
    }

    &-navigation {
      display: flex;
      color: var(--kui-white);
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-bottom: 16px;

      &.small {
        margin-bottom: 8px;
      }

      @include media-breakpoint-down(md) {
        margin-bottom: 8px;
      }
    }

    &-pager {
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
    }
  }

  .navigation {
    color: var(--kui-white);
    cursor: pointer;

    &.prev {
      rotate: 180deg;
    }
  }
}
