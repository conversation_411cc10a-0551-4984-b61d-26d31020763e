@use 'shared' as *;

:host {
  &.round > button {
    border-radius: 20px;
  }
}

button.btn {
  &-primary {
    background: var(--kui-red-500);
    font-weight: 700;
    font-size: 16px;
    padding: 0 16px;
    height: 40px;
    line-height: 20px;

    &:hover {
      background: var(--kui-red-1100);
    }
  }

  &-secondary {
    box-sizing: border-box;
    background: transparent;
    border: 2px solid var(--kui-red-500);
    color: var(--kui-red-500);
    font-weight: 700;
    font-size: 16px;
    padding: 0 16px;
    height: 40px;
    line-height: 20px;

    &:hover {
      color: var(--kui-red-1100);
      border-color: var(--kui-red-1100);
    }
  }

  &-dark {
    background: var(--kui-black-950);
    color: var(--kui-white);
    font-weight: 700;
    font-size: 16px;
    padding: 0 16px;
    height: 40px;

    &:hover {
      background: var(--kui-black);
    }

    &:disabled {
      color: var(--kui-gray-400);
    }
  }
}
