import { inject, OnD<PERSON>roy, <PERSON><PERSON>, PipeTransform } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { ApiResponseMeta } from '@trendency/kesma-ui';

type LinkifyTargetEntry = Readonly<{
  entry: string; //"<PERSON>"
  type: keyof typeof EntryTypes;
  slug: string; //"jennifer-lopez"
}>;

type LinkifyMeta = ApiResponseMeta &
  Readonly<{
    contentTargetEntries?: LinkifyTargetEntry[];
  }>;

const EntryTypes = {
  starDictionaryStar: 'lexikon/sztar',
};

@Pipe({
  name: 'toLinkify',
})
export class LinkifyPipe implements PipeTransform, OnDestroy {
  // Static array to track linked entries to avoid duplicate links.
  static linkedEntries: string[] = [];
  private readonly seoService = inject(SeoService);

  transform(plainHTML: string, meta: LinkifyMeta): string {
    const entries: LinkifyTargetEntry[] = meta?.contentTargetEntries ?? [];
    if (!entries?.length) {
      return plainHTML;
    }
    entries.forEach(({ entry, slug, type }) => {
      // Skip entry if it's already linked, empty, or of an invalid type.
      if (LinkifyPipe.linkedEntries.includes(entry) || !entry.trim() || !EntryTypes[type]) {
        return;
      }
      /**
       * A RegExp pattern that matches the first standalone occurrence of the entry word in the string.
       * It ignores already linked <a> tags, so these won't be replaced if they're already linked.
       *
       * @example
       * plainHTML = "The first line of 'Lorem' Ipsum, Lorem ipsum dolor sit amet, comes from a line in section 1.10.32."
       * entry = 'Lorem'
       *
       * It will match the second `Lorem` because the first occurrence is part of a phrase and not a standalone word.
       *
       * @return The transformed string where the first `Lorem` will be replaced with a link.
       */
      const regex = new RegExp(`(?<!<a[^>]*>)(?<!<img[^>]*)(?<=^|[^\\p{L}])(${entry})(?=[^\\p{L}]|$)(?!<\\/a>)`, 'iu');
      const match: RegExpMatchArray | null = plainHTML.match(regex);
      if (!match || !match?.[0]) {
        return;
      }
      plainHTML = plainHTML.replace(regex, `<a href="${this.seoService.hostUrl}/${EntryTypes[type]}/${slug}" target="_blank">${match[0]}</a>`);
      LinkifyPipe.linkedEntries.push(entry);
    });

    return plainHTML;
  }

  /**
   * Clears the list of linked entries when the pipe is destroyed.
   */
  ngOnDestroy(): void {
    LinkifyPipe.linkedEntries = [];
  }
}
