import { DestroyRef, inject, Injectable } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { BreadcrumbItem } from '@trendency/kesma-ui';
import { Observable, take } from 'rxjs';
import { filter, map, startWith, switchMap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { StarService } from '../star/services/star.service';

@Injectable({
  providedIn: 'root',
})
export class LexiconMetaService {
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly starService = inject(StarService);

  getStarPageBreadcrumbs(label?: string): Observable<BreadcrumbItem[]> {
    return this.router.events.pipe(
      take(1),
      filter((event) => event instanceof NavigationEnd),
      startWith(this.router),
      switchMap(() => {
        let route: ActivatedRoute = this.router.routerState.root;
        while (route.firstChild) {
          route = route.firstChild;
        }

        return route.params.pipe(
          take(1),
          map(() => {
            return this.getBreadcrumbItems(route, label);
          })
        );
      })
    );
  }

  getBreadcrumbItems(route: ActivatedRoute, label?: string): BreadcrumbItem[] {
    // Avoid to be more than one letters in breadcrumbs
    let breadcrumbItems: BreadcrumbItem[] =
      route.snapshot.data['breadcrumb']
        ?.map((item: BreadcrumbItem) => ({
          label: this.getVariables(item.label),
          url: Array.isArray(item.url) ? item.url.map((url) => this.getVariables(String(url || ''))) : this.getVariables(item.url || ''),
        }))
        ?.filter((item: BreadcrumbItem) => item.label && !item.url?.includes(`abc/${label}`)) ?? [];

    if (label) {
      const hasItemWithStartingLetter = breadcrumbItems.some((item: BreadcrumbItem) => item.label === label);

      if (!hasItemWithStartingLetter && this.router.url.includes('/abc/')) {
        breadcrumbItems.push({ label: label.toUpperCase(), url: `/lexikon/sztar/abc/${label}` });
      }
      const profile = route.snapshot.data['profile'];
      if (profile) {
        breadcrumbItems = [
          { label: 'Sztár', url: '/lexikon/sztar' },
          {
            label: profile.artistName ? `${profile.name} - ${profile.artistName}` : profile.name,
            url: `/lexikon/sztar/${profile.slug}`,
          },
        ];
      }
    }

    const defaultBreadcrumbItem: BreadcrumbItem = {
      label: 'Bors Lexikon',
      url: '/lexikon/sztar',
    };
    breadcrumbItems.unshift(defaultBreadcrumbItem);

    return breadcrumbItems;
  }

  getStarPageTitle(): Observable<string> {
    return this.router.events.pipe(
      takeUntilDestroyed(this.destroyRef),
      filter((event) => event instanceof NavigationEnd),
      startWith(this.router),
      switchMap(() => {
        let route: ActivatedRoute = this.router.routerState.root;
        while (route.firstChild) {
          route = route.firstChild;
        }

        return route.params.pipe(
          takeUntilDestroyed(this.destroyRef),
          map(() => this.getVariables(route.snapshot.data['title'] ?? ''))
        );
      })
    );
  }

  getVariables(input: string): string {
    if (!input.includes(':')) {
      return input;
    }

    const variables = {
      ':dailyStarSlug': this.starService.chosenDailyStar?.slug,
      ':dailyStarName': this.starService.chosenDailyStar?.name,
      ':query': this.route.snapshot.queryParamMap.get('name_filter'),
    } as Record<string, string | undefined>;

    return input.replace(/:[a-z]+/gi, (match) => (match in variables ? variables[match] || '' : ''));
  }
}
