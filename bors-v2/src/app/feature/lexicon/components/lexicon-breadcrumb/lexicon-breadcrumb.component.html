<div>
  <a routerLink="/" aria-label="Kezdőlap">
    <img alt="" class="home-icon" src="assets/images/lexicon/breadcrumb-home.svg" />
  </a>
</div>

<div *ngFor="let item of breadcrumbItems" class="breadcrumb-item">
  <ng-container [ngTemplateOutlet]="separator"></ng-container>
  <a *ngIf="item.url; else current" [queryParams]="item.queryParams" [routerLink]="item.url" [attr.aria-label]="item.label">{{ item.label }}</a>

  <ng-template #current
    ><span>{{ item.label }}</span></ng-template
  >
</div>

<ng-template #separator>
  <img alt="" class="arrow-icon" src="assets/images/lexicon/breadcrumb-arrow.svg" />
</ng-template>
