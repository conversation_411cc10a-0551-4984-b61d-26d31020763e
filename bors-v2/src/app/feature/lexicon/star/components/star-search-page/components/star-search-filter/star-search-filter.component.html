<div class="search-by-keyword">
  <div class="search-by-keyword-input-wrapper">
    <input (keydown.enter)="onSearch()" [(ngModel)]="searchFilter['name_filter']" class="search-by-keyword-input" placeholder="Név szerint" type="text" />

    <div (click)="onClearSearch()" class="search-by-keyword-input-clear">
      <img alt="Keresés törlése" src="assets/images/lexicon/search-x.svg" />
    </div>
  </div>

  <button (click)="onSearch()" aria-label="Keresés">
    <span>Keresés</span>
    <img class="search-icon" alt="" src="assets/images/lexicon/search.svg" />
  </button>
</div>

<div class="search-filters" [class.hidden]="!isCollapsed()">
  <div class="search-filters-item input">
    <input [(ngModel)]="searchFilter['from_age_filter']" [maxLength]="3" appOnlyNumber placeholder="Kor-tól" type="text" />
  </div>

  <div class="search-filters-item input">
    <input [(ngModel)]="searchFilter['to_age_filter']" [maxLength]="3" appOnlyNumber placeholder="Kor-ig" type="text" />
  </div>

  <div class="search-filters-item select">
    <app-star-search-filter-api-select
      (ngModelChange)="searchFilter['birthplaces_filter[]'] = $event"
      [(ngModel)]="selectedBirthplace"
      [singleItemRequest]="birthplaceSingleItemRequest"
      [sourceRequest]="birthplacesSourceRequest"
      [selectedItem]="searchFilter['birthplaces_filter[]']"
      bindLabel="birthplace"
      bindValue="birthplace"
      placeholder="Születési hely"
    ></app-star-search-filter-api-select>
  </div>

  <div class="search-filters-item select">
    <app-star-search-filter-api-select
      (ngModelChange)="searchFilter['is_hungarian_filter'] = $event"
      [(ngModel)]="selectedOrigin"
      [sourceRequest]="originsSourceRequest"
      [selectedItem]="searchFilter['is_hungarian_filter']"
      [searchable]="false"
      bindLabel="title"
      bindValue="value"
      placeholder="Származás"
    ></app-star-search-filter-api-select>
  </div>

  <div class="search-filters-item select">
    <app-star-search-filter-api-select
      (ngModelChange)="searchFilter['horoscopes_filter[]'] = $event"
      [(ngModel)]="selectedHoroscope"
      [sourceRequest]="horoscopesSourceRequest"
      [selectedItem]="searchFilter['horoscopes_filter[]']"
      [searchable]="false"
      bindValue="key"
      placeholder="Horoszkóp"
    ></app-star-search-filter-api-select>
  </div>

  <div class="search-filters-item select">
    <app-star-search-filter-api-select
      (ngModelChange)="searchFilter['occupations_filter[]'] = $event"
      [(ngModel)]="selectedOccupation"
      [singleItemRequest]="occupationSingleItemRequest"
      [sourceRequest]="occupationsSourceRequest"
      [selectedItem]="searchFilter['occupations_filter[]']"
      placeholder="Foglalkozás"
    ></app-star-search-filter-api-select>
  </div>

  <div class="search-filters-item select awards">
    <app-star-search-filter-api-select
      (ngModelChange)="searchFilter['awards_filter[]'] = $event"
      [(ngModel)]="selectedAward"
      [singleItemRequest]="awardSingleItemRequest"
      [sourceRequest]="awardsSourceRequest"
      [selectedItem]="searchFilter['awards_filter[]']"
      placeholder="Díjak"
    ></app-star-search-filter-api-select>
  </div>
</div>

<div class="collapse" (click)="isCollapsed.set(!isCollapsed())">
  Részletes keresés
  <span class="arrow-wrapper" [class.collapsed]="isCollapsed()"></span>
</div>
