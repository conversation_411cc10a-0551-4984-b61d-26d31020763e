@use 'shared' as *;

:host {
  display: block;

  .star-abc {
    &-shortcuts {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
      gap: 8px;
      z-index: 1;

      @include media-breakpoint-down(md) {
        grid-template-columns: repeat(14, 1fr);
      }

      @include media-breakpoint-down(sm) {
        grid-template-columns: repeat(11, 1fr);
      }

      @include media-breakpoint-down(xs) {
        grid-template-columns: repeat(7, 1fr);
        gap: 16px;
      }

      &-letter {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid $grey-100;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $blue-dark;
        font-size: 14px;
        font-weight: 700;
        line-height: 15px;
        text-transform: uppercase;

        &.disabled {
          color: $grey-100;
        }

        &.active {
          background: $red-500;
          color: $white;
          border: none;
        }
      }
    }
  }
}
