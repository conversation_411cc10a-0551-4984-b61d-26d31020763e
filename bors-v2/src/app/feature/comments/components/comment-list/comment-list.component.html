@if (parentType() === 'article') {
  <div class="controls">
    <div class="controls-sort">
      Sorrend:
      <ng-select
        ariaLabel="Sorrend"
        class="controls-sort-select"
        [ngModel]="order()"
        (click)="toggleSelect()"
        [isOpen]="isSelectOpen()"
        (open)="isSelectOpen.set(true)"
        (close)="isSelectOpen.set(false)"
        (ngModelChange)="setCommentOrder($event)"
        [items]="orderItems"
        [clearable]="false"
      />
    </div>
    @if (currentUser()) {
      <app-simple-button class="controls-refresh" round="round" (click)="loadComments(0)" [disabled]="loadingState() === 'loading'"
        >Kommentek frissítése</app-simple-button
      >
    }
  </div>
}

@for (comment of comments(); track comment.id) {
  <div class="comment-container">
    <app-comment-card
      [comment]="comment"
      [parentAuthor]="parentAuthor()"
      [class.answer]="!!parentAuthor()"
      [isChildrenOpen]="openChildren()[comment.id]"
      (isChildrenOpenChange)="setOpenChildren(comment.id, $event)"
    />
    @if (openChildren()[comment.id]) {
      <app-comment-list [class.indent]="level() < 3" [parentId]="comment.id" [order]="order()" [level]="level() + 1" [parentAuthor]="comment.author" />
    }
  </div>
} @empty {
  @if (loadingState() !== 'loading') {
    Jelenleg nincsenek kommentek.
  }
}

@switch (loadingState()) {
  @case ('loading') {
    <p class="loading">Kommentek betöltése...</p>
  }

  @case ('error') {
    <p class="error">Kommentek betöltése sikertelen</p>
  }
}

@if (hasMore()) {
  <app-simple-button color="dark" round="round" [disabled]="loadingState() === 'loading'" (click)="loadMore()">
    További kommentek megjelenítése
  </app-simple-button>
}
