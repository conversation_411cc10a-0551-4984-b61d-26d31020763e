@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px;
  ::ng-deep {
    app-breakdcrumb ul {
      height: 42px;
      @include media-breakpoint-down(md) {
        height: 20px;
      }
    }
    app-page-title h2.page-title {
      color: $dark-blue-800;

      @include media-breakpoint-down(md) {
        margin-top: 8px;
        margin-bottom: 16px;
      }
    }
  }
  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .introduction {
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    @include media-breakpoint-down(md) {
      font-size: 18px;
      line-height: 26px;
    }
  }
  .category {
    display: flex;
    flex-direction: column;
    &-link {
      color: var(--kui-slate-950);
    }
    &-title {
      color: $dark-blue-800;
      font-size: 48px;
      font-style: normal;
      font-weight: 800;
      line-height: 42px; /* 87.5% */
      text-transform: uppercase;
      overflow-wrap: anywhere;
      margin-bottom: 32px;
      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
        font-size: 28px;
        line-height: 28px; /* 100% */
      }
    }
    &-quizzes {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 16px;
      @include media-breakpoint-down(lg) {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
      }
    }
    &-title-link {
      color: $dark-blue-800;
    }
    &-link {
      margin-top: 32px;
      display: flex;
      color: $dark-blue-800;
      gap: 10px;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px; /* 125% */
      @include media-breakpoint-down(md) {
        margin-top: 16px;
      }
    }

    &.featured {
      background-color: $dark-blue-800;
      padding: 44px 24px 24px 24px;
      clip-path: polygon(0 20px, 101% 0, 100% 100%, 0 100%);

      @include media-breakpoint-down(md) {
        padding: 26px 8px 16px 8px;
        clip-path: polygon(0 10px, 101% 0, 100% 100%, 0 100%);
      }

      .category-title {
        color: var(--kui-white);
      }

      app-quiz-mini-card {
        border-bottom: 2px solid var(--kui-red-500);
      }

      .category-link {
        color: var(--kui-white);

        kesma-icon {
          color: var(--kui-red-500);
        }
      }
    }
  }
  .list-separator {
    margin: 32px 0;
    border-color: var(--kui-gray-300);
    opacity: 0.3;
    @include media-breakpoint-down(md) {
      margin: 16px 0;
    }

    &.top {
      @include media-breakpoint-down(md) {
        margin-bottom: 32px;
      }
    }
    &.first {
      @include media-breakpoint-down(md) {
        margin-top: 32px;
        margin-bottom: 16px;
      }
    }
  }
  .with-aside {
    gap: 24px;
    margin-top: 0;
  }
  app-pager {
    margin-top: 20px;
  }
}
