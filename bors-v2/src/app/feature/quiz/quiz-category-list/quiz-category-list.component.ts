import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResponseMetaList,
  ApiResult,
  BreadcrumbItem,
  createCanonicalUrlForPageablePage,
  IconComponent,
} from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { BlockTitleRowComponent, BreadcrumbComponent, createBorsOnlineTitle, defaultMetaInfo, makeBreadcrumbSchema, PagerComponent } from '../../../shared';
import { QuizMiniCardComponent } from '../../../shared/components/quiz-mini-card/quiz-mini-card.component';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { QuizCategory, QuizCategoryWithChildQuizzes } from '../quiz.definitions';

@Component({
  selector: 'app-quiz-category-list',
  templateUrl: './quiz-category-list.component.html',
  styleUrl: './quiz-category-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SidebarComponent,
    BlockTitleRowComponent,
    RouterLink,
    PagerComponent,
    QuizMiniCardComponent,
    BreadcrumbComponent,
    IconComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class QuizCategoryListComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Aktuális',
      url: ['/rovat/aktualis'],
    },
    {
      label: 'Kvíz',
      url: ['/rovat/aktualis/kviz'],
    },
  ];

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads))));

  readonly routeData = toSignal<ApiResult<QuizCategory[], ApiResponseMetaList>>(
    this.route.data.pipe(
      map(({ data }) => {
        this.setMetaData();
        const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems);
        this.schemaService.insertSchema(breadcrumbSchema);
        return data;
      })
    )
  );

  readonly quizCategories = computed<QuizCategoryWithChildQuizzes[] | undefined>(() => this.routeData()?.data);
  readonly quizMeta = computed<ApiResponseMetaList | undefined>(() => this.routeData()?.meta);

  isFeaturedCategory(category: QuizCategoryWithChildQuizzes): boolean {
    return (category?.quizzes?.length ?? 0) > 0 && !!category.quizzes?.[0]?.quizCategoryIsFeatured;
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('rovat/aktualis/kviz', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Kvíz kategóriák');
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
