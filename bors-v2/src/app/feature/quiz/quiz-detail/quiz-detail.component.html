<section>
  @if (adverts()?.desktop?.leaderboard_1; as ad) {
    <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
  }

  <div class="wrapper">
    <app-breadcrumb [data]="breadcrumbItems()"></app-breadcrumb>
    <app-block-title-row [data]="{ text: routeData()?.data?.title }"></app-block-title-row>

    @if (routeData()?.data?.createdAt; as createdAt) {
      <div class="publish-date">
        <span class="text">Publikálás: </span>
        <span class="date">{{ createdAt | publishDate: 'yyyy. MM. dd. HH:mm' }}</span>
      </div>
    }
    <app-social-share [emailSubject]="routeData()?.data?.title || ''"></app-social-share>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (adverts()?.desktop?.roadblock_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      <app-quiz [data]="mappedQuiz()" />

      @if (adverts()?.desktop?.roadblock_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>
