import { ChangeDetectionStrategy, Component, computed, effect, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { PublishDatePipe, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResponseMetaList,
  ApiResult,
  BreadcrumbItem,
  createCanonicalUrlForPageablePage,
  Quiz,
} from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import {
  AppQuizComponent,
  BlockTitleRowComponent,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  makeBreadcrumbSchema,
  SocialShareComponent,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-quiz-detail',
  templateUrl: './quiz-detail.component.html',
  styleUrl: './quiz-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AppQuizComponent,
    SidebarComponent,
    BlockTitleRowComponent,
    BreadcrumbComponent,
    SocialShareComponent,
    PublishDatePipe,
    AdvertisementAdoceanComponent,
  ],
})
export class QuizDetailComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads) => this.adStoreAdo.separateAdsByMedium(ads))));

  readonly breadcrumbItems = computed<BreadcrumbItem[]>(() => {
    const { quizSlug } = this.route.snapshot.params;

    const breadcrumbBase = [
      {
        label: 'Aktuális',
        url: ['/rovat/aktualis'],
      },
      {
        label: 'Kvíz',
        url: ['/rovat/aktualis/kviz'],
      },
    ];
    if (!this.mappedQuiz()) {
      return breadcrumbBase;
    }
    return [
      ...breadcrumbBase,
      {
        label: this.mappedQuiz()?.title || '',
        url: ['/kvizek', quizSlug],
      },
    ];
  });

  readonly routeData = toSignal<ApiResult<Quiz, ApiResponseMetaList>>(
    this.route.data.pipe(
      map(({ data: routeResult }) => {
        this.setMetaData(routeResult.data.title);
        return routeResult;
      })
    )
  );

  readonly mappedQuiz = computed<Quiz | undefined>(() => {
    const routeData = this.routeData();
    if (!routeData?.data) {
      return;
    }
    return {
      ...routeData.data,
      questions: routeData.data.questions.map((question) => ({
        ...question,
        image: (question.image as any).fullSizeUrl,
      })),
    };
  });

  constructor() {
    effect(() => {
      const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems());
      this.schemaService.insertSchema(breadcrumbSchema);
    });
  }

  private setMetaData(quizTitle: string): void {
    const { quizSlug } = this.route.snapshot.params;
    const canonical = createCanonicalUrlForPageablePage(`kvizek/${quizSlug}`);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(quizTitle);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
