@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px;
  ::ng-deep h2.page-title {
    color: $black-950 !important;
    margin-block: 32px;
    font-size: 52px;
    @include media-breakpoint-down(md) {
      font-size: 32px;
    }
  }
  .with-aside {
    gap: 24px;
  }
  app-breadcrumb {
    ::ng-deep {
      ul.breadcrumb {
        display: block;
        li {
          display: inline;
        }
      }
    }
  }
  .publish-date {
    padding: 16px 0;
    color: var(--kui-black-950);
    line-height: 16px;
    font-size: 12px;
    border-top: 1px solid var(--kui-gray-200);
    border-bottom: 1px solid var(--kui-gray-200);

    .text {
      font-weight: 700;
      text-transform: uppercase;
    }

    .date {
      font-weight: 400;
    }
  }
  app-social-share {
    margin-block: 32px;
  }
}
