import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map, Observable } from 'rxjs';
import { ApiResponseMetaList, ArticleCard, buildArticleUrl, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { createBorsOnlineTitle, defaultMetaInfo, FormatArticlePublishDatePipe } from '../../../../shared';
import { ArchiveResponse, GroupedArchiveArticlesByColumn, NewsArchiveArticleColumns } from '../../definitions/news-archive-page.definitions';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-current-week-news',
  templateUrl: './current-week-news.component.html',
  styleUrls: ['./current-week-news.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, FormatArticlePublishDatePipe, RouterLink],
})
export class CurrentWeekNewsComponent implements OnInit {
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  readonly utilService = inject(UtilService);

  currentWeekArticles$: Observable<ArchiveResponse<GroupedArchiveArticlesByColumn[], ApiResponseMetaList, NewsArchiveArticleColumns[]>> =
    this.activatedRoute.data.pipe(map(({ data }) => data));

  ngOnInit(): void {
    this.setMetaData();
  }

  getArticleLink(data: ArticleCard): string | string[] {
    return data && buildArticleUrl(data);
  }

  getCategoryLink(data: NewsArchiveArticleColumns): string | string[] {
    return data && ['/', 'rovat', data?.columnSlug];
  }

  setMetaData(): void {
    const title = createBorsOnlineTitle('Aktuális hét hírei');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });

    const canonical = createCanonicalUrlForPageablePage(`hirarchivum/aktualis-het`, undefined);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
