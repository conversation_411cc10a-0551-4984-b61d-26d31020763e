@if (newsArchiveData$ | async; as newsArchiveData) {
  <div class="wrapper content-wrapper">
    <h1 class="archive-title">HÍRARCHÍVUM</h1>
    <div class="shortcuts">
      <h2><a [routerLink]="[today | dfnsFormat: 'yyyy', today | dfnsFormat: 'MM', today | dfnsFormat: 'dd']">Mai hírek</a></h2>
      <h2><a [routerLink]="[yesterday | dfnsFormat: 'yyyy', yesterday | dfnsFormat: 'MM', yesterday | dfnsFormat: 'dd']">Te<PERSON><PERSON> hírek</a></h2>
      <h2><a [routerLink]="'/hirarchivum/aktualis-het'">Aktuális hét hírei</a></h2>
    </div>
    <div class="years-container">
      @for (years of newsArchiveData; track years.year) {
        <div class="months-container">
          <span class="year-title">{{ years?.year }}</span>
          <div class="months">
            @for (months of years?.months; track months.month) {
              <a [routerLink]="[years?.year, months?.month]"> {{ months?.date | dfnsFormat: 'MMM' }}</a>
            }
          </div>
        </div>
      }
    </div>
  </div>
}
