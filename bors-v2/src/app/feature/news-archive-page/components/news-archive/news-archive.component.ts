import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map, Observable } from 'rxjs';
import { SeoService } from '@trendency/kesma-core';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { AsyncPipe } from '@angular/common';
import { createBorsOnlineTitle, defaultMetaInfo } from '../../../../shared';
import { DateFnsModule } from 'ngx-date-fns';
import { NewsArchiveTimeLine } from '../../definitions/news-archive-page.definitions';
import { subDays } from 'date-fns';

@Component({
  selector: 'app-news-archive',
  templateUrl: './news-archive.component.html',
  styleUrls: ['./news-archive.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, AsyncPipe, DateFnsModule],
})
export class NewsArchiveComponent implements OnInit {
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);

  newsArchiveData$: Observable<NewsArchiveTimeLine[]> = this.activatedRoute.data.pipe(map(({ data }) => data));
  today: Date = new Date();
  yesterday: Date = subDays(this.today, 1);

  ngOnInit(): void {
    this.setMetaData();
  }

  setMetaData(): void {
    const title = createBorsOnlineTitle('Hírarchívum');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
    const canonical = createCanonicalUrlForPageablePage('hirarchivum', undefined);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
