import { Routes } from '@angular/router';
import { NewsArchiveComponent } from './components/news-archive/news-archive.component';
import { MonthNewsComponent } from './components/month-news/month-news.component';
import { NewsArchiveResolver } from './api/news-archive-page.resolver';
import { MonthNewsResolver } from './api/month-news-page.resolver';
import { CurrentWeekNewsResolver } from './api/current-week-news-page.resolver';
import { CurrentWeekNewsComponent } from './components/current-week-news/current-week-news.component';

export const NEWS_ARCHIVE_PAGE_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: NewsArchiveComponent,
    resolve: { data: NewsArchiveResolver },
  },
  {
    path: 'aktualis-het',
    component: CurrentWeekNewsComponent,
    resolve: { data: CurrentWeekNewsResolver },
  },
  {
    path: ':year/:month',
    component: MonthNewsComponent,
    resolve: { data: MonthNewsResolver },
  },
  {
    path: ':year/:month/:day',
    component: MonthNewsComponent,
    resolve: { data: MonthNewsResolver },
  },
];
