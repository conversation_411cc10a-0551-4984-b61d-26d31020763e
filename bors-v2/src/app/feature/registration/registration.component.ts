import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, Signal, signal } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  emailValidator,
  KesmaFormControlComponent,
  markControlsTouched,
  passwordValidator,
  usernameWithAccentValidator,
} from '@trendency/kesma-ui';
import { IMetaData, SeoService, StorageService } from '@trendency/kesma-core';
import {
  ApiService,
  AppInputControlComponent,
  AppPasswordControlComponent,
  BackendAllowedLoginMethodsResponse,
  BorsSimpleButtonComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  RegRedirectToken,
} from '../../shared';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { environment } from '../../../environments/environment';
import { ViewportScroller } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrl: './registration.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, ReactiveFormsModule, KesmaFormControlComponent, AppInputControlComponent, AppPasswordControlComponent, BorsSimpleButtonComponent],
})
export class RegistrationComponent implements OnInit {
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly seoService = inject(SeoService);
  private readonly reCaptchaV3Service = inject(ReCaptchaV3Service);
  private readonly apiService = inject(ApiService);
  private readonly viewportScroller = inject(ViewportScroller);
  private readonly destroyRef = inject(DestroyRef);
  private readonly route = inject(ActivatedRoute);
  private readonly storageService = inject(StorageService);

  readonly formGroup = signal<UntypedFormGroup>(this.initForm);
  readonly isSubmitted = signal<boolean>(false);
  readonly error = signal<string | null>(null);
  readonly isLoading = signal<boolean>(false);
  readonly redirectUrl = signal<string | null>(this.route.snapshot.queryParamMap.get('redirect'));

  readonly allowedLoginMethods: Signal<BackendAllowedLoginMethodsResponse | undefined> = toSignal(this.apiService.getAllowedLoginMethods());

  ngOnInit(): void {
    this.setMetaData();
  }

  register(): void {
    if (this.formGroup()) {
      markControlsTouched(this.formGroup());
    }

    if (this.formGroup().invalid) {
      return;
    }
    this.error.set(null);
    this.isLoading.set(true);
    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_register',
      (recaptchaToken: string) => {
        this.apiService
          .register(this.formGroup().value, recaptchaToken)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              if (this.redirectUrl()) {
                this.storageService.setLocalStorageData(RegRedirectToken, this.redirectUrl());
              }
              this.isSubmitted.set(true);
              this.isLoading.set(false);
              this.formGroup().reset();
              this.viewportScroller.scrollToPosition([0, 0]);
            },
            error: (response: HttpErrorResponse) => {
              this.isLoading.set(false);
              const backendErrors = response.error as BackendFormErrors;
              let isErrorHandled = false;
              const childrenErrors = backendErrors?.form?.errors?.children;
              if (!childrenErrors) {
                return;
              }
              const emailErrors = childrenErrors['email']?.errors;

              if (emailErrors) {
                this.formGroup().get('email')?.setErrors({ emailInUse: true });
                isErrorHandled = true;
              }

              const userNameErrors = childrenErrors['userName']?.errors;

              if (userNameErrors) {
                const isMaxLength = userNameErrors.some((error: string) => error?.includes('100'));
                this.formGroup()
                  .get('username')
                  ?.setErrors(isMaxLength ? { usernameMaxLength: true } : { usernameInUse: true });
                isErrorHandled = true;
              }

              if (!isErrorHandled) {
                this.error.set('Ismeretlen hiba!');
              }
            },
          });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error.set('Captcha: Robot ellenőrzés hiba!');
        this.isLoading.set(false);
      }
    );
  }

  private get initForm(): UntypedFormGroup {
    return this.formBuilder.group({
      username: [null, [Validators.required, usernameWithAccentValidator, Validators.maxLength(100)]],
      email: [null, [Validators.required, emailValidator]],
      password: [null, [Validators.required, passwordValidator]],
      newsletter: [false],
      terms: [false, Validators.requiredTrue],
      marketing: [false],
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Regisztráció');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}
