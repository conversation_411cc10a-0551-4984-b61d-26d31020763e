<section>
  <div class="meta">
    @if (breadcrumbItems()?.length) {
      <app-breadcrumb [data]="breadcrumbItems()"></app-breadcrumb>
    }
    <h1 [style.color]="columnColor()" class="page-title">{{ columnTitle() }}</h1>
    @if (!layoutData()) {
      <hr class="divider" />
    }
  </div>
  @if (layoutData()) {
    <app-layout [configuration]="layoutData()?.content" [layoutType]="LayoutPageType.COLUMN" [structure]="layoutData()?.struct"></app-layout>
    @if (!isAstronet()) {
      <hr class="divider" [ngClass]="{ layout: layoutData() }" />
    } @else {
      <div class="wrapper astronet-branding-box">
        <app-astronet-branding-box brand="babahoroszkop" />
      </div>
    }
  }
  @if (articles()?.length > 0 && !layoutData() && !isAstronet()) {
    <div class="main-article">
      <app-article-card
        [styleId]="ArticleCardType.HighlightedSideImgDateTitleLead"
        [data]="articles()?.[0]"
        [useEagerLoad]="true"
        [fetchpriority]="'high'"
      ></app-article-card>
      <hr class="divider" [ngClass]="{ layout: layoutData() }" />
    </div>
  }
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (isAstronet()) {
        <div class="article-list">
          @for (article of astronetArticles(); track $index) {
            <app-astronet-card [data]="article"></app-astronet-card>
          }
        </div>
      } @else {
        @if (layoutData() && articles()?.length > 0) {
          <div class="article-list">
            @for (article of articles(); track article.id) {
              <app-article-card [styleId]="ArticleCardType.SideImgDateTitleLead" [data]="article"></app-article-card>
            }
          </div>
        } @else if (articles()?.length >= 2) {
          <div class="article-list">
            @for (article of articles() | slice: 1; track article.id; let i = $index) {
              <app-article-card [styleId]="ArticleCardType.SideImgDateTitleLead" [data]="article" [useEagerLoad]="i < 4"></app-article-card>

              @if (i === 0) {
                @if (adverts()?.desktop?.roadblock_1; as ad) {
                  <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
                }
                @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
                  <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
                }
              }
              @if (i === 2) {
                @if (adverts()?.desktop?.roadblock_2; as ad) {
                  <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
                }
                @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
                  <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
                }
              }
            }
          </div>
        }
        @if (limitable()?.pageMax) {
          <app-pager [rowAllCount]="limitable()?.rowAllCount!" [rowOnPageCount]="limitable()?.rowOnPageCount!"></app-pager>
        }
      }
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType()"></app-sidebar>
    </aside>
  </div>
</section>
