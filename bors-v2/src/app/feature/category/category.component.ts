import { Ng<PERSON>lass, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, OnDestroy, signal, Signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { ReqService, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResult,
  ArticleCard,
  BackendExternalFeedItem,
  BreadcrumbItem,
  createCanonicalUrlForPageablePage,
  LayoutPageType,
} from '@trendency/kesma-ui';
import { map, switchMap, tap } from 'rxjs/operators';
import {
  ArticleCardComponent,
  ArticleCardType,
  AstronetBrandingBoxComponent,
  AstronetCardComponent,
  BreadcrumbComponent,
  CategoryResolverResponse,
  createBorsOnlineTitle,
  DEFAULT_BREADCRUMB_ITEM,
  defaultMetaInfo,
  HeaderService,
  makeBreadcrumbSchema,
  PagerComponent,
} from '../../shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

const ASTRONET_FEED = 'https://www.astronet.hu/feed/';

@Component({
  selector: 'app-category',
  imports: [
    ArticleCardComponent,
    BreadcrumbComponent,
    PagerComponent,
    SidebarComponent,
    SlicePipe,
    LayoutComponent,
    NgClass,
    AdvertisementAdoceanComponent,
    AstronetCardComponent,
    AstronetBrandingBoxComponent,
  ],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryComponent implements OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly headerService = inject(HeaderService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly reqService = inject(ReqService);
  private readonly schemaService = inject(SchemaOrgService);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as CategoryResolverResponse)));

  readonly breadcrumbItems = computed(() => {
    const items: BreadcrumbItem[] = [];
    const columnParentTitle = this.resolverData()?.columnParentTitle;
    const columnParentSlug = this.resolverData()?.columnParentSlug;
    const currentColumnTitle = this.resolverData()?.columnTitle;
    if (columnParentTitle && columnParentSlug) {
      items.push({
        label: columnParentTitle,
        url: ['/', 'rovat', columnParentSlug],
      });
    }
    if (currentColumnTitle) {
      items.push({
        label: currentColumnTitle,
      });
    }
    return items;
  });

  readonly currentPage = computed(() =>
    this.resolverData()?.category?.meta?.limitable?.pageCurrent ? this.resolverData()!.category.meta.limitable.pageCurrent + 1 : 1
  );

  readonly columnTitle: Signal<string | undefined> = computed(() => {
    const title = this.currentPage() > 1 ? `${this.currentPage()}. oldal - ${this.resolverData()?.columnTitle}` : this.resolverData()?.columnTitle;
    this.setMetaData(title);
    return title;
  });
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.category?.data);
  readonly layoutData = computed(() => this.resolverData()?.layoutApiResponse);
  readonly limitable = computed(() => this.resolverData()?.category?.meta?.limitable);
  readonly columnColor = computed(() => this.resolverData()?.columnColor);

  readonly ArticleCardType = ArticleCardType;
  readonly LayoutPageType = LayoutPageType;

  readonly isAstronet = computed(() => this.resolverData()?.columnSlug === 'astronet');
  readonly astronetArticles = signal<BackendExternalFeedItem[]>([]);

  readonly adPageType = signal<string>('');

  readonly adverts = toSignal(
    this.route.data.pipe(
      tap(({ data }) => {
        this.adPageType.set(`column_${data.slug}`);
        this.adStoreAdo.setArticleParentCategory(this.adPageType());
      }),
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads, this.adPageType()))
    )
  );

  constructor() {
    effect(() => {
      if (this.isAstronet()) {
        this.reqService
          .get<ApiResult<{ items: BackendExternalFeedItem[] }>>(`/external-rss-feed?url=${ASTRONET_FEED}`)
          .pipe(
            map(({ data }) => data?.items),
            takeUntilDestroyed(this.destroyRef)
          )
          .subscribe((articles) => {
            this.astronetArticles.set(articles);
          });
      }
    });
    effect(() => {
      const data = this.resolverData();
      if (!data?.columnTitle || !data?.slug) {
        return;
      }
      const breadCrumbs: BreadcrumbItem[] = this.breadcrumbItems().slice(0, this.breadcrumbItems()?.length - 1);
      breadCrumbs.unshift(DEFAULT_BREADCRUMB_ITEM);
      this.schemaService.removeStructuredData();
      const breadcrumbSchema = makeBreadcrumbSchema(breadCrumbs);
      this.schemaService.insertSchema(breadcrumbSchema);
    });
  }

  ngOnDestroy(): void {
    this.headerService.setColor();
    this.adStoreAdo.setArticleParentCategory('');
  }

  private setMetaData(columnTitle?: string): void {
    if (!columnTitle) {
      return;
    }
    const title = createBorsOnlineTitle(columnTitle);

    this.seo.setMetaData({
      ...defaultMetaInfo,
      description: `${columnTitle} rovat - ${defaultMetaInfo.description}`,
      title: title,
      ogTitle: title,
    });

    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
  }
}
