import { SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, signal, Signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { SeoService, StorageService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResponseMetaList,
  ApiResult,
  createCanonicalUrlForPageablePage,
  GalleryData,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';
import {
  ADULT_CHOICE_STORAGE_KEY,
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PagerComponent,
  PageTitleComponent,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrl: './galleries.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PagerComponent, BreadcrumbComponent, PageTitleComponent, SidebarComponent, SlicePipe, ArticleCardComponent, AdvertisementAdoceanComponent],
})
export class GalleriesComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly storageService = inject(StorageService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly ArticleCardType = ArticleCardType;

  readonly isUserAdultChoice = signal<boolean>(false);

  readonly resolverData: Signal<ApiResult<GalleryData[], ApiResponseMetaList>> = toSignal(
    this.route.data.pipe(
      tap(() => {
        this.setMetaData();
        this.isUserAdultChoice.set(this.storageService.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY, false) ?? false);
      }),
      map(({ data }) => data)
    )
  );

  readonly galleries: Signal<GalleryData[] | undefined> = computed(() => this.resolverData()?.data);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta?.limitable);

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads))));

  private setMetaData(): void {
    const title = createBorsOnlineTitle('Galériák');
    const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }
}
