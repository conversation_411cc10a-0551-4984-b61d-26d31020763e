import { SlicePipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ArticleCard,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { ApiResponseMetaList } from '@trendency/kesma-ui/lib/definitions/api-result';
import { map, tap } from 'rxjs/operators';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PagerComponent,
  PageTitleComponent,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

type DossierListMeta = ApiResponseMetaList & {
  title?: string;
  description?: string;
};

@Component({
  selector: 'app-dossier-list',
  templateUrl: './dossier-list.component.html',
  styleUrl: './dossier-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ArticleCardComponent,
    BreadcrumbComponent,
    TitleCasePipe,
    PageTitleComponent,
    SlicePipe,
    PagerComponent,
    SidebarComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class DossierListComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly resolverData = toSignal(
    this.route.data.pipe(
      map(({ data }) => data),
      tap(({ meta }) => this.setMetaData(meta.title))
    )
  );

  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData().data);
  readonly meta: Signal<DossierListMeta | undefined> = computed(() => this.resolverData().meta);

  readonly ArticleCardType = ArticleCardType;

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads))));

  private setMetaData(pageTitle: string): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot, {
      skipCategorySlug: true,
    });
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(pageTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
    };
    this.seoService.setMetaData(metaData);
  }
}
