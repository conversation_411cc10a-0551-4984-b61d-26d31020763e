import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { BorsSimpleButtonComponent } from '../../../shared';
import { DialogRef } from '@angular/cdk/dialog';

@Component({
  selector: 'app-doctor-answer-success-modal',
  templateUrl: './doctor-answer-success-modal.component.html',
  styleUrl: './doctor-answer-success-modal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [BorsSimpleButtonComponent],
})
export class DoctorAnswerSuccessModalComponent {
  private readonly dialogRef = inject<DialogRef<never>>(DialogRef<never>);
  close(): void {
    this.dialogRef.close();
  }
}
