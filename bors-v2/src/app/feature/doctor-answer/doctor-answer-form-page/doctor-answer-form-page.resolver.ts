import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiResult } from '@trendency/kesma-ui';
import { throwError } from 'rxjs';
import { ApiService } from '../../../shared';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';

export const doctorAnswerFormPageResolver: ResolveFn<ApiResult<any>> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const apiService = inject(ApiService);
  const page = parseInt(route.queryParams['page']);
  const params = { ...route.queryParams, rowCount_limit: '10', ...(page > 1 ? { page_limit: (page - 1).toString() } : {}) };

  return apiService.getDoctorAnswers({ params }).pipe(
    tap((res) => {
      if (page <= 0 || (page > 1 && res.data.length === 0)) {
        throw new Error('No data supplied after pagination');
      }
    }),
    catchError((error: HttpErrorResponse) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => error);
    })
  );
};
