import { ChangeDetectionStrategy, Component, computed, effect, inject, Signal, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  BreadcrumbItem,
  createCanonicalUrlForPageablePage,
  IconComponent,
  KesmaFormControlComponent,
  LimitStringPipe,
  markControlsTouched,
} from '@trendency/kesma-ui';
import { finalize } from 'rxjs/operators';
import {
  AppInputControlComponent,
  AuthService,
  BorsSimpleButtonComponent,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  LoginRedirectToken,
  makeBreadcrumbSchema,
  PagerComponent,
  SecureApiService,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { BlockTitleRowComponent } from '../../../shared';
import { PublishDatePipe, SchemaOrgService, SeoService, StorageService } from '@trendency/kesma-core';
import { Dialog } from '@angular/cdk/dialog';
import { DoctorAnswerSuccessModalComponent } from '../doctor-answer-success-modal/doctor-answer-success-modal.component';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { DoctorAnswerListItem } from '../doctor-answer.definitions';

@Component({
  selector: 'app-doctor-answer-form-page',
  templateUrl: './doctor-answer-form-page.component.html',
  styleUrl: './doctor-answer-form-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SidebarComponent,
    BlockTitleRowComponent,
    BreadcrumbComponent,
    AppInputControlComponent,
    ReactiveFormsModule,
    KesmaFormControlComponent,
    BorsSimpleButtonComponent,
    PagerComponent,
    PublishDatePipe,
    RouterLink,
    IconComponent,
    LimitStringPipe,
  ],
  providers: [Dialog],
})
export class DoctorAnswerFormPageComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly secureApiService = inject(SecureApiService);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly authService = inject(AuthService);
  private readonly dialog = inject(Dialog);
  private readonly router = inject(Router);
  private readonly storageService = inject(StorageService);

  private readonly fb = inject(FormBuilder);

  isSubmitting = signal<boolean>(false);
  isSubmitted = signal<boolean>(false);

  routeData = toSignal(this.route.data);
  routeQueryParams = toSignal(this.route.queryParams);
  listItems: Signal<DoctorAnswerListItem[]> = computed(() => this.routeData()?.['data']?.data);
  limitable = computed(() => this.routeData()?.['data']?.meta?.limitable);
  isOnFirstPage = computed(() => !(this.currentPage() - 1));
  currentPage = computed(() => parseInt(this.routeQueryParams()?.['page']));

  formGroup = this.fb.group({
    subject: ['', [Validators.required, Validators.maxLength(12)]],
    question: ['', [Validators.required, Validators.maxLength(1200), Validators.minLength(250)]],
    acceptTerms: ['', [Validators.requiredTrue]],
  });

  readonly breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Orvos válaszol',
    },
  ];

  constructor() {
    const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems);
    this.schemaService.insertSchema(breadcrumbSchema);
    this.formGroup.valueChanges.subscribe(() => {
      if (this.isSubmitted()) this.isSubmitted.set(false);
    });
    effect(() => {
      this.setMetaData();
    });
  }
  isLoggedIn = toSignal(this.authService.isAuthenticated());

  handleSubmit(): void {
    markControlsTouched(this.formGroup);

    if (!this.formGroup.valid) {
      return;
    }
    const { subject, question } = this.formGroup.value;
    if (!subject || !question) {
      return;
    }
    this.isSubmitting.set(true);
    this.secureApiService
      .submitDoctorAnswerQuestion(subject, question)
      .pipe(
        finalize(() => {
          this.isSubmitting.set(false);
        })
      )
      .subscribe(() => {
        this.formGroup.reset();
        this.isSubmitted.set(true);
        this.openSuccessModal();
      });
  }
  private openSuccessModal(): void {
    this.dialog.open(DoctorAnswerSuccessModalComponent, {});
  }

  private setMetaData(): void {
    const currentPage = this.currentPage();
    const canonical = createCanonicalUrlForPageablePage(`orvos-valaszol`, this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle((currentPage || currentPage > 1 ? `${currentPage}. oldal - ` : '') + 'Orvos válaszol');
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      robots: 'index, follow',
    });
  }
  handleLogin(): void {
    this.storageService.setLocalStorageData(LoginRedirectToken, this.router.url);
    this.router.navigate(['/bejelentkezes']);
  }
  handleRegistration(): void {
    this.storageService.setLocalStorageData(LoginRedirectToken, this.router.url);
    this.router.navigate(['/regisztracio']);
  }
}
