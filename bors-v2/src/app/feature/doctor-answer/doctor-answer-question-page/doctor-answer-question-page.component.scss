@use 'shared' as *;

@use '@angular/cdk' as cdk;

@include cdk.overlay();

::ng-deep {
  .cdk-overlay-container {
    z-index: 2000;
  }
  .orvos-backdrop {
    background-color: rgba(0, 0, 0, 0.75);
  }
}

:host {
  display: block;
  margin-block: 32px;

  .wrapper {
    padding: 0;
  }

  @include media-breakpoint-down(md) {
    padding-inline: 16px;
  }
  .with-aside {
    gap: 24px;
    @include media-breakpoint-down(md) {
      gap: 0;
      margin-top: 0;
    }
  }
  hr {
    margin-top: 32px;
    margin-bottom: 32px;
    @include media-breakpoint-down(md) {
      margin-top: 16px;
      margin-bottom: 16px;
    }
  }
  app-breadcrumb {
    ::ng-deep {
      ul.breadcrumb {
        display: block;
        li {
          display: inline;
        }
      }
    }
  }
  .left-column {
    display: flex !important;
    flex-direction: column;
    gap: 32px;
    .date {
      font-size: 14px;
      margin-bottom: 10px;
    }
    h2 {
      font-size: 36px;
      font-weight: 800;
      line-height: 42px;
      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
      }
    }
    p {
      font-size: 18px;
      font-style: normal;
      line-height: 26px;
      &.question {
        font-size: 20px;
        line-height: 28px;
        font-weight: 600;
      }
    }
    .back-button {
      display: flex;
      color: var(--kui-red-500);
      gap: 10px;
      align-items: center;
      font-weight: 700;
    }
  }
  app-social-share {
    margin-block: 32px;
  }
}
