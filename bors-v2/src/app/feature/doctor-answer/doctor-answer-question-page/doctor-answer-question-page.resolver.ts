import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { throwError } from 'rxjs';
import { ApiService } from '../../../shared';
import { catchError } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { DoctorAnswerDetail } from '../doctor-answer.definitions';
import { ApiResult } from '@trendency/kesma-ui';

export const doctorAnswerQuestionPageResolver: ResolveFn<ApiResult<DoctorAnswerDetail>> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const apiService = inject(ApiService);
  const { slug } = route.params;
  const params = route.queryParams;

  return apiService.getDoctorAnswerSingle(slug, params).pipe(
    catchError((error: HttpErrorResponse) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => error);
    })
  );
};
