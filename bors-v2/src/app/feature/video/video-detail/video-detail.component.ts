import { ChangeDetectionStrategy, Component, effect, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { ArticleVideoComponent, IconComponent, IntervalToDurationPipe, Video, VideoComponentObject } from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { map } from 'rxjs';
import { BreadcrumbComponent, SocialShareComponent } from 'src/app/shared';

@Component({
  selector: 'app-video-detail',
  imports: [ArticleVideoComponent, BreadcrumbComponent, FormatPipeModule, IconComponent, IntervalToDurationPipe, RouterLink, SocialShareComponent],
  templateUrl: './video-detail.component.html',
  styleUrl: './video-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VideoDetailComponent {
  private readonly route = inject(ActivatedRoute);
  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as Video)));
  videoData = signal<VideoComponentObject | undefined>(undefined);
  constructor() {
    effect(() => {
      const data = this.resolverData();
      if (data) {
        this.videoData.set(this.convertVideoToKesmaVideo(data));
      }
    });
  }

  convertVideoToKesmaVideo(video: Video): VideoComponentObject {
    return {
      videaUrl: video.videaUrl,
    } as VideoComponentObject;
  }
}
