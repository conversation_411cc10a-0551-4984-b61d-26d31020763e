import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { backendDateToDate, Video } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { ApiService } from 'src/app/shared';

@Injectable()
export class VideoDetailResolver {
  constructor(private readonly apiService: ApiService) {}

  resolve(activatedRouteSnapshot: ActivatedRouteSnapshot): Observable<Video> {
    const { slug } = activatedRouteSnapshot.params;

    return this.apiService.getVideo(slug).pipe(
      map(({ data }) => {
        return { ...data?.[0], publishDate: data?.[0]?.publicDate ? (backendDateToDate(data?.[0]?.publicDate as string) ?? undefined) : undefined };
      })
    );
  }
}
