import { Routes } from '@angular/router';
import { VideoDetailResolver } from './video-detail/video-detail.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { videoListResolver } from './video-list/api/video-list.resolver';

export const VIDEOS_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    loadComponent: () => import('./video-list/video-list.component').then((m) => m.VideoListComponent),
    resolve: {
      data: videoListResolver,
    },
    runGuardsAndResolvers: 'always',
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':slug',
    loadComponent: () => import('./video-detail/video-detail.component').then((m) => m.VideoDetailComponent),
    providers: [VideoDetailResolver],
    resolve: {
      data: VideoDetailResolver,
    },
  },
];
