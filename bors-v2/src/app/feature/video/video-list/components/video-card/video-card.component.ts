import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { BackendVideo, buildColumnUrl, buildVideoUrl, FocusPointDirective, IconComponent, IntervalToDurationPipe } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { FormatPipeModule } from 'ngx-date-fns';
import { PlaceholderImg } from '../../../../../shared';

@Component({
  selector: 'app-video-card',
  imports: [RouterLink, IntervalToDurationPipe, FormatPipeModule, IconComponent, FocusPointDirective],
  templateUrl: './video-card.component.html',
  styleUrl: './video-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VideoCardComponent {
  readonly video = input.required<BackendVideo>();
  readonly isFirst = input(false);
  readonly isFeatured = input(false);

  readonly PlaceholderImg = PlaceholderImg;

  readonly columnUrl = computed(() => buildColumnUrl(this.video()));
  readonly videoUrl = computed(() => buildVideoUrl(this.video()));
}
