@use 'shared' as *;

:host {
  margin-top: 32px;
  display: block;
}

.header {
  &-title {
    color: var(--kui-red-500);
    font-size: 26px;
    font-weight: 800;
    line-height: 28px;

    @include media-breakpoint-up(md) {
      font-size: 36px;
      line-height: 42px;
    }
  }
}

.featured-videos {
  display: flex;
  flex-direction: column;
  gap: 32px;
  background-color: var(--kui-gray-50);
  padding: 24px 16px;
  margin-bottom: 32px;

  @include media-breakpoint-up(md) {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 16px 32px;
    grid-template-areas:
      'highlight .'
      'highlight .'
      'highlight .'
      'highlight .';
  }

  .highlighted {
    grid-area: highlight;
  }
}

app-short-videos {
  width: 100%;
  margin-bottom: 32px;
}

.divider {
  border-top: 1px solid var(--kui-gray-250);
  margin-bottom: 32px;
}

.video-list {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  column-gap: 32px;
  flex-direction: column;

  @include media-breakpoint-up(md) {
    flex-direction: row;

    app-video-card {
      width: calc(50% - 16px);
    }
  }
}

app-pager {
  margin: 32px auto;
}
