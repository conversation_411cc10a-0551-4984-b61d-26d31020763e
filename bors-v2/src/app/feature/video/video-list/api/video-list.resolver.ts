import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiService } from '../../../../shared';
import { backendVideoToVideoCard, RedirectService } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { forkJoin, of, throwError } from 'rxjs';
import { VideoListData } from './video-list.definitions';

const MAX_RESULTS_PER_PAGE = 25;

export const videoListResolver: ResolveFn<VideoListData> = (route) => {
  const router = inject(Router);
  const apiService = inject(ApiService);
  const redirectService = inject(RedirectService);

  const page = route.queryParams['page'] ? +route.queryParams['page'] - 1 : 0;

  return forkJoin({
    videos: apiService
      .getVideos({
        page,
        rowCount: MAX_RESULTS_PER_PAGE,
        isShort: false,
      })
      .pipe(
        map((result) => {
          if (redirectService.shouldBeRedirect(page, result.data)) {
            redirectService.redirectOldUrl('video', false, 302);
          }
          return result;
        }),
        catchError((err) => {
          router.navigate(['404'], { skipLocationChange: true }).then();
          return throwError(() => err);
        })
      ),
    shorts: apiService
      .getVideos({
        page: 0,
        rowCount: MAX_RESULTS_PER_PAGE,
        isShort: true,
      })
      .pipe(
        map((result) => result.data.map((video) => backendVideoToVideoCard(video))),
        catchError((err) => {
          console.error(err);
          return of([]);
        })
      ),
  });
};
