import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { SearchSelectComponent } from 'src/app/shared';

@Component({
  selector: 'app-profile',
  imports: [FormsModule, RouterLink, RouterLinkActive, RouterOutlet, SearchSelectComponent],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileComponent {
  private readonly router = inject(Router);

  selectMenu = [
    { title: 'Profil', slug: '/profil/beallitasok' },
    { title: 'Hozzászólások', slug: '/profil/hozzaszolasok' },
    { title: 'Mentett cikkek', slug: '/profil/mentett-cikkek' },
  ];
  selectedMenu = signal({});

  constructor() {
    const index = this.selectMenu.findIndex((item) => item.slug === this.router.url);
    this.selectedMenu.set(this.selectMenu[index]);
  }

  handleMenuSelect(route: string): void {
    this.router.navigate([route]);
  }
}
