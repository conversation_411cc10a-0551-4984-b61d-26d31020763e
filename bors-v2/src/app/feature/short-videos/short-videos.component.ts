import { Location } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, effect, ElementRef, inject, signal, viewChildren } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiListResult,
  BackendVideo,
  BypassPipe,
  IconComponent,
} from '@trendency/kesma-ui';
import { map } from 'rxjs';
import { ApiService, BorsSimpleButtonComponent, BreadcrumbComponent, defaultMetaInfo } from 'src/app/shared';
import { ShortVideosUtilsService } from 'src/app/shared/utils';

@Component({
  selector: 'app-short-videos',
  imports: [BreadcrumbComponent, IconComponent, BypassPipe, BorsSimpleButtonComponent, AdvertisementAdoceanComponent],
  templateUrl: './short-videos.component.html',
  styleUrl: './short-videos.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShortVideosComponent implements AfterViewInit {
  private readonly location = inject(Location);
  private readonly seoService = inject(SeoService);
  private readonly route = inject(ActivatedRoute);
  private readonly apiService = inject(ApiService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly shortVideosUtilsService = inject(ShortVideosUtilsService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads))));

  shortVideos = signal<BackendVideo[] | undefined>(undefined);
  currentPage = signal(0);
  firstVideoSlug: string;
  pageMax: number;

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data)));
  constructor() {
    effect(() => {
      const data = this.resolverData();
      if (data) {
        this.shortVideos.set(data.data.filter((item: BackendVideo) => item.videaUrl));
        this.firstVideoSlug = data.data[0]?.slug;
        this.pageMax = data.meta?.limitable?.pageMax;
      }
    });

    effect(() => {
      const videos = this.shortVideos();
      if (videos && videos.length > 0) {
        // Wait for the next tick to ensure DOM is updated
        setTimeout(() => {
          this.setupIntersectionObserver();
        }, 0);
      }
    });
  }

  readonly iframeElements = viewChildren('iframe', { read: ElementRef });
  readonly currentIndex = signal(0);
  readonly isAtStart = computed(() => this.currentIndex() === 0);
  readonly isAtEnd = computed(() => this.currentIndex() === (this.shortVideos()?.length ?? 0) - 1);
  readonly currentShortVideo = computed<BackendVideo | undefined>(() => this.shortVideos()?.at(this.currentIndex()));

  scrollObserver?: IntersectionObserver;

  ngAfterViewInit(): void {
    this.setupIntersectionObserver();
  }

  private setupIntersectionObserver(): void {
    if (this.scrollObserver) {
      this.scrollObserver.disconnect();
    }

    this.scrollObserver = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        entries.forEach((entry: IntersectionObserverEntry) => {
          if (entry.isIntersecting) {
            this.seoService.setMetaData({
              ...defaultMetaInfo,
              title: this.currentShortVideo()?.title,
              ogTitle: this.currentShortVideo()?.title,
            });
            // Updating the browser URL without triggering navigation.

            const index = Number(entry.target.getAttribute('data-index'));
            if (!isNaN(index)) {
              this.currentIndex.set(index);
              this.location.go(`/shorts/${this.currentShortVideo()?.slug}`);
            }
          }
        });
      },
      { threshold: 0.5 }
    );

    const shorts = this.iframeElements();
    if (shorts?.length) {
      shorts.forEach((short) => this.scrollObserver?.observe(short.nativeElement));
    }
  }

  scrollUp(): void {
    if (this.isAtStart()) {
      return;
    }
    this.scrollToCurrentVideo(this.currentIndex() - 1);
  }

  swipeDown(): void {
    if (this.isAtEnd()) {
      return;
    }
    this.scrollToCurrentVideo(this.currentIndex() + 1);
  }

  private scrollToCurrentVideo(index: number): void {
    const iframeRef = this.iframeElements().at(index)?.nativeElement;
    if (!iframeRef) {
      return;
    }
    iframeRef.scrollIntoView({ behavior: 'smooth' });
  }

  loadMore(): void {
    this.currentPage.update((v) => v + 1);
    this.apiService
      .getVideos({ page: this.currentPage(), rowCount: 12, isShort: true })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((result: ApiListResult<BackendVideo>) => {
        const filteredData = this.shortVideosUtilsService.convertVideoUrls(result.data.filter((item: BackendVideo) => item.slug !== this.firstVideoSlug));
        this.shortVideos.update((currentVideos) => [...(currentVideos || []), ...filteredData]);
      });
  }
}
