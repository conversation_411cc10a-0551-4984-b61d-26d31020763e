import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { ApiListResult, BackendVideo } from '@trendency/kesma-ui';
import { Observable, switchMap, map } from 'rxjs';
import { ApiService } from 'src/app/shared';
import { ShortVideosUtilsService } from 'src/app/shared/utils';

@Injectable()
export class ShortVideosResolver {
  private readonly shortVideosUtilsService = inject(ShortVideosUtilsService);
  constructor(private readonly apiService: ApiService) {}

  resolve(activatedRouteSnapshot: ActivatedRouteSnapshot): Observable<ApiListResult<BackendVideo>> {
    const { slug } = activatedRouteSnapshot.params;
    if (!slug) {
      return this.apiService.getVideos({ page: 0, rowCount: 12, isShort: true }).pipe(
        map((result) => ({
          ...result,
          data: this.shortVideosUtilsService.convertVideoUrls(result.data),
        }))
      );
    }

    return this.apiService.getVideos({ page: 0, rowCount: 1, isShort: true }, slug).pipe(
      switchMap(({ data: firstCallData }) => {
        const firstItem = firstCallData?.[0];
        const firstId = firstItem?.id;
        return this.apiService.getVideos({ page: 0, rowCount: 12, isShort: true }).pipe(
          map((secondResult) => {
            const filtered = this.shortVideosUtilsService.convertVideoUrls(
              secondResult.data.filter((item: BackendVideo) => (firstId ? item.id !== firstId : true))
            );
            return {
              ...secondResult,
              data: firstItem ? [...this.shortVideosUtilsService.convertVideoUrls([firstItem]), ...filtered] : filtered,
            };
          })
        );
      })
    );
  }
}
