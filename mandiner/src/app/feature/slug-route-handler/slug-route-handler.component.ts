import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { StaticPageComponent } from '../static-page/components/static-page/static-page.component';
import { NgIf } from '@angular/common';
import { ArticlePageComponent } from '../article-page/article-page.component';

@Component({
  selector: 'app-slug-route-handler',
  templateUrl: './slug-route-handler.component.html',
  styleUrls: ['./slug-route-handler.component.scss'],
  imports: [StaticPageComponent, NgIf, ArticlePageComponent],
})
export class SlugRouteHandlerComponent implements OnInit {
  staticPage: boolean;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data.subscribe((res) => {
      this.staticPage = !(res?.['articlePageData']?.article || res?.['articlePageData']?.data);
      this.cdr.detectChanges();
    });
  }
}
