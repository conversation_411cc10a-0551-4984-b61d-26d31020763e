import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { catchError, switchMap, takeUntil } from 'rxjs/operators';
import { Subject, throwError } from 'rxjs';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { AuthService, ManSimpleButtonComponent, SocialProvider } from '../../../shared';
import { HttpErrorResponse } from '@angular/common/http';
import { SecureApiService } from '../../../shared';
import { BackendFormErrors, KesmaFormControlComponent, markControlsTouched, nonWhitespaceOnlyValidator, usernameValidator } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-finalize-registration',
  templateUrl: './finalize-registration.component.html',
  styleUrls: ['./finalize-registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, ReactiveFormsModule, KesmaFormControlComponent, ManSimpleButtonComponent],
})
export class FinalizeRegistrationComponent implements OnInit, OnDestroy {
  formGroup: UntypedFormGroup;
  isLoading = true;
  error: string | null = null;

  isFormLoading = false;
  formError: string | null = null;

  showAppleInstructions = false;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly secureApiService: SecureApiService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      if (params['state'] && params['code']) {
        this.verifyOAuthCode(params['state'] as SocialProvider, params['code']);
      } else if (params['error'] && params['error'] === 'apple_register_need_email') {
        this.isLoading = false;
        this.error =
          'A folyamat megszakadt, mert az Apple regisztráció / bejelentkezés használatához engedélyeznie kell ' +
          'az email-címe használatát. Kérjük próbálja újra!';
        this.showAppleInstructions = true;
        this.cdr.detectChanges();
      } else {
        this.isLoading = false;
        this.error = 'A regisztráció / bejelentkezés folyamata megszakadt, kérjük próbálja újra!';
        this.cdr.detectChanges();
      }
    });
  }

  verifyOAuthCode(provider: SocialProvider, responseCode: string): void {
    this.authService.authenticateWithSocialProvider(provider, responseCode).subscribe({
      next: (isRegistrationFinished: boolean) => {
        if (isRegistrationFinished) {
          this.router.navigate(['/profil']);
        } else {
          this.isLoading = false;
          this.initForm();
          this.cdr.detectChanges();
        }
      },
      error: () => {
        this.isLoading = false;
        this.error = 'Hiba történt a külső forrásból kapott adatok vizsgálata során, kérjük próbálja újra!';
        this.cdr.detectChanges();
      },
    });
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      lastName: [null, [Validators.required, nonWhitespaceOnlyValidator]],
      firstName: [null, [Validators.required, nonWhitespaceOnlyValidator]],
      username: [null, [Validators.required, usernameValidator]],
      newsletter: [false],
      terms: [false, Validators.requiredTrue],
      marketing: [false],
    });
  }

  finalizeRegistration(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.formError = null;
    this.isFormLoading = true;

    this.secureApiService
      .finishRegister(this.formGroup.value)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User with the same username is already registered
              if (errorKey === 'userName' && !!value.errors) {
                if (value.errors.find((error) => error?.includes('100'))) {
                  this.formGroup.get('username')?.setErrors({ usernameMaxLength: true });
                } else {
                  this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                }
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.formError = 'Ismeretlen hiba!';
          }
          this.isFormLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.authService.isAuthenticated())
      )
      .subscribe(() => {
        this.router.navigate(['/profil']);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
