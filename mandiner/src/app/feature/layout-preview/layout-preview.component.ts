import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { LayoutElementContentConfiguration, LayoutElementRow } from '@trendency/kesma-ui';
import { BehaviorSubject, Subject, distinctUntilChanged, fromEvent, startWith, takeUntil, map } from 'rxjs';
import { UtilService } from '@trendency/kesma-core';
import { makeMobileLayout, searchLayoutContentElements } from '../../shared';
import { AsyncPipe, NgIf } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';

@Component({
  selector: 'app-layout-preview',
  templateUrl: './layout-preview.component.html',
  styleUrls: ['./layout-preview.component.scss'],
  imports: [AsyncPipe, NgIf, LayoutComponent],
})
export class LayoutPreviewComponent implements OnInit, OnD<PERSON>roy {
  destroy$ = new Subject<void>();
  layoutApiData: {
    struct: LayoutElementRow[];
    content: LayoutElementContentConfiguration[];
  };

  mobileLayout?: LayoutElementRow[];

  currentWindowWidth$ = fromEvent(window, 'resize').pipe(
    map(() => window.innerWidth),
    startWith(window.innerWidth)
  );
  isMobile$ = this.currentWindowWidth$.pipe(map((width: number) => width < 992));
  struct$ = new BehaviorSubject<LayoutElementRow[] | undefined>(undefined);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    const routeSnapshot: ActivatedRouteSnapshot = this.route.snapshot;
    this.layoutApiData = routeSnapshot.data['layoutData'];
    const { pageType } = routeSnapshot.queryParams;
    if (pageType === 'home' || pageType === 'homepage') {
      this.mobileLayout = [makeMobileLayout(searchLayoutContentElements(this.layoutApiData.struct))];
      if (!this.utilsService.isBrowser()) {
        //SSR
        this.struct$.next(this.layoutApiData.struct);
      } else {
        //Browser
        this.isMobile$.pipe(distinctUntilChanged(), takeUntil(this.destroy$)).subscribe((isMobile: boolean) => {
          if (isMobile && this.mobileLayout && this.mobileLayout?.length > 0) {
            this.struct$.next(this.mobileLayout);
          } else {
            this.struct$.next(this.layoutApiData.struct);
          }
        });
      }
    } else {
      this.struct$.next(this.layoutApiData.struct);
    }
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
