import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { BehaviorSubject, first, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage, User } from '@trendency/kesma-ui';
import {
  AuthService,
  createMandinerTitle,
  defaultMetaInfo,
  JournalDataWithArticles,
  JournalService,
  ManBreadcrumbComponent,
  ManSimpleButtonComponent,
  TranslatedDatePipe,
} from '../../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AsyncPipe, NgForOf, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { JournalRecommenderComponent } from '../../components/journal-recommender/journal-recommender.component';

const PAGE_SIZE = 12;

@Component({
  selector: 'app-journals-list',
  templateUrl: './journals-list.component.html',
  styleUrls: ['./journals-list.component.scss'],
  imports: [
    NgIf,
    AsyncPipe,
    ManBreadcrumbComponent,
    JournalRecommenderComponent,
    NgForOf,
    SlicePipe,
    NgTemplateOutlet,
    ManSimpleButtonComponent,
    RouterLink,
    TranslatedDatePipe,
  ],
})
export class JournalsListComponent implements OnInit {
  readonly data$: Observable<JournalDataWithArticles[]> = this.activatedRoute.data.pipe(
    map((data) => data['data'] as ApiResult<JournalDataWithArticles[], ApiResponseMetaList>),
    tap((data) => {
      this._rowMax = (data.meta?.limitable?.rowAllCount || 0) - PAGE_SIZE + 1; // 13 is the initial count
      this.hasMore$.next(this._rowFrom < this._rowMax);
    }),
    map((data) => data.data)
  );

  readonly moreArticles$ = new BehaviorSubject<JournalDataWithArticles[]>([]);
  readonly loading$ = new BehaviorSubject<boolean>(false);
  readonly hasMore$ = new BehaviorSubject<boolean>(false);

  private _rowFrom = 1;
  private _rowMax = 0;
  private currentUser: User | undefined;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly journalService: JournalService,
    private readonly seo: SeoService,
    private readonly authService: AuthService
  ) {}

  ngOnInit(): void {
    this.setMetaData();

    this.user$.subscribe((user) => {
      this.currentUser = user;
    });
  }

  loadMore(): void {
    if (this._rowFrom >= this._rowMax) {
      return;
    }

    this._rowFrom += PAGE_SIZE;
    this.hasMore$.next(this._rowFrom < this._rowMax);
    this.loading$.next(true);
    this.journalService
      .getJournalListData(this._rowFrom, PAGE_SIZE)
      .pipe(first())
      .subscribe((data) => {
        this.moreArticles$.next([...this.moreArticles$.value, ...data]);
        this.loading$.next(false);
      });
  }

  subscribe(): void {
    this.journalService.subscribeClicked();
  }

  pdfClicked(item: JournalDataWithArticles): void {
    this.journalService.pdfClicked(this.currentUser, item);
  }

  get user$(): Observable<User | undefined> {
    return this.authService.currentUserSubject.asObservable();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('hetilap');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Hetilap');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
