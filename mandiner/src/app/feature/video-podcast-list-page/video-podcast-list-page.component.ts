import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { ArticleCard, BreadcrumbItem, createCanonicalUrlForPageablePage, mapBackendArticleDataToArticleCardWithHideThumbnail } from '@trendency/kesma-ui';
import { BackendArticleSearchResult } from '@trendency/kesma-ui/lib/definitions';
import { Observable, Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import {
  ApiService,
  ArticleCardType,
  createMandinerTitle,
  defaultMetaInfo,
  FilterValues,
  makeBreadcrumbSchema,
  ManArticleCardComponent,
  ManBreadcrumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  SearchFilterDefinitions,
} from '../../shared';
import { injectMandinerEnvironment } from '../../../environments/environment.definitions';

const MAX_RESULTS_PER_PAGE = 10;

@Component({
  selector: 'app-video-podcast-list-page',
  templateUrl: './video-podcast-list-page.component.html',
  styleUrls: ['./video-podcast-list-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    AsyncPipe,
    ManBreadcrumbComponent,
    ManSearchFilterComponent,
    ManArticleCardComponent,
    ManSimpleButtonComponent,
    StrossleAdvertComponent,
  ],
})
export class VideoPodcastListPageComponent implements OnInit, OnDestroy {
  private readonly environment = injectMandinerEnvironment();
  breadcrumbItems?: BreadcrumbItem[];

  readonly ArticleCardType = ArticleCardType;

  private readonly unsubscribe$: Subject<boolean> = new Subject();

  results: ArticleCard[] = [];
  isLoading = false;
  page = 0;
  rowAllCount = 0;
  filterValues: FilterValues = {};
  MAX_RESULTS_PER_PAGE = MAX_RESULTS_PER_PAGE;
  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((res) => res['searchData']));

  constructor(
    private readonly apiService: ApiService,
    private readonly cd: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly schemaService: SchemaOrgService,
    private readonly route: ActivatedRoute,
    private readonly searchFilterDataService: SearchFilterDataService
  ) {}

  ngOnInit(): void {
    this.setMetaData();
    this.loadResults();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.MAX_RESULTS_PER_PAGE * (this.page + 1) < this.rowAllCount;
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.results = [];
    this.page = 0;
    this.isLoading = true;
    this.loadResults();
  }

  loadMoreResults(): void {
    if (this.canLoadMore) {
      this.page += 1;
      this.loadResults();
    }
  }

  loadResults(): void {
    const searchQuery = this.filterValues?.keyword ?? '';
    const seachDateRange = this.searchFilterDataService.setSearchQuery(this.filterValues);
    this.apiService
      .getVideoTypeArticles(
        searchQuery,
        this.page,
        this.MAX_RESULTS_PER_PAGE,
        this.filterValues.sort,
        seachDateRange.fromDate,
        seachDateRange.toDate,
        this.filterValues.contentTypes,
        this.filterValues.columns
      )
      .subscribe((result) => {
        this.rowAllCount = result.meta['limitable'].rowAllCount as number;
        this.results = this.results.concat(
          result.data
            .map((data) => mapBackendArticleDataToArticleCardWithHideThumbnail(data as unknown as BackendArticleSearchResult))
            .map((data) => ({
              ...data,
              publishDate: new Date(data.publishDate as Date) as unknown as string,
            }))
        );
        this.isLoading = false;
        this.setMetaData();
        this.cd.detectChanges();
      });
  }

  private setMetaData(): void {
    const title = createMandinerTitle(this.filterValues?.keyword ? `Videók: ${this.filterValues.keyword}` : 'Videók');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.breadcrumbItems = [
      {
        label: 'Videó',
        url: ['/', 'video'],
      },
    ];

    const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems, this.environment);
    this.schemaService.insertSchema(breadcrumbSchema);

    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('video');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
