@use 'shared' as *;

:host {
  display: block;

  ::ng-deep man-wysiwyg-box {
    .block-content > .raw-html-embed {
      min-height: auto !important;
    }
  }
}

@mixin articleCardSeparator {
  content: ' ';
  display: block;
  width: 100%;
  height: 1px;
  margin-top: 15px;
  background: var(--kui-gray-50);
}
::ng-deep .content-element {
  & > man-article-card:not(:only-child):not(:last-child):not(.manual-article-related):after {
    @include articleCardSeparator();
  }
}
::ng-deep .content-element {
  & > man-article-card:not(.manual-article-related):after {
    @include articleCardSeparator();
  }
}

::ng-deep .content-element {
  & man-article-card.has-related-article.article-card,
  & man-article-card.manual-article-related.article-card {
    margin-bottom: 10px;
  }

  & man-article-card.manual-article-related.article-card.last:after {
    @include articleCardSeparator();
  }
}

/**
  Remove top margins between manual opinion cards to leave no space between them.
 */
::ng-deep .content-type-manual-opinion + ::ng-deep .content-type-manual-opinion {
  margin-top: -20px;
}
