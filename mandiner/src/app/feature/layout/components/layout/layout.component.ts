import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, <PERSON><PERSON><PERSON>, SlicePipe } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  ArticleCard,
  BlockWrapperTemplateData,
  BreakingNews,
  HtmlEmbedComponent,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElement,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  NEWSLETTER_COMPONENT_TYPE,
  OlimpicPortalEnum,
  PortalConfigSetting,
  provideLayoutDataExtractors,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { BehaviorSubject, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { OlimpiaService } from 'src/app/shared/services/olimpia.service';
import {
  ArticleCardType,
  BreakingType,
  ManArticleCardComponent,
  ManBlockTitleRowComponent,
  ManBlockTitleSidebarComponent,
  ManBreakingStripComponent,
  ManCategoryStepperComponent,
  ManDossierListComponent,
  ManDossierRepeaterCardComponent,
  ManDossierRepeaterComponent,
  ManGalleryListComponent,
  ManNewsFeedComponent,
  ManNewsletterDiverterCardComponent,
  ManOpinionCardComponent,
  ManOpinionHeaderComponent,
  ManOpinionLayoutComponent,
  ManPdfBoxComponent,
  ManQuizComponent,
  ManSponsoredContentComponent,
  ManTrendingTagsComponent,
  ManVideoListComponent,
  ManVotingComponent,
  ManWeeklyNewspaperComponent,
  ManWysiwygBoxComponent,
  NewsletterDiverterCardType,
  PortalConfigService,
  SocialInteractionEvent,
  SocialInteractionEventType,
} from '../../../../shared';
import { MandinerBrandingBoxExComponent } from '../../../../shared/components/branding-box-ex/branding-box-ex.component';
import { FreshNewsAdapterComponent } from '../../../../shared/components/fresh-news-adapter/fresh-news-adapter.component';
import { OpinionTypeResponse } from '../../../opinions/api/opinions.definitions';
import { OpinionsService } from '../../../opinions/api/opinions.service';
import { backendOpinionToArticleCard } from '../../../opinions/api/opinions.utils';
import { MANDINER_EXTRACTORS_CONFIG } from '../../data-extractors/extractor.config';

const MANDINER_REALESTATE_URL =
  'https://www.ingatlanbazar.hu/api/property-search' +
  '?property_location=6,1000000004,1000000005,1000000006,1000000007' +
  '&amp;;property_newbuildonly=on' +
  '&amp;property__2=3_2';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ManBlockTitleRowComponent,
    NgIf,
    ManBlockTitleSidebarComponent,
    MandinerBrandingBoxExComponent,
    ManArticleCardComponent,
    ManBreakingStripComponent,
    ManDossierListComponent,
    ManGalleryListComponent,
    ManWeeklyNewspaperComponent,
    ManNewsFeedComponent,
    ManNewsletterDiverterCardComponent,
    SlicePipe,
    NgForOf,
    ManOpinionCardComponent,
    ManSponsoredContentComponent,
    ManCategoryStepperComponent,
    ManVotingComponent,
    ManTrendingTagsComponent,
    ManOpinionHeaderComponent,
    FreshNewsAdapterComponent,
    ManOpinionLayoutComponent,
    AsyncPipe,
    ManWysiwygBoxComponent,
    HtmlEmbedComponent,
    RealEstateBazaarSearchBlockComponent,
    ManQuizComponent,
    ManDossierRepeaterComponent,
    ManDossierRepeaterCardComponent,
    RealEstateBazaarBlockComponent,
    ManPdfBoxComponent,
    ManVideoListComponent,
    KesmaLayoutComponent,
  ],
  providers: [provideLayoutDataExtractors(MANDINER_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent {
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageType = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;
  readonly NewsletterDiverterCardType = NewsletterDiverterCardType;
  readonly BreakingType = BreakingType;
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  public realEstateData: RealEstateBazaarData[] = [];
  opinionListData$ = new BehaviorSubject<ArticleCard[]>([]);
  readonly MANUAL_ARTICLE_RELATED_ARTICLE_STYLE = ArticleCardType.TitleMeta;
  ENABLE_FOOTBALL_EB_ELEMENTS = false;
  ENABLE_OLIMPIA_ELEMENTS = false;

  private extractedData: any = {};
  private realEstateDataLoading = false;
  private opinionListDataRequested = false;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    private readonly utilService: UtilService,
    public readonly httpClient: HttpClient,
    public readonly voteService: VoteService,
    public readonly opinionsService: OpinionsService,
    private readonly analyticsService: AnalyticsService,
    private readonly portalConfigService: PortalConfigService,
    public readonly olimpiaService: OlimpiaService
  ) {
    this.ENABLE_FOOTBALL_EB_ELEMENTS = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
    this.ENABLE_OLIMPIA_ELEMENTS = olimpiaService.isEnableOlympicsElements();
  }

  onSubscribeClicked(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
    window.open('/hirlevel-feliratkozas', '_blank');
  }

  getData(type: string, layoutElement: LayoutElement, extractor: any, index?: number): any {
    const id = type + layoutElement.id + index;
    if (!this.extractedData[id]) {
      this.extractedData[id] = index || index == 0 ? extractor[type]?.(layoutElement, index) : extractor[type]?.(layoutElement);
    }

    return this.extractedData[id];
  }

  onRealEstateInit(): void {
    if (this.utilService.isBrowser() && !this.realEstateDataLoading && this.realEstateData.length < 1) {
      this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading = true;
    this.httpClient
      .get<RealEstateBazaarBackendResponse>(MANDINER_REALESTATE_URL)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.utilService.log('HIBA: ingatlanbazár nem tölthető be! ', 'error');
          this.utilService.log({ message: error.message, details: error.error.message }, 'error');

          return of({ hits: [] });
        })
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        this.realEstateData = (data?.hits ?? []).map(mapRealEstateApiDataToRealEstateData);
        this.realEstateDataLoading = false;
        this.changeDetector.detectChanges();
      });
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    if (voteData.votedId || voteData.showResults) {
      return;
    }

    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        voteData.showResults = true;
        this.changeDetector.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  getOpinionListData(): BehaviorSubject<ArticleCard[]> {
    if (!this.opinionListDataRequested) {
      this.opinionListDataRequested = true;
      this.opinionsService
        .getOpinions(0, 20)
        .pipe(map((res: OpinionTypeResponse) => res?.data.map(backendOpinionToArticleCard)))
        .subscribe((opinionListData: ArticleCard[]) => {
          this.opinionListData$.next(opinionListData);
        });
    }

    return this.opinionListData$;
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.analyticsService.sendSocialInteraction({
      clickLink: $event.url ?? 'no data',
      clickText: $event.linkText ?? 'no data',
    });

    if ($event.event === SocialInteractionEventType.FacebookShare) {
      this.analyticsService.sendFacebookShare({
        clickLink: $event.url ?? 'no data',
        title: $event.title ?? 'no data',
        publishDate: $event.publishDate,
      });
    }
  }
}
