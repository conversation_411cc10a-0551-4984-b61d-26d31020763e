import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ReCaptchaV3Service } from 'ngx-captcha';
import {
  ApiService,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  createMandinerTitle,
  defaultMetaInfo,
  ManSimpleButtonComponent,
  SocialProvider,
} from '../../shared';
import { IMetaData, injectEnvironment, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { Observable } from 'rxjs';
import { createCanonicalUrlForPageablePage, KesmaFormControlComponent, markControlsTouched, passwordValidator } from '@trendency/kesma-ui';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, ReactiveFormsModule, KesmaFormControlComponent, ManSimpleButtonComponent, RouterLink],
})
export class LoginComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPassword = false;
  isLoading = false;
  error: string | null = null;
  SocialProvider = SocialProvider;
  allowedLoginMethods$: Observable<BackendAllowedLoginMethodsResponse> = this.apiService.getAllowedLoginMethods();
  private readonly environment = injectEnvironment();

  constructor(
    private readonly authService: AuthService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      emailOrUsername: [null, [Validators.required]],
      password: [null, [Validators.required, passwordValidator]],
      rememberMe: [false],
    });
  }

  login(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      this.environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_login',
      (recaptchaToken: string) => {
        this.authService.isRememberMeSubject.next(this.formGroup.controls['rememberMe'].value);
        this.authService.authenticate(this.formGroup.value, recaptchaToken).subscribe({
          next: () => {
            this.isLoading = false;
            this.cdr.detectChanges();
            const redirectUrl = this.route.snapshot.queryParams['redirect'] ?? this.storageService.getLocalStorageData('loginRedirectUrl');
            this.storageService.setLocalStorageData('loginRedirectUrl', null);
            if (redirectUrl) {
              this.router.navigate([redirectUrl]);
            } else {
              this.router.navigate(['/profil']);
            }
          },
          error: (err) => {
            if (err?.error?.data?.message === 'Email not verified') {
              this.error = 'Kérjük, erősítse meg regisztrációját az e-mail-ben kapott link segítségével!';
            } else {
              this.error = 'Hibás bejelentkezési adatok, kérem, próbálja újra!';
            }
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  loginWithSocialProvider(provider: SocialProvider): void {
    this.authService.isRememberMeSubject.next(this.formGroup.controls['rememberMe'].value);
    if (this.utilsService.isBrowser()) {
      this.document.location.href = this.authService.getSocialProviderAuthUrl(provider);
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('bejelentkezes');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createMandinerTitle('Bejelentkezés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
