@use 'shared' as *;

:host {
  display: block;
  margin: 30px 0;

  man-breadcrumb {
    margin-bottom: 20px;
  }

  man-search-filter {
    margin-bottom: 50px;

    &::ng-deep {
      .search-bar {
        margin-bottom: 0 !important;
      }
    }

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;
    }
  }

  .tag-title {
    color: var(--kui-orange-600);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--kui-gray-100);
  }

  .tag-results-meta {
    margin-bottom: 50px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;
    }
  }

  .tag-results-counter {
    font-size: 14px;
    line-height: 19.07px;
    margin-bottom: 10px;
  }

  @include media-breakpoint-down(sm) {
    man-simple-button {
      width: 100%;
    }
  }

  @include media-breakpoint-down(md) {
    aside {
      display: none;
    }
  }
}
