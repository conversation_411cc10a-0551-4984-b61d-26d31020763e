import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, Optional } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, Tag } from '@trendency/kesma-ui';
import { Response } from 'express';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import { StrossleAdvertComponent } from 'src/app/shared/components/strossle-advert/strossle-advert.component';
import { SearchFilterDataService } from 'src/app/shared/services/search-filter-data.service';
import {
  ArticleCardType,
  createMandinerTitle,
  FilterValues,
  ManArticleCardComponent,
  ManBreadcrumbComponent,
  ManSearchFilterComponent,
  ManSimpleButtonComponent,
  SearchFilterDefinitions,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { TagsPageService } from './tags-page.service';
import { searchResultToArticleCard } from './tags-page.utils';
import { injectMandinerEnvironment } from '../../../environments/environment.definitions';

const MAX_RESULTS_PER_PAGE = 20;

@Component({
  selector: 'app-tags-page',
  templateUrl: './tags-page.component.html',
  styleUrls: ['./tags-page.component.scss'],
  imports: [
    ManBreadcrumbComponent,
    NgIf,
    ManSearchFilterComponent,
    AsyncPipe,
    ManArticleCardComponent,
    ManSimpleButtonComponent,
    SidebarComponent,
    NgForOf,
    StrossleAdvertComponent,
  ],
})
export class TagsPageComponent implements OnInit, OnDestroy {
  readonly ArticleCardType = ArticleCardType;
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  private readonly environment = injectMandinerEnvironment();

  articles: ArticleCard[] = [];
  tag: Tag | undefined;

  MAX_RESULTS_PER_PAGE = MAX_RESULTS_PER_PAGE;
  isLoading = false;
  page = 0;
  rowAllCount = 0;
  filterValues: FilterValues = {};
  searchFilterColumns$: Observable<SearchFilterDefinitions> = this.route.data.pipe(map((data) => data['tag']['searchFilterData']));

  sidebarExcludedIds: string[] = [];

  constructor(
    private readonly tagsPageService: TagsPageService,
    private readonly utilsService: UtilService,
    private readonly cdr: ChangeDetectorRef,
    private readonly seoService: SeoService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly route: ActivatedRoute,
    private readonly searchFilterDataService: SearchFilterDataService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  ngOnInit(): void {
    this.subscribeToResolverDataChange();
  }

  private subscribeToResolverDataChange(): void {
    this.activatedRoute.data.pipe(takeUntil(this.unsubscribe$)).subscribe((res) => {
      this.tag = res?.['tag']['tags'];
      this.getTagArticles();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.MAX_RESULTS_PER_PAGE * (this.page + 1) < this.rowAllCount;
  }

  onFilter(filterValues: FilterValues): void {
    this.filterValues = filterValues;
    this.articles = [];
    this.page = 0;
    this.getTagArticles();
  }

  loadMoreResults(): void {
    if (this.canLoadMore) {
      this.page += 1;
      this.getTagArticles();
    }
  }

  getTagArticles(): void {
    if (!this.tag) {
      return;
    }

    this.isLoading = true;
    const searchDateRange = this.searchFilterDataService.setSearchQuery(this.filterValues);
    this.tagsPageService
      .searchArticleByTags(
        [this.tag.slug],
        this.page,
        MAX_RESULTS_PER_PAGE,
        this.filterValues.sort,
        searchDateRange.fromDate,
        searchDateRange.toDate,
        this.filterValues?.contentTypes ?? [],
        this.filterValues?.columns ?? []
      )
      .pipe(
        tap((res) => {
          if (res.data.length === 0) {
            const meta = res.meta;

            // if redirect needed (merged tag)
            if (meta?.['redirect']?.tag?.slug) {
              const redirectUrl = `${this.environment.siteUrl}/cimke/${meta?.['redirect']?.tag?.slug}`;

              // client side (basic redirection)
              if (this.utilsService.isBrowser()) {
                window.location.href = redirectUrl;
                return;
              }

              // server side (SSR - 301 redirect with express js response injector)
              this.response.status(301);
              this.response.setHeader('location', redirectUrl);
            }
          }
        })
      )
      .subscribe((res) => {
        this.articles = this.articles.concat(res.data.map((sr) => searchResultToArticleCard(sr)));
        this.rowAllCount = res.meta?.limitable?.rowAllCount || 0;
        this.isLoading = false;
        this.setPageMeta();
        this.populateSidebarExcludedIds();
        this.cdr.detectChanges();
      });
  }

  private populateSidebarExcludedIds(): void {
    this.sidebarExcludedIds = this.articles.map((item) => item.id!);
  }

  private setPageMeta(): void {
    let metaData: any = this.composePageMeta('', ''); // need a default otherwise an empty search will raise an error

    if (this.tag) {
      const title = createMandinerTitle(`${this.capitalize(this.tag.title as any)} címke oldal`);
      const desc = `${title}` || '';
      metaData = this.composePageMeta(title, desc);
    }
    const canonical = createCanonicalUrlForPageablePage('cimke', this.activatedRoute.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData(metaData);
  }

  private composePageMeta(title: string, description: string): any {
    return {
      title: title ?? '',
      description: description ?? '',
      ogTitle: title ?? '',
      ogDescription: description ?? '',
      twitterTitle: title ?? '',
      twitterDescription: description ?? '',
    } as any;
  }

  private capitalize(txt: string): string {
    return txt.charAt(0).toUpperCase() + txt.slice(1);
  }
}
