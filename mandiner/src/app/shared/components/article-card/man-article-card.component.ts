import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostBinding, Input, OnInit, Output, ViewChild } from '@angular/core';
import { UtilService, PublishDatePipe } from '@trendency/kesma-core';
import {
  ArticleCard,
  ArticleCardWithSocial,
  BaseComponent,
  buildArticleUrl,
  buildColumnUrl,
  buildRegionUrl,
  buildTagUrl,
  buildUrl,
  ExternalRecommendation,
  MinuteToMinuteState,
  toBool,
  FocusPointDirective,
} from '@trendency/kesma-ui';
import { BreakingType } from '../breaking-strip/man-breaking-strip.definitions';
import { LikeButtons } from '../social-interactions/man-social-interactions.definitions';
import { ArticleCardType } from './man-article-card.types';
import { DATETIME_FORMAT, TIME_FORMAT } from '../../constants';
import { SocialInteractionEvent } from '../social-share-modal/man-social-share-modal.definitions';
import { FormatPipeModule } from 'ngx-date-fns';
import { ManSocialInteractionsComponent } from '../social-interactions/man-social-interactions.component';
import { ManBreakingStripComponent } from '../breaking-strip/man-breaking-strip.component';
import { RouterLink } from '@angular/router';
import { NgIf, NgClass, NgSwitch, NgSwitchCase, NgTemplateOutlet, DatePipe } from '@angular/common';

type TagType = 'mPlus' | 'video' | 'gallery' | 'podcast' | 'minuteToMinute' | 'dossier' | 'adultsOnly';
export type TagList = Partial<Record<TagType, number>>;

@Component({
  selector: 'man-article-card',
  templateUrl: './man-article-card.component.html',
  styleUrls: ['./man-article-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgClass,
    NgSwitch,
    NgSwitchCase,
    RouterLink,
    NgTemplateOutlet,
    FocusPointDirective,
    ManBreakingStripComponent,
    ManSocialInteractionsComponent,
    DatePipe,
    PublishDatePipe,
    FormatPipeModule,
  ],
})
export class ManArticleCardComponent extends BaseComponent<ArticleCard> implements OnInit {
  @Input() set styleID(styleID: ArticleCardType) {
    this._type = styleID;
    this.hostClass = `article-card style-${ArticleCardType[styleID]}`;
  }

  @Input() socialInteractions?: LikeButtons;
  @Input() isSidebar = false;
  @Input() isTagVisible = true;
  @Input() isCategoryVisible = false;
  @Input() isExcerptVisible = true;
  @Input() areSocialInteractionsVisible = true;
  @Input() canHaveVideo = false;
  @Input() hasVideo = false;
  @Input() isMplus? = false;
  @Input() isLive = false;
  @Input() @HostBinding('class.wide') isWide = false;
  @Input() hideDate = false;
  @Input() isMaxWidth = false;
  @Input() highlight: boolean = false;
  @Input() caption: boolean = false;
  @Input() italic: boolean = false;
  @Input() isManual = false;
  @Input() fontSize?: number;
  @Input() set hasExternalUrl(hasExternalUrl: boolean) {
    if (hasExternalUrl) {
      this._hasExternalUrl = hasExternalUrl;

      if (this.data) {
        this.articleLink = this.setArticleLink();
      }
    }
  }
  get hasExternalUrl(): boolean {
    return this._hasExternalUrl;
  }
  @Output() bookmarkClicked = new EventEmitter<void>();
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  get social(): LikeButtons {
    const articleData = this.articleCard as ArticleCardWithSocial | undefined;
    return {
      like: articleData?.likeCount ?? 0,
      dislike: articleData?.dislikeCount ?? 0,
      comment: articleData?.commentCount ?? 0,
      areReactionsHidden: articleData?.isLikesAndDislikesDisabled ?? false,
      areCommentsHidden: articleData?.isCommentsDisabled ?? false,
      ...(this.socialInteractions ?? {}), // Social interactions can be overridden if passed in manually
    };
  }

  @HostBinding('class') hostClass = '';

  @ViewChild('article', { read: ElementRef }) articleContainer?: ElementRef;
  @ViewChild('tagsTemplate', { read: ElementRef }) tagsContainer?: ElementRef;

  readonly DATETIME_FORMAT = DATETIME_FORMAT;
  readonly TIME_FORMAT = TIME_FORMAT;
  readonly BreakingType = BreakingType;
  readonly ArticleCardType = ArticleCardType;

  private _type = ArticleCardType.ImgRightTitleLeadDateMeta;

  displayedTagMetaType?: 'label' | 'region' | 'tag' | 'columnTitle';
  displayedThumbnailUrl? = '';
  displayedThumbnailAlt? = '';
  categoryLink: string[] = [];
  regionLink: string[] = [];
  articleLink: string[] = [];
  tagLink: string[] = [];
  publishDate?: Date;
  isMobile?: boolean;
  _hasExternalUrl = false;

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    private readonly utilsService: UtilService
  ) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.isMobile = this.utilsService.isBrowser() ? window.innerWidth <= 768 : false;
  }

  get styleID(): ArticleCardType {
    return this._type;
  }

  get articleCard(): ArticleCard | undefined {
    return this.data as ArticleCard;
  }

  get externalRecommendation(): ExternalRecommendation | undefined {
    return this.data as ExternalRecommendation;
  }

  get hasMinuteToMinute(): boolean {
    return !!(this.data as ArticleCard)?.minuteToMinute && (this.data as ArticleCard)?.minuteToMinute !== MinuteToMinuteState.NOT;
  }

  get fontSizePx(): string | undefined {
    return this.fontSize ? `${this.fontSize}px` : undefined;
  }

  get fontSizeClass(): string {
    return this.fontSize ? `fs-${this.fontSize}` : '';
  }

  getTags(): { items: TagList; totalLength: number } {
    const items: TagList = {
      ...(toBool(this.isMplus) ? { mPlus: 1 } : {}),
      ...(toBool(this.data?.isVideoType) || this.hasVideo ? { video: 2 } : {}),
      ...(toBool(this.data?.hasGallery) ? { gallery: 2 } : {}),
      ...(toBool(this.data?.isPodcastType) ? { podcast: 2 } : {}),
      ...(toBool(this.hasMinuteToMinute) ? { minuteToMinute: 3 } : {}),
      ...(toBool(this.data?.hasDossiers) ? { dossier: 2 } : {}),
      ...(toBool(this.data?.isAdultsOnly) ? { adultsOnly: 1 } : {}),
    };
    const itemsLength = Object.values(items).length;
    return {
      items,
      totalLength: itemsLength > 0 ? Object.values(items).reduce((acc: number, value: number) => acc + value) : 0,
    };
  }

  protected override setProperties(): void {
    super.setProperties();

    // On article cards that appear in the sidebar we want to use the secondary image with 4:3 aspect ratio.
    // You can add other conditions for displaying the thumbnail by modifying the lines above.
    this.displayedThumbnailUrl =
      this.styleID === ArticleCardType.ImgTagTitleLeadDateMeta
        ? // eslint-disable-next-line max-len
          // If SmallSidedImgTagTitle use secondary thumbnail 4:3 cropped version, if no 4:3 version, then use secondary thumbnail or the placeholder file.
          this.articleCard?.secondaryThumbnail?.url43AspectRatio || this.articleCard?.secondaryThumbnail?.url || '/assets/images/placeholder.svg'
        : // Every other case just use the regular thumbnail.
          this.articleCard?.thumbnail?.url;

    // This condition also applies for the alt attribute.
    this.displayedThumbnailAlt =
      this.styleID === ArticleCardType.ImgTagTitleLeadDateMeta ? this.articleCard?.secondaryThumbnail?.alt : this.articleCard?.thumbnail?.alt;

    this.categoryLink = this.articleCard ? buildColumnUrl(this.articleCard) : [];
    this.regionLink = this.articleCard ? buildRegionUrl(this.articleCard, 0) : [];
    this.articleLink = this.setArticleLink();
    this.tagLink = this.articleCard ? buildTagUrl(this.articleCard, 0) : [];

    //Priority list: override tag, 1st tag, category

    let text = this.articleCard?.columnTitle;
    let url: string[] | string = this.categoryLink;
    this.displayedTagMetaType = 'columnTitle';

    if (this.articleCard?.tags?.[0]?.title) {
      this.displayedTagMetaType = 'tag';
      text = this.articleCard?.tags?.[0]?.title;
      url = this.tagLink;
    }

    if (this.articleCard?.label?.text) {
      this.displayedTagMetaType = 'label';
      text = this.articleCard.label.text;
      url = this.articleCard?.label.url && this.articleCard?.label.url !== '' ? buildUrl(this.articleCard?.label.url as string, this.articleCard) : '';
    }

    if (!this.externalRecommendation) {
      this.setData({
        ...this.articleCard,
        label: {
          text,
          url,
        },
      } as ArticleCard);
    }

    this.publishDate = (this.articleCard?.publishDate ?? '') as Date;
    this.changeDetector.detectChanges();
  }

  setArticleLink(): string[] {
    return this.articleCard ? (this.hasExternalUrl ? ['/', this.articleCard.url || ''] : buildArticleUrl(this.articleCard)) : [];
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.socialInteraction.emit({
      ...$event,
      publishDate: this.publishDate,
    });
  }
}
