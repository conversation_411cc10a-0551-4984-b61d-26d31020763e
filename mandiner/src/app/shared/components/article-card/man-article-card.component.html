<article
  #article
  *ngIf="articleCard"
  [class.has-video]="canHaveVideo && (hasVideo || articleCard?.isVideoType)"
  [class.is-manual]="isManual"
  [class]="fontSizeClass"
  [ngClass]="{
    highlight,
    caption,
    italic,
  }"
>
  <ng-container [ngSwitch]="styleID">
    <!-- STYLE ImgRightTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgRightTitleLeadDateMeta">
      <div class="article-card-left">
        <a [routerLink]="articleLink" class="article-card-link">
          <div class="article-card-title-container">
            <h2 [style.font-size]="fontSizePx" class="article-card-title">
              {{ articleCard?.title }}
            </h2>
            <div class="article-card-img-sm">
              <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
            </div>
          </div>
        </a>
        <div class="flex-container">
          <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        </div>

        <ng-container *ngIf="articleCard.foundationTagSlug">
          <a [routerLink]="'/cimke/' + articleCard.foundationTagSlug" class="article-card-category">
            {{ articleCard?.foundationTagTitle }}
          </a>
        </ng-container>

        <ng-container *ngIf="isCategoryVisible">
          <a [routerLink]="categoryLink" class="article-card-category">
            {{ articleCard?.category?.name }}
          </a>
        </ng-container>

        <p class="article-card-lead">
          {{ articleCard?.lead }}
        </p>

        <div *ngIf="!hideDate" class="article-card-footer">
          <div class="article-card-publish-date">
            {{ publishDate | publishDate: DATETIME_FORMAT }}
          </div>
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
      <a [routerLink]="articleLink" class="article-card-img-lg">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
      </a>
      <ng-template #videoThumbnail>
        <figure class="article-card-figure article-card-video">
          <ng-container *ngIf="isLive || articleCard?.label?.text">
            <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: isMobile }"></ng-container>
          </ng-container>
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [alt]="articleCard?.thumbnail?.alt || ''"
            [displayedAspectRatio]="{ desktop: '16:9', mobile: '1:1' }"
            [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
            class="article-card-figure-image"
            loading="lazy"
          />
          <i class="icon icon-mandiner-play"></i>
        </figure>
      </ng-template>
      <ng-template #imgThumbnail>
        <figure class="article-card-figure">
          <ng-container *ngIf="isLive || articleCard?.label?.text">
            <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: isMobile }"></ng-container>
          </ng-container>
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [alt]="articleCard?.thumbnail?.alt || ''"
            [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
            [displayedAspectRatio]="{ desktop: '16:9', mobile: '1:1' }"
            class="article-card-figure-image"
            loading="lazy"
          />
        </figure>
      </ng-template>
    </ng-container>

    <!-- STYLE ImgRightTitleLead-->
    <ng-container *ngSwitchCase="ArticleCardType.ImgRightTitleLead">
      <div class="article-card-left">
        <a [routerLink]="articleLink" class="article-card-link">
          <ng-container *ngIf="isLive || articleCard.label?.text">
            <ng-container *ngTemplateOutlet="label; context: { style: 'gold' }"></ng-container>
          </ng-container>

          <div class="article-card-title-container">
            <h2 [style.font-size]="fontSizePx" class="article-card-title">
              {{ articleCard?.title }}
            </h2>
            <div class="article-card-img-sm">
              <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
            </div>
          </div>
        </a>
        <div class="flex-container">
          <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        </div>

        <ng-container *ngIf="isCategoryVisible">
          <a [routerLink]="categoryLink" class="article-card-category">
            {{ articleCard?.category?.name }}
          </a>
        </ng-container>

        <p class="article-card-lead">
          {{ articleCard?.lead }}
        </p>
      </div>
      <a [routerLink]="articleLink" class="article-card-img-lg">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
      </a>
      <ng-template #videoThumbnail>
        <figure class="article-card-figure article-card-video">
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [alt]="articleCard?.thumbnail?.alt || ''"
            [displayedAspectRatio]="{ desktop: '16:9', mobile: '1:1' }"
            [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
            class="article-card-figure-image"
            loading="lazy"
          />
          <i class="icon icon-mandiner-play"></i>
        </figure>
      </ng-template>
      <ng-template #imgThumbnail>
        <figure class="article-card-figure">
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [alt]="articleCard?.thumbnail?.alt || ''"
            [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
            [displayedAspectRatio]="{ desktop: '16:9', mobile: '1:1' }"
            class="article-card-figure-image"
            loading="lazy"
          />
        </figure>
      </ng-template>
    </ng-container>

    <!-- STYLE ImgSidedTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgSidedTitleLeadDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <div class="article-card-header">
          <h2 [style.font-size]="fontSizePx" class="article-card-title">
            {{ articleCard?.title }}
          </h2>
          <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
          <ng-template #videoThumbnail>
            <figure class="article-card-figure article-card-video">
              <ng-container *ngIf="isLive || articleCard?.label?.text">
                <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: true }"></ng-container>
              </ng-container>
              <img
                withFocusPoint
                [data]="articleCard?.thumbnailFocusedImages"
                [alt]="articleCard?.thumbnail?.alt || ''"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
                class="article-card-figure-image"
                loading="lazy"
              />
              <i class="icon icon-mandiner-play"></i>
            </figure>
          </ng-template>
          <ng-template #imgThumbnail>
            <figure class="article-card-figure">
              <ng-container *ngIf="isLive || articleCard?.label?.text">
                <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: true }"></ng-container>
              </ng-container>
              <img
                withFocusPoint
                [data]="articleCard?.thumbnailFocusedImages"
                [alt]="articleCard?.thumbnail?.alt || ''"
                [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
                class="article-card-figure-image"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                loading="lazy"
              />
            </figure>
          </ng-template>
        </div>
      </a>
      <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | dfnsFormat: DATETIME_FORMAT }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ImgTagTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgTagTitleLeadDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
        <ng-template #videoThumbnail>
          <figure class="article-card-figure article-card-video">
            <ng-container *ngIf="isLive || articleCard.label?.text">
              <ng-container *ngTemplateOutlet="label; context: { style: 'white' }"></ng-container>
            </ng-container>
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
            <i class="icon icon-mandiner-play"></i>
          </figure>
        </ng-template>
        <ng-template #imgThumbnail>
          <figure class="article-card-figure">
            <ng-container *ngIf="isLive || articleCard.label?.text">
              <ng-container *ngTemplateOutlet="label; context: { style: 'white' }"></ng-container>
            </ng-container>
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
          </figure>
        </ng-template>
        <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        <h2 [style.font-size]="fontSizePx" class="article-card-title">
          {{ articleCard?.title }}
        </h2>
      </a>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate: 'yyyy. LLLL d. HH:mm' }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ImgTitleDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgTitleDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
        <ng-template #videoThumbnail>
          <figure class="article-card-figure article-card-video">
            <ng-container *ngIf="isLive || articleCard.label?.text">
              <ng-container *ngTemplateOutlet="label; context: { style: 'white' }"></ng-container>
            </ng-container>
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
            <i class="icon icon-mandiner-play"></i>
          </figure>
        </ng-template>
        <ng-template #imgThumbnail>
          <figure class="article-card-figure">
            <ng-container *ngIf="isLive || articleCard.label?.text">
              <ng-container *ngTemplateOutlet="label; context: { style: 'white' }"></ng-container>
            </ng-container>
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
          </figure>
        </ng-template>
        <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        <h2 [style.font-size]="fontSizePx" class="article-card-title">
          {{ articleCard?.title }}
        </h2>
      </a>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE BlackBgImgTitleMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.BlackBgImgTitleMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
        <ng-template #videoThumbnail>
          <figure class="article-card-figure article-card-video">
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
            <i class="icon icon-mandiner-play"></i>
          </figure>
        </ng-template>
        <ng-template #imgThumbnail>
          <figure class="article-card-figure">
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
          </figure>
        </ng-template>

        <h2 [style.font-size]="fontSizePx" class="article-card-title">
          {{ articleCard?.title }}
        </h2>
      </a>
      <div class="article-card-footer">
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate; context: { $implicit: true }"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ImgTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgTitleLeadDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
        <ng-template #videoThumbnail>
          <figure class="article-card-figure article-card-video">
            <ng-container *ngIf="isLive || articleCard.label?.text">
              <ng-container *ngTemplateOutlet="label; context: { style: 'white' }"></ng-container>
            </ng-container>
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
            <i class="icon icon-mandiner-play"></i>
          </figure>
        </ng-template>
        <ng-template #imgThumbnail>
          <figure class="article-card-figure">
            <ng-container *ngIf="isLive || articleCard.label?.text">
              <ng-container *ngTemplateOutlet="label; context: { style: 'white' }"></ng-container>
            </ng-container>
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-figure-image"
              loading="lazy"
            />
          </figure>
        </ng-template>
        <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        <h2 [style.font-size]="fontSizePx" class="article-card-title">
          {{ articleCard?.title }}
        </h2>
      </a>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ItalicTitleDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ItalicTitleDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <h2 [style.font-size]="fontSizePx" class="article-card-title">„{{ articleCard?.title }}”</h2>
      </a>
      <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      <ng-container *ngIf="isLive || articleCard.label?.text">
        <ng-container *ngTemplateOutlet="label; context: { style: 'gold' }"></ng-container>
      </ng-container>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ItalicTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ItalicTitleLeadDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <h2 [style.font-size]="fontSizePx" class="article-card-title">„{{ articleCard?.title }}”</h2>
      </a>
      <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      <ng-container *ngIf="isLive || articleCard.label?.text">
        <ng-container *ngTemplateOutlet="label; context: { style: 'gold' }"></ng-container>
      </ng-container>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE TitleDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.TitleDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <h2 [style.font-size]="fontSizePx" class="article-card-title">
          {{ articleCard?.title }}
        </h2>
      </a>
      <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      <ng-container *ngIf="isLive || articleCard.label?.text">
        <ng-container *ngTemplateOutlet="label; context: { style: 'gold' }"></ng-container>
      </ng-container>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE TitleLeadDateMeta, PodcastTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.PodcastTitleLeadDateMeta" [ngTemplateOutlet]="titleLeadDateMeta"></ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.TitleLeadDateMeta" [ngTemplateOutlet]="titleLeadDateMeta"></ng-container>

    <ng-template #titleLeadDateMeta>
      <ng-container *ngIf="!hasExternalUrl; else externalUrl">
        <a [routerLink]="articleLink" class="article-card-link">
          <h2 [style.font-size]="fontSizePx" class="article-card-title">
            {{ articleCard?.title }}
          </h2>
        </a>
      </ng-container>
      <ng-container *ngIf="styleID === ArticleCardType.PodcastTitleLeadDateMeta">
        <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      </ng-container>
      <ng-template #externalUrl>
        <!-- At this moment we don't need to add target to the link because externalURL is used only in case of ZOE articles which point to the current domain! -->
        <a [href]="articleCard?.url" class="article-card-link">
          <h2 [style.font-size]="fontSizePx" class="article-card-title">
            {{ articleCard?.title }}
          </h2>
        </a>
      </ng-template>

      <ng-container *ngIf="styleID !== ArticleCardType.PodcastTitleLeadDateMeta">
        <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        <ng-container *ngIf="isLive || articleCard.label?.text">
          <ng-container *ngTemplateOutlet="label; context: { style: 'gold' }"></ng-container>
        </ng-container>
      </ng-container>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | dfnsFormat: DATETIME_FORMAT }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-template>

    <!-- STYLE UpperTitleLeadDateMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.UpperTitleLeadDateMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <h2 [style.font-size]="fontSizePx" class="article-card-title">
          {{ articleCard?.title }}
        </h2>
      </a>
      <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | publishDate }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ExternalRecommendation -->
    <ng-container *ngSwitchCase="ArticleCardType.ExternalRecommendation">
      <figure class="article-card-figure">
        <a [href]="articleCard?.url" class="article-card-link article-card-image-link" referrerpolicy="no-referrer" target="_blank">
          <img
            *ngIf="displayedThumbnailUrl"
            [alt]="articleCard?.thumbnail?.alt ? articleCard?.title : ''"
            [src]="displayedThumbnailUrl"
            class="article-card-image"
            loading="lazy"
          />
        </a>

        <figcaption class="article-card-caption">
          <a [href]="articleCard?.url" class="article-card-link" referrerpolicy="no-referrer" target="_blank">
            <h3 class="article-card-title">{{ articleCard?.title }}</h3>
            <span class="article-card-source">{{ articleCard?.category?.name }}</span>
          </a>
        </figcaption>
      </figure>
    </ng-container>

    <!-- STYLE BreakingNewsArticleUp -->
    <ng-container *ngSwitchCase="ArticleCardType.BreakingNewsArticleUp">
      <section>
        <man-breaking-strip [data]="data" [type]="BreakingType.Black"></man-breaking-strip>
        <div class="image-container">
          <a [routerLink]="articleLink">
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="isMobile ? articleCard?.thumbnail?.url : articleCard?.thumbnailUrlHuge || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '20:10' }"
              class="article-card-image"
              loading="lazy"
            />
            <div class="article-card-info-block">
              <div class="article-card-heading">
                <h2 [style.font-size]="fontSizePx" class="article-card-title big">
                  {{ articleCard?.title }}
                </h2>
                <p class="article-card-lead big">
                  {{ articleCard?.lead }}
                </p>
              </div>
            </div>
          </a>
        </div>
        <div class="mobile-box">
          <a [routerLink]="articleLink">
            <div class="heading">
              <h2 [style.font-size]="fontSizePx" class="title">
                {{ articleCard?.title }}
              </h2>
              <p class="lead">
                {{ articleCard?.lead }}
              </p>
            </div>
          </a>
        </div>
      </section>
    </ng-container>

    <!-- STYLE BreakingNewsArticleDown -->
    <ng-container *ngSwitchCase="ArticleCardType.BreakingNewsArticleDown">
      <section>
        <man-breaking-strip [data]="data" [type]="BreakingType.Black"></man-breaking-strip>
        <div class="image-container">
          <a [routerLink]="articleLink">
            <img
              withFocusPoint
              [data]="articleCard?.thumbnailFocusedImages"
              [alt]="articleCard?.thumbnail?.alt || ''"
              [displayedUrl]="isMobile ? articleCard?.thumbnail?.url : articleCard?.thumbnailUrlHuge || 'assets/images/placeholder-16-9.svg'"
              [displayedAspectRatio]="{ desktop: '16:9' }"
              class="article-card-image"
              loading="eager"
            />
            <div class="article-card-info-block">
              <div class="article-card-heading">
                <h2 [style.font-size]="fontSizePx" class="article-card-title big">
                  {{ articleCard?.title }}
                </h2>
                <p class="article-card-lead big">
                  {{ articleCard?.lead }}
                </p>
              </div>
            </div>
          </a>
        </div>
        <div class="mobile-box">
          <a [routerLink]="articleLink">
            <div class="heading">
              <h2 [style.font-size]="fontSizePx" class="title">
                {{ articleCard?.title }}
              </h2>
              <p class="lead">
                {{ articleCard?.lead }}
              </p>
            </div>
          </a>
        </div>
      </section>
    </ng-container>

    <!-- STYLE UpperTitleDateMeta -->
    <ng-container *ngIf="styleID === ArticleCardType.UpperTitleDateMeta">
      <article class="article-card style-UpperTitleLeadDateMeta">
        <a [routerLink]="articleLink" class="article-card-link">
          <h2 [style.font-size]="fontSizePx" class="article-card-title">
            {{ articleCard?.title }}
          </h2>
        </a>
        <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        <div class="article-card-footer">
          <div *ngIf="!hideDate" class="article-card-publish-date">
            {{ articleCard?.publishDate | publishDate }}
          </div>
          <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
        </div>
      </article>
    </ng-container>

    <!-- STYLE ImgRightUpperMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgRightUpperMeta">
      <div class="article-card-left">
        <div class="flex-container">
          <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        </div>
        <a [routerLink]="articleLink" class="article-card-link">
          <div class="article-card-title-container">
            <h2 [style.font-size]="fontSizePx" class="article-card-title">
              {{ articleCard?.title }}
            </h2>
            <div class="article-card-img-sm">
              <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
            </div>
          </div>
        </a>
        <p class="article-card-lead">
          {{ articleCard?.lead || articleCard?.excerpt }}
        </p>
        <div class="article-card-footer">
          <div *ngIf="!hideDate" class="article-card-publish-date">
            {{ publishDate | dfnsFormat: DATETIME_FORMAT }}
          </div>
          <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
        </div>
      </div>
      <div class="article-card-img-lg">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE TitleDateWithoutMeta -->
    <ng-container *ngIf="styleID === ArticleCardType.TitleDateWithoutMeta">
      <div class="article-card-twenty-four">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ articleCard?.publishDate | date: TIME_FORMAT }}
        </div>
        <div class="article-card-divider"></div>
        <a [routerLink]="articleLink" class="article-card-link">
          <h2 [style.font-size]="fontSizePx" class="article-card-title">
            {{ articleCard?.title }}
          </h2>
        </a>
      </div>
    </ng-container>

    <ng-container *ngIf="styleID === ArticleCardType.TitleMeta">
      <ul>
        <li>
          <a [routerLink]="articleLink" class="article-card-link">
            <h2 class="article-card-title">
              {{ articleCard?.title }}
            </h2>
          </a>
        </li>
      </ul>
      <div class="seperator"></div>
    </ng-container>

    <!-- STYLE ImgSixteenNineTitleLeadMeta -->
    <ng-container *ngSwitchCase="ArticleCardType.ImgSixteenNineTitleLeadMeta">
      <a [routerLink]="articleLink" class="article-card-link">
        <div class="article-card-header">
          <h2 [style.font-size]="fontSizePx" class="article-card-title">
            {{ articleCard?.title }}
          </h2>
          <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
          <ng-template #videoThumbnail>
            <figure class="article-card-figure article-card-video">
              <ng-container *ngIf="isLive || articleCard.label?.text">
                <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: true }"></ng-container>
              </ng-container>
              <img
                withFocusPoint
                [data]="articleCard?.thumbnailFocusedImages"
                [alt]="articleCard?.thumbnail?.alt || ''"
                [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                class="article-card-figure-image"
                loading="lazy"
              />
              <i class="icon icon-mandiner-play"></i>
            </figure>
          </ng-template>
          <ng-template #imgThumbnail>
            <figure class="article-card-figure">
              <ng-container *ngIf="isLive || articleCard.label?.text">
                <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: true }"></ng-container>
              </ng-container>
              <img
                withFocusPoint
                [data]="articleCard?.thumbnailFocusedImages"
                [alt]="articleCard?.thumbnail?.alt || ''"
                [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
                [displayedAspectRatio]="{ desktop: '1:1' }"
                class="article-card-figure-image"
                loading="lazy"
              />
            </figure>
          </ng-template>
        </div>
      </a>
      <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
      <p class="article-card-lead">
        {{ articleCard?.lead }}
      </p>
      <div class="article-card-footer">
        <div *ngIf="!hideDate" class="article-card-publish-date">
          {{ publishDate | dfnsFormat: DATETIME_FORMAT }}
        </div>
        <ng-container *ngTemplateOutlet="socialInteractionsTemplate"></ng-container>
      </div>
    </ng-container>

    <!-- STYLE ArticleDetailStyle-->
    <ng-container *ngSwitchCase="ArticleCardType.ArticleDetailStyle">
      <div class="article-card-left">
        <a [routerLink]="articleLink" class="article-card-link">
          <div class="article-card-title-container">
            <h2 [style.font-size]="fontSizePx" class="article-card-title">
              {{ articleCard?.title }}
            </h2>
            <div class="article-card-img-sm">
              <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
            </div>
          </div>
        </a>
        <div class="flex-container">
          <ng-container *ngTemplateOutlet="IndicatorTags"></ng-container>
        </div>

        <ng-container *ngIf="isCategoryVisible">
          <a [routerLink]="categoryLink" class="article-card-category">
            {{ articleCard?.category?.name }}
          </a>
        </ng-container>

        <p class="article-card-lead">
          {{ articleCard?.lead }}
        </p>
      </div>
      <a [routerLink]="articleLink" class="article-card-img-lg">
        <ng-container *ngIf="canHaveVideo && (hasVideo || articleCard?.isVideoType); then videoThumbnail; else imgThumbnail"></ng-container>
      </a>
      <ng-template #videoThumbnail>
        <figure class="article-card-figure article-card-video">
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [displayedAspectRatio]="{ desktop: '16:9', mobile: '1:1' }"
            [alt]="articleCard?.thumbnail?.alt || ''"
            [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
            class="article-card-figure-image"
            loading="lazy"
          />
          <i class="icon icon-mandiner-play"></i>
        </figure>
      </ng-template>
      <ng-template #imgThumbnail>
        <figure class="article-card-figure">
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [displayedAspectRatio]="{ desktop: '16:9', mobile: '1:1' }"
            [alt]="articleCard?.thumbnail?.alt || ''"
            [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-1-1.svg'"
            class="article-card-figure-image"
            loading="lazy"
          />
        </figure>
      </ng-template>
    </ng-container>
  </ng-container>
</article>

<ng-template #IndicatorTags>
  <ng-container *ngIf="getTags() as tags">
    <div
      #tagsTemplate
      *ngIf="tags.totalLength > 0"
      [attr.data-tag-hide-labels]="tags.totalLength > 5"
      [attr.data-tag-total-width]="tags.totalLength"
      class="article-card-tags"
    >
      <div *ngIf="tags.items?.mPlus" [attr.data-tag-width]="tags.items.mPlus" class="icon-mplus">m+</div>
      <div *ngIf="tags.items?.video" [attr.data-tag-width]="tags.items.video" class="article-card-tags-item">
        <i class="icon icon-mandiner-video-play"></i><span class="article-cards-tags-item-label"> Videó</span>
      </div>
      <div *ngIf="tags.items?.gallery" [attr.data-tag-width]="tags.items.gallery" class="article-card-tags-item">
        <i class="icon icon-mandiner-gallery"></i><span class="article-cards-tags-item-label"> Galéria</span>
      </div>
      <div *ngIf="tags.items?.podcast" [attr.data-tag-width]="tags.items.podcast" class="article-card-tags-item">
        <i class="icon icon-mandiner-podcast"></i><span class="article-cards-tags-item-label"> Podcast</span>
      </div>
      <div *ngIf="tags.items?.dossier" [attr.data-tag-width]="tags.items.dossier" class="article-card-tags-item">
        <i class="icon icon-mandiner-folder-open"></i><span class="article-cards-tags-item-label"> Akta</span>
      </div>
      <div *ngIf="tags.items?.minuteToMinute" [attr.data-tag-width]="tags.items.minuteToMinute" class="article-card-tags-item">
        <i class="icon icon-mandiner-minute-to-minute"></i><span class="article-cards-tags-item-label"> Percről percre</span>
      </div>
      <div *ngIf="tags.items?.adultsOnly" [attr.data-tag-width]="tags.items.adultsOnly" class="article-card-tags-item adult-tag">18+</div>
    </div>
  </ng-container>
</ng-template>

<ng-template #videoThumbnail>
  <figure class="article-card-figure article-card-video">
    <ng-container *ngIf="isLive || articleCard?.label?.text">
      <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: isMobile }"></ng-container>
    </ng-container>
    <img
      withFocusPoint
      [data]="articleCard?.thumbnailFocusedImages"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [alt]="articleCard?.thumbnail?.alt || ''"
      [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
      class="article-card-figure-image"
      loading="lazy"
    />
    <i class="icon icon-mandiner-play"></i>
  </figure>
</ng-template>
<ng-template #imgThumbnail>
  <figure class="article-card-figure">
    <ng-container *ngIf="isLive || articleCard?.label?.text">
      <ng-container *ngTemplateOutlet="label; context: { style: 'white', onlyIcon: isMobile }"></ng-container>
    </ng-container>
    <img
      withFocusPoint
      [data]="articleCard?.thumbnailFocusedImages"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [alt]="articleCard?.thumbnail?.alt || ''"
      [displayedUrl]="articleCard?.thumbnail?.url || 'assets/images/placeholder-16-9.svg'"
      class="article-card-figure-image"
      loading="lazy"
    />
  </figure>
</ng-template>

<ng-template #socialInteractionsTemplate let-showDividers>
  <ng-container *ngIf="areSocialInteractionsVisible">
    <mandiner-social-interactions
      (socialInteraction)="onSocialInteraction($event)"
      [articleLink]="articleLink"
      [articleTitle]="articleCard?.title"
      [data]="social"
      [hasExternalUrl]="hasExternalUrl"
      [isMaxWidth]="isMaxWidth"
      [showDividers]="showDividers"
    ></mandiner-social-interactions>
  </ng-container>
</ng-template>

<ng-template #label let-onlyIcon="onlyIcon" let-style="style">
  <!--
  style: 'white' | 'gold'
  -->
  <ng-container *ngTemplateOutlet="articleCard?.label?.url?.length === 0 ? labelInternalHref : labelExternalHref; context: { style, onlyIcon }"></ng-container>
</ng-template>
<ng-template #labelInner let-onlyIcon="onlyIcon" let-style="style">
  <img
    *ngIf="isLive"
    [src]="style === 'gold' ? '/assets/images/icons/live-gold.svg' : '/assets/images/icons/live.svg'"
    alt="Élő közvetítés"
    class="article-card-label-icon"
    height="18"
    width="24"
  />
  {{ isLive && !onlyIcon && (!articleCard?.label?.text || articleCard?.label?.text?.length === 0) ? 'Élő közvetítés' : articleCard?.label?.text }}
</ng-template>
<ng-template #labelInternalHref let-onlyIcon="onlyIcon" let-style="style">
  <a [routerLink]="articleLink" class="article-card-label">
    <ng-container *ngTemplateOutlet="labelInner; context: { style, onlyIcon }"></ng-container>
  </a>
</ng-template>

<ng-template #labelExternalHref let-onlyIcon="onlyIcon" let-style="style">
  <a [href]="articleCard?.label?.url" class="article-card-label">
    <ng-container *ngTemplateOutlet="labelInner; context: { style, onlyIcon }"></ng-container>
  </a>
</ng-template>
