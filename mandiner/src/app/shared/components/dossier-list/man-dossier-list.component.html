<ng-container *ngIf="cacheData">
  <ng-container *ngFor="let dossier of cacheData; trackBy: trackByFn">
    <a class="dossier-tag" *ngIf="dossier?.title" [routerLink]="['/', 'dosszie', dossier?.slug]" (click)="onClickDossier()">
      <div class="dossier-tag-akta-text">AKTA</div>
      <span class="dossier-tag-text">
        <span class="dossier-tag-flex-wrapper">
          <img class="dossier-tag-thumbnail" *ngIf="dossier?.coverImgFullSizeUrl" [src]="dossier?.coverImgFullSizeUrl" loading="lazy" />
          {{ dossier?.title }}
        </span>
        <i class="icon icon-mandiner-chevron-up"></i>
      </span>
    </a>
  </ng-container>
</ng-container>
