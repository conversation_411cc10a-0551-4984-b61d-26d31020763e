import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, share, throwError, withLatestFrom } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { ArticleSearchResult, SearchPageResolverData } from './tags-page.definitions';
import { TagsPageService } from './tags-page.service';
import { RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { ApiResponseMetaList, ApiResult, Tag } from '../../shared';

@Injectable({
  providedIn: 'root',
})
export class TagsPageResolver {
  constructor(
    private readonly tagsPage: TagsPageService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<SearchPageResolverData> {
    const tagSlug = route.params['tag'];
    const maxResultsPerPage = 10;
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const tagObservable$ = this.tagsPage.getTag(tagSlug).pipe(share());
    return tagObservable$.pipe(
      mergeMap((tag) => {
        const slug = tag?.data?.slug;
        if (!slug) {
          return throwError(null);
        }
        return this.tagsPage.searchArticleByTags([slug], currentPage, maxResultsPerPage);
      }),
      withLatestFrom(tagObservable$),
      map(([articlesResponse, tagResponse]) => {
        if (this.redirectService.shouldBeRedirect(route.queryParams['page'], articlesResponse.data)) {
          this.redirectService.redirectOldUrl(`cimke/${tagSlug}`, false, 302);
        }
        return this.composeSearchResolverData(articlesResponse, '', tagResponse, currentPage);
      }),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(err);
      })
    );
  }

  private composeSearchResolverData(
    articlesResponse: ApiResult<ArticleSearchResult[], ApiResponseMetaList>,
    globalFilter: string,
    tagResponse: ApiResult<Tag> | null,
    currentPage: number
  ): SearchPageResolverData {
    return {
      currentPage: currentPage,
      globalFilter: globalFilter,
      tag: tagResponse?.data,
      articles: articlesResponse?.data,
      meta: articlesResponse?.meta,
      articlesAsArticleCards: articlesResponse?.data?.map((sr) => searchResultToArticleCard(sr as any)),
    } as SearchPageResolverData;
  }
}
