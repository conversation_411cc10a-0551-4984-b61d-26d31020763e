@use 'shared' as *;

.static-page {
  margin-bottom: 40px;
}

.text-formatter {
  h2 {
    font-size: 48px;
    font-family: var(--kui-font-primary);
    margin: 0 0 10px 0;

    span {
      padding: 0 3px 0 0;
    }
  }

  h3 {
    font-size: 30px;
    font-family: var(--kui-font-primary);
    margin: 0 0 10px 0;

    span {
      padding: 0 3px 0 0;
    }
  }

  h4 {
    font-size: 22px;
    font-family: var(--kui-font-primary);
    margin: 0 0 10px 0;

    span {
      padding: 0 3px 0 0;
    }
  }

  p,
  li,
  td {
    font-weight: 400;
    font-style: normal;
    line-height: 24px;
    text-align: left;
    margin: 1.2rem 0;

    span {
      padding: 0 2px 0 0;
    }

    a {
      color: var(--kui-red);
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }

    .custom-text-style.quote {
      display: block;
      font-size: 48px;
      line-height: 52px;
      padding: 15px 0 15px 30px;
      border-left: 10px solid var(--kui-red);

      &::after {
        content: '\201D';
      }

      &::before {
        content: '\201E';
      }
    }
  }

  ul {
    list-style-position: inside;
    list-style-type: disc;
  }

  ol {
    list-style: decimal;
  }

  ul,
  ol {
    padding-left: 18px;
    margin: 1.2rem 0;

    li {
      padding-left: 0;
      display: list-item;
      margin: 0.2rem 0;
    }
  }

  table {
    margin: auto;

    td {
      padding: 12px 15px;
      border: 1px solid #c4c4c4;
      color: #586771;
    }
  }

  .article-text-table {
    display: block;
    overflow: auto;
    margin: 35px 0;

    table {
      width: calc(100% - 2px);

      tr {
        &:first-child {
          td {
            border: 0;
          }
        }

        td {
          padding: 12px 15px;
          border: 1px solid #c4c4c4;
          color: #586771;
        }
      }
    }
  }

  .image {
    display: block;
    max-width: 100%;

    img {
      width: 100%;
      height: auto;
    }

    figcaption {
      text-align: center;
      opacity: 0.5;
      font-style: italic;
      padding-top: 5px;
      font-size: 13px;
    }
  }

  p {
    font-size: 18px;
    line-height: 32px;
    font-weight: 400;
    margin: 0.7rem 0;

    @include media-breakpoint-down(md) {
      margin: 26px 0;
    }
  }

  .quote {
    margin: 40px 0;

    p {
      color: var(--kui-red);
      font-family: var(--kui-font-primary);
      font-style: italic;
      letter-spacing: normal;
      font-size: 26px !important;
      line-height: 44px;

      &:before {
        content: '\201E';
      }

      &:after {
        content: '\201D';
      }
    }

    @include media-breakpoint-down(md) {
      margin: 26px 0;
    }
  }

  .highlight {
    margin: 40px 0;
    display: block;
    position: relative;
    margin-block-start: 1em;
    margin-block-end: 1em;
    border-left: 8px solid var(--kui-red);
    font-size: 48px;
    line-height: 52px;
    padding: 15px 0 15px 30px;
    color: var(--kui-white);
    background-color: var(--kui-red);

    @include media-breakpoint-down(md) {
      margin-top: 26px;
      margin-bottom: 26px;
    }

    p {
      font-family: var(--kui-font-primary);
      position: relative;
      display: inline;
      width: auto;
      color: var(--kui-white);
      white-space: pre-wrap;

      border-bottom: 0 solid var(--kui-black);
      border-top: 0 solid var(--kui-black);
      border-width: 0.2em 0;
      background-color: var(--kui-blue-dark);
      padding-left: 4px;
      padding-right: 4px;
      font-size: 24px;
      font-weight: 400;
      line-height: 36px;
    }
  }

  .border-text {
    display: block;
    margin: 40px 0;
    padding: 40px;
    border-radius: 3px;
    border: 1px solid var(--kui-black);
    background-color: var(--kui-white);
    @include media-breakpoint-down(md) {
      margin: 26px 0;
    }

    h2 {
      font-size: 25px;
      font-weight: 400;
      font-family: var(--kui-font-primary);
      margin-bottom: 25px;
    }

    p {
      font-size: 16px;
      font-weight: 300;
      line-height: 26px;
    }
  }

  .raw-html-embed {
    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;

    > * {
      margin: 0 auto;
    }
  }
}
