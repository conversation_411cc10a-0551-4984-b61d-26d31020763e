import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { StaticPageService } from './static-page.service';
import { catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class StaticPageResolver {
  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly router: Router
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const param = route.params['slug'];
    const previewHash = route.queryParams['previewHash'];

    const request$ = previewHash ? this.staticPageService.getStaticPagePreview(param, previewHash) : this.staticPageService.getStaticPage(param);

    return request$.pipe(
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
