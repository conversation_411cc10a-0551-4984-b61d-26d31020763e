import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RadioPlayerButtonComponent } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'metropol-retro-radio-player-button',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/radio-player-button/radio-player-button.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/radio-player-button/radio-player-button.component.scss',
    './metropol-retro-radio-player-button.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe],
})
export class MetropolRetroRadioPlayerButtonComponent extends RadioPlayerButtonComponent {
  constructor() {
    super();
    ((this.title = 'RETRO RÁDIÓ'), (this.logo = '/assets/images/retro-radio-logo.png'), (this.streamUrl = 'https://icast.connectmedia.hu/5001/live.mp3'));
  }
}
