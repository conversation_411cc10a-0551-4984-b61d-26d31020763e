<nav class="hamburger-menu">
  <button (click)="toggleMenu(); $event.stopPropagation()" *ngIf="(isMenuOpen | async) === false; else closeIcon" class="hamburger-menu-button">&#9776;</button>
  <ng-template #closeIcon>
    <button (click)="toggleMenu(); $event.stopPropagation()">
      <i class="icon icon-close"></i>
    </button>
  </ng-template>

  <ul (clickOutside)="toggleMenu()" *ngIf="isMenuOpen | async" class="hamburger-menu-list">
    <ng-container *ngFor="let item of mainMenu; let index = index">
      <li class="hamburger-menu-item">
        <ng-container *ngTemplateOutlet="!item.isCustomUrl ? normalLink : customUrl; context: { item, index }"></ng-container>
        <ng-container *ngIf="item?.children?.length">
          <div *ngIf="activeDropdownIndex === index" class="dropdown-menu">
            <ng-container *ngFor="let children of item?.children">
              <ng-container *ngTemplateOutlet="!item.isCustomUrl ? normalLink : customUrl; context: { item: children }"></ng-container>
            </ng-container>
          </div>
        </ng-container>
      </li>
    </ng-container>
    <div *ngIf="radioOutlet" class="header-right-retro-player">
      <ng-container [ngTemplateOutlet]="radioOutlet"></ng-container>
    </div>
  </ul>
</nav>
<ng-template #normalLink let-index="index" let-item="item">
  @if (item.relatedType === RelatedType.COLUMN && item?.related?.sponsorship?.logo; as logo) {
    <a
      (click)="toggleMenu()"
      *ngIf="!item?.children?.length; else dropdown"
      [routerLink]="item.link"
      [target]="item.target"
      class="hamburger-menu-link with-logo"
    >
      <img [src]="logo" loading="lazy" />
      {{ item.title }}
    </a>
  } @else {
    <a (click)="toggleMenu()" *ngIf="!item?.children?.length; else dropdown" [routerLink]="item.link" [target]="item.target" class="hamburger-menu-link">
      {{ item.title }}
    </a>
  }

  <ng-template #dropdown>
    <ng-container *ngTemplateOutlet="dropdownLink; context: { item, index }"></ng-container>
  </ng-template>
</ng-template>
<ng-template #customUrl let-index="index" let-item="item">
  <a (click)="toggleMenu()" *ngIf="!item?.children?.length; else dropdown" [href]="item.link" [target]="item.target" class="hamburger-menu-link">
    {{ item.title }}
  </a>
  <ng-template #dropdown>
    <ng-container *ngTemplateOutlet="dropdownLink; context: { item, index }"></ng-container>
  </ng-template>
</ng-template>

<ng-template #dropdownLink let-index="index" let-item="item">
  <a (click)="activeDropdownIndex === index ? (activeDropdownIndex = -1) : (activeDropdownIndex = index)" class="hamburger-menu-link dropdown-link">
    <div [class.active]="activeDropdownIndex === index" class="with-icon">
      {{ item.title }}
      <i class="icon icon-dropdown"></i>
    </div>
  </a>
</ng-template>
