import { AsyncPipe, DOCUMENT, NgClass, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  BreakingBlock,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  InitResolverData,
  loadScript,
  MediaworksFooterCompactComponent,
  PortfolioItem,
  PortfolioResponse,
  SimplifiedMenuItem,
  TECHNICAL_ADS,
} from '@trendency/kesma-ui';
import { combineLatest, Observable, of, Subject, take } from 'rxjs';
import { delay, filter, map, mergeMap, startWith } from 'rxjs/operators';
import { ApiService, ElectionsService, ShowHeaderHandlerService } from '../../services';
import { RetroRadioWrapperComponent } from '../retro-radio-wrapper/retro-radio-wrapper.component';
import { MetropolFooterComponent } from '../metropol-footer/metropol-footer.component';
import { MetropolHeaderComponent } from '../metropol-header/metropol-header.component';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    ElectionsBoxComponent,
    AdvertisementAdoceanComponent,
    RetroRadioWrapperComponent,
    NgClass,
    RouterOutlet,
    MediaworksFooterCompactComponent,
    AsyncPipe,
    MetropolFooterComponent,
    MetropolHeaderComponent,
  ],
})
export class BaseComponent implements OnInit, OnDestroy, AfterViewInit {
  mainMenu: SimplifiedMenuItem[] = [];
  topMenu: SimplifiedMenuItem[] = [];
  footer0: SimplifiedMenuItem[] = [];
  footer1: SimplifiedMenuItem[] = [];

  headerAdverts?: AdvertisementsByMedium;
  breakingNews?: BreakingBlock;
  isArticleUrl: boolean;
  url: string;
  categorySlug: string;
  $showHeader = this.showHeaderHandlerService.$showHeader;

  isAdblockerActive: boolean;

  mediaworksFooter: PortfolioItem[];

  ElectionsBoxStyle = ElectionsBoxStyle;

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );
  facebookLink = 'https://www.facebook.com/Metropol.Napilap';
  instagramLink = 'https://www.instagram.com/metropol.napilap/';
  tiktokLink = 'https://www.tiktok.com/@metropol.napilap';
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly apiService: ApiService,
    private readonly showHeaderHandlerService: ShowHeaderHandlerService,
    public readonly electionsService: ElectionsService,
    @Inject(DOCUMENT) private readonly documentElement: Document
  ) {}

  ngOnInit(): void {
    // Reload Google Adsense script on every route change
    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe((): void => {
      loadScript('https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8559195417632426');
    });

    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`
    combineLatest([
      this.adStoreAdo.isAdult,
      this.adStoreAdo.isArticleLoaded$,
      this.adStoreAdo.articleParentCategory$,
      this.router.events.pipe(
        filter((event) => event instanceof NavigationEnd),
        startWith(this.router)
      ),
    ])
      .pipe(
        mergeMap(() => {
          if (!this.utils.isBrowser() || !this.documentElement?.location) {
            return of({
              header: {} as AdvertisementsByMedium,
              footer: {} as AdvertisementsByMedium,
            });
          }

          const [_, path1, path2] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];

          const parentCategory = this.adStoreAdo.articleParentCategory$.getValue();

          this.categorySlug = parentCategory || `column_${path2}`;

          this.isArticleUrl = !isNaN(parseInt(path2, 10));

          this.resetAds();

          if (this.isArticleUrl) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.isArticleUrl && !this.adStoreAdo.isArticleLoaded$.getValue()
            ? of(null)
            : this.adStoreAdo.advertisemenets$.pipe(
                // We need delay to reinitialize the header adverts, otherwise this.resetAds function will not have any effect.
                delay(0),
                map<Advertisement[], AdvertisementsByMedium>((ads) => {
                  const headerMediumSeparator = this.baseElementPageTypeSwitch(path1);

                  return this.adStoreAdo.separateAdsByMedium(ads, headerMediumSeparator.page, headerMediumSeparator.ads);
                })
              );
        })
      )
      .subscribe((adverts) => {
        if (!adverts) {
          return;
        }

        this.headerAdverts = adverts as AdvertisementsByMedium;

        this.url = this.documentElement?.location?.pathname;

        if (this.route.snapshot.firstChild?.data?.['isLeaderboardAdHidden']) {
          this.headerAdverts = undefined;
        }

        this.changeRef.detectChanges();
      });

    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter = data.data;
      });

    const responseData: InitResolverData = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews = responseData?.init?.breakingNews;

    const {
      menu: { header_0, header_1, footer_0, footer_1 },
    } = responseData || {};
    this.mainMenu = header_0 ?? [];
    this.topMenu = header_1 ?? [];
    this.footer0 = footer_0 ?? [];
    this.footer1 = footer_1 ?? [];

    this.changeRef.detectChanges();
    this.enableStickyLayoutDebug();
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  private baseElementPageTypeSwitch(path: string): { page: string; ads: AdvertisementBannerName[] } {
    const footerAds: AdvertisementBannerName[] = ['leaderboard_footer', 'mobilrectangle_footer'];
    const banners: AdvertisementBannerName[] = ['leaderboard_1', 'layer', 'interstitial', 'leaderboard_2', 'szponzorcsik', ...TECHNICAL_ADS, ...footerAds];

    switch (path) {
      case '':
        return {
          page: 'main_page',
          ads: banners,
        };
      case 'rovat':
        return {
          page: this.categorySlug,
          ads: banners,
        };
      default:
        return {
          page: this.categorySlug,
          ads: ['leaderboard_1', 'layer', 'interstitial', 'szponzorcsik', ...TECHNICAL_ADS, ...footerAds],
        };
    }
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return (this.isAdblockerActive = typeof (<any>window).ado !== 'object');
  }

  private resetAds(): void {
    this.headerAdverts = undefined;

    this.changeRef.detectChanges();
  }

  /**
   * Sets a CSS background color for the sticky elements. This could be useful for debugging.
   */
  private enableStickyLayoutDebug(): void {
    if (this.utils.isBrowser() && this.route.snapshot.queryParams['stickyBg'] === '1') {
      (this.documentElement.querySelector(':root') as any)['style'].setProperty('--kui-sticky-bg', 'lightblue');
    }
  }
}
