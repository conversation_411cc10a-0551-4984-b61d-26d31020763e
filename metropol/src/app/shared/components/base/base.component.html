<metropol-header
  *ngIf="$showHeader | async"
  [facebookLink]="facebookLink"
  [instagramLink]="instagramLink"
  [mainMenu]="mainMenu"
  [radioOutlet]="retroRadio"
  [tiktokLink]="tiktokLink"
  [topMenu]="topMenu"
  class="metropol-header"
>
</metropol-header>

<ng-container *ngIf="electionsService.isElections2024Enabled()">
  <section>
    <div class="wrapper elections-box-wrapper">
      <kesma-elections-box [link]="electionsService.getElections2024Link()" [styleID]="ElectionsBoxStyle.HEADER"></kesma-elections-box>
    </div>
  </section>
</ng-container>

<kesma-advertisement-adocean *ngIf="headerAdverts?.mobile?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.mobile?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.mobile?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="headerAdverts?.mobile?.layer as ad" [ad]="ad" [hasNoParentHeight]="true" class="with-margin"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.desktop?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="headerAdverts?.mobile?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.desktop?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="headerAdverts?.desktop?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.desktop?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.desktop?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean
  *ngIf="!isAdblockerActive && headerAdverts?.desktop?.leaderboard_1 as ad"
  [ad]="ad"
  [style]="{
    margin: '10px',
  }"
></kesma-advertisement-adocean>

<kesma-advertisement-adocean
  [style]="{
    margin: '30px 0px',
  }"
  *ngIf="headerAdverts?.mobile?.szponzorcsik as ad"
  [ad]="ad"
  [hasNoParentHeight]="true"
></kesma-advertisement-adocean>
<kesma-advertisement-adocean
  [style]="{
    margin: '30px 0px',
  }"
  *ngIf="headerAdverts?.desktop?.szponzorcsik as ad"
  [ad]="ad"
  [hasNoParentHeight]="true"
></kesma-advertisement-adocean>

<ng-template #retroRadio>
  <app-retro-radio-wrapper></app-retro-radio-wrapper>
</ng-template>

<div [ngClass]="{ 'content-wrap-full-width': (isFullWidth$ | async) === true }" class="content-wrap">
  <router-outlet></router-outlet>
</div>

<kesma-advertisement-adocean *ngIf="headerAdverts?.desktop?.leaderboard_footer as ad" [ad]="ad" [hasNoParentHeight]="true"> </kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="headerAdverts?.mobile?.mobilrectangle_footer as ad" [ad]="ad" [hasNoParentHeight]="true"> </kesma-advertisement-adocean>

<metropol-footer
  (openCookieSettings)="openCookieSettings()"
  [bottomMenu]="footer1"
  [facebookLink]="facebookLink"
  [instagramLink]="instagramLink"
  [tiktokLink]="tiktokLink"
  [topMenu]="footer0"
>
</metropol-footer>

<kesma-mediaworks-footer-compact [data]="mediaworksFooter"></kesma-mediaworks-footer-compact>
