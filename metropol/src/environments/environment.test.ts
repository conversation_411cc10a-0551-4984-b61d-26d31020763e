// UAT teszt környezet
import { MetropolEnvironment } from './environment.definition';

export const environment: MetropolEnvironment = {
  production: true,
  type: 'beta',
  apiUrl: 'http://metropolfe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  ingatlanbazarApiUrl: {
    serverApiUrl: 'http://localhost:30059/ingatlanbazar-api',
    clientApiUrl: 'https://www.ingatlanbazar.hu/api',
  },
  facebookAppId: '',
  siteUrl: 'http://metropolfe.apptest.content.private',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-NKDRW3C',
  gemiusId: '151LyWesPGXJvmoh.BNXF.UITBQFSjhplmgVri64b07.m7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
  ssrProxyConfig: [
    {
      path: '/ingatlanbazar-api',
      target: 'https://www.ingatlanbazar.hu/api',
    },
  ],
};
