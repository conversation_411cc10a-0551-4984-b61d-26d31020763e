import { MetropolEnvironment } from './environment.definition';

// <PERSON><PERSON>
export const environment: MetropolEnvironment = {
  production: true,
  type: 'prod',
  apiUrl: {
    clientApiUrl: 'https://api.metropol.hu/publicapi/hu',
    serverApiUrl: 'http://metropolfeapi.app.content.private/publicapi/hu',
  },
  ingatlanbazarApiUrl: {
    serverApiUrl: 'http://localhost:30059/ingatlanbazar-api',
    clientApiUrl: 'https://www.ingatlanbazar.hu/api',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '1534911313439123',
  siteUrl: 'https://metropol.hu',
  googleSiteKey: '6LfBpjIjAAAAAObb1seSke4hO-LXIa_kupMTS4NM',
  googleTagManager: 'GTM-NKDRW3C',
  gemiusId: '151LyWesPGXJvmoh.BNXF.UITBQFSjhplmgVri64b07.m7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
  ssrProxyConfig: [
    {
      path: '/ingatlanbazar-api',
      target: 'https://www.ingatlanbazar.hu/api',
    },
  ],
};
