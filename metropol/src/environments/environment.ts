// Lok<PERSON>lis fejlesztői környezet
import { MetropolEnvironment } from './environment.definition';

export const environment: MetropolEnvironment = {
  production: false,
  type: 'local',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu', // for proxy: '/publicapi/hu' then: npm run start-with-proxy
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  ingatlanbazarApiUrl: 'https://www.ingatlanbazar.hu/api',
  facebookAppId: '1534911313439123',
  siteUrl: 'http://localhost:4200',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-NKDRW3C',
  gemiusId: '151LyWesPGXJvmoh.BNXF.UITBQFSjhplmgVri64b07.m7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
