import { MetropolEnvironment } from './environment.definition';

// Deves k<PERSON>t
export const environment: MetropolEnvironment = {
  production: false,
  type: 'dev',
  apiUrl: 'https://kozponti-varnish-publicapi.dev.trendency.hu/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  ingatlanbazarApiUrl: 'https://www.ingatlanbazar.hu/api',
  facebookAppId: '',
  siteUrl: 'https://metropol.dev.trendency.hu',
  withHttps: false,
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-NKDRW3C',
  gemiusId: '151LyWesPGXJvmoh.BNXF.UITBQFSjhplmgVri64b07.m7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: 'https://<EMAIL>/50',
    tracingOrigins: ['https://metropol.dev.trendency.hu/', 'http://localhost:4200', 'localhost'],
    sampleRate: 0.1,
  },
};
